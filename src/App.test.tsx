import { render } from './test-utils'
import { App } from './modules/app'

describe('App Component', () => {
  test('renders app without crashing', () => {
    render(<App />)

    expect(document.body).toBeInTheDocument()
  })

  test('renders with Redux store', () => {
    const { container } = render(<App />)
    // Check that the app container exists
    expect(container).toBeInTheDocument()
  })

  test('handles routing without errors', () => {
    // This test ensures the router is properly configured
    expect(() => render(<App />)).not.toThrow()
  })
})
