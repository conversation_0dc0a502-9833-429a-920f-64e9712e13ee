import axios from 'axios'
import { getConfig } from '../../config'
import { networkErrorPath } from '../paths'
import { StorageKey } from '../../shared/helpers/constants'
import { refreshAccessToken } from './auth'
import { isSuccess } from '../../shared/helpers/util'

const token: any = localStorage.getItem('token')
if (token) {
  axios.defaults.headers.common['Authorization'] = 'Bearer ' + JSON.parse(token)
}

export const AxiosInstance = axios.create({
  baseURL: getConfig()?.apiUrl,
  headers: {
    'Content-Type': 'application/json',
  },
})
let refreshPromise: any = null

AxiosInstance.interceptors.response.use(
  function (response) {
    return response
  },
  async (error) => {
    const originalRequest = error.config
    const code = error?.response?.status

    console.log({ error })

    if (code === 401) {
      const refreshToken: string | null = localStorage.getItem('refreshtoken')

      if (!refreshToken) {
        console.warn('No refresh token found, logging out...')
        localStorage.clear()
        setTimeout(() => {
          window.location.href = '/signin'
        }, 1000)
        return Promise.reject(error)
      }

      // Prevent multiple refresh requests at the same time
      if (originalRequest._retry || originalRequest.url.includes('/auth/refresh-token')) {
        console.warn('Refresh already in progress or failed, logging out...')
        localStorage.clear()
        setTimeout(() => {
          window.location.href = '/signin'
        }, 1000)
        return Promise.reject(error)
      }

      originalRequest._retry = true

      // If a refresh request is already in progress, wait for it
      if (!refreshPromise) {
        refreshPromise = refreshAccessToken(refreshToken)
          .then((resp) => {
            if (resp?.status === 401 || resp?.data?.statusCode === 401) {
              console.warn('Refresh token expired or invalid, logging out...')
              localStorage.clear()
              setTimeout(() => {
                window.location.href = '/signin'
              }, 1000)
              return Promise.reject(error)
            }

            if (isSuccess(resp)) {
              const payload = resp?.data?.data
              const newAccessToken = payload?.access_token
              window.localStorage.setItem('token', JSON.stringify(newAccessToken))
              window.localStorage.setItem('tokenExpireAt', JSON.stringify(payload?.user?.exp))
              window.localStorage.setItem('refreshtoken', JSON.stringify(payload?.refresh_token))
              return newAccessToken
            }

            throw new Error('Failed to refresh token')
          })
          .catch((refreshError) => {
            console.error('Refresh token request failed:', refreshError)
            localStorage.clear()
            setTimeout(() => {
              window.location.href = '/signin'
            }, 1000)
            return Promise.reject(refreshError)
          })
          .finally(() => {
            refreshPromise = null // Reset the refresh process after completion
          })
      }

      try {
        const newAccessToken = await refreshPromise
        if (newAccessToken) {
          originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`

          return AxiosInstance(originalRequest) // Retry the original request
        }
      } catch (refreshError) {
        console.error('Refresh token request failed:', refreshError)
        localStorage.clear()
        setTimeout(() => {
          window.location.href = '/signin'
        }, 1000)
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)
