import { AxiosInstance } from '.'

interface I_CreateDailyLog {
  crewId: string
  date: string
  worked?: string
  highTemp?: number
  lowTemp?: number
  weather?: string
  rainTime?: string
  projects: any
  createdBy: string
  auditLog: any
}

interface I_UpdateDailyLog {
  dailyLogId: string | undefined

  crewId: string
  date: string
  worked?: string
  highTemp?: number
  lowTemp?: number
  maxwind_mph?: number
  totalprecip_in?: number
  weather?: string
  rainTime?: string
  projects: any
  createdBy: string
  auditLog: any
}

export const createDailyLog = async (data: I_CreateDailyLog) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/daily-log/create-daily-log`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('createDailyLog error', error)
    return error?.response
  }
}

export const updateDailyLog = async (data: I_UpdateDailyLog) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/daily-log/update-daily-log`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('updateDailyLog error', error)
    return error?.response
  }
}

export const getDailyLog = async (crewId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/daily-log/get-daily-log/crew/${crewId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getDailyLog error', error)
    return error?.response
  }
}
export const getDailyLogById = async (dailyLog: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/daily-log/daily-log-by-id/dailylog/${dailyLog}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getDailyLog error', error)
    return error?.response
  }
}

export const getDailyLogPO = async (showAll?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/daily-log/po-list`, {
      params: {
        showAll,
      },
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getDailyLog error', error)
    return error?.response
  }
}

export const getOpportunityWork = async (oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/daily-log/opportunity-work/oppId/${oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getDailyLog error', error)
    return error?.response
  }
}

export const getDailyLogProjectValue = async (oppId: string, projectType: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/daily-log/project-data/oppId/${oppId}/projectType/${projectType}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getDailyLog error', error)
    return error?.response
  }
}
