import { AxiosInstance } from '.'
import { filterUndefinedAndNull, notify, simplifyBackendError } from '../../shared/helpers/util'

interface I_AcceptRejectInvitation {
  senderEmail: string
  recipientEmail: string
  company: string
  status: number
}

interface I_Signup {
  firstName: string
  lastName: string
  // username: string
  email: string
  password: string
  confirmPassword: string
  hireDate?: string
  preferredName?: string
}
interface I_Signin {
  email: string
  password: string
}
interface I_ForgotPassword {
  email: string
}
interface I_ResetPassword {
  token: string
  password: string
}

export const signup = async ({
  signupDto,
  invitationResponseDto,
}: {
  signupDto: I_Signup
  invitationResponseDto: I_AcceptRejectInvitation
}) => {
  try {
    // const payload = filterUndefinedAndNull(data, true, true)
    const response = await AxiosInstance.post('/auth/signup-member', {
      signupDto,
      invitationResponseDto,
    })
    return response
  } catch (error: any) {
    console.error('signup error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const signin = async (data: I_Signin) => {
  try {
    const response = await AxiosInstance.post('/auth/login', data)

    return response
  } catch (error: any) {
    console.error('signin error', error)
    return error?.response
  }
}

export const refreshAccessToken = async (refreshToken: string | null) => {
  try {
    const response = await AxiosInstance.post(`/auth/refresh-token/${JSON.parse(refreshToken)}`)
    // Standardize: always return the full Axios response so callers can access response.data.data
    return response
  } catch (error: any) {
    console.error('refresh token error', error)
    return error?.response
  }
}
export const authenticate = (token: string, refreshtoken: string, tokenExpireAt: string, id?: string) => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', JSON.stringify(token))
      localStorage.setItem('refreshtoken', JSON.stringify(refreshtoken))
      localStorage.setItem('tokenExpireAt', JSON.stringify(tokenExpireAt))
      localStorage.setItem('id', JSON.stringify(id))
    }
  } catch (error) {
    console.error('authenticate error', error)
  }
}

export const isAuthenticated = () => {
  if (typeof window == 'undefined') {
    return false
  }
  if (localStorage.getItem('token')) {
    return true
  } else {
    return false
  }
}

export const forgotPassword = async (email: I_ForgotPassword) => {
  try {
    const response = await AxiosInstance.post('/auth/forgot-password', email)

    return response
  } catch (error: any) {
    console.error('forgotPassword error', error)
    return error?.response
  }
}

export const resetPassword = async (data: I_ResetPassword) => {
  try {
    const response = await AxiosInstance.post('/auth/reset-password', data)

    return response
  } catch (error: any) {
    console.error('resetPassword error', error)
    return error?.response
  }
}

export const signout = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get('/auth/logout', {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
      localStorage.removeItem('id')
      localStorage.removeItem('refreshtoken')
      localStorage.removeItem('tokenExpireAt')
      localStorage.removeItem('currentCompany')
      localStorage.removeItem('isInvited')
      localStorage.removeItem('position')
      localStorage.removeItem('currentDate')
    }
    return response
  } catch (error: any) {
    console.error('signout error', error)
    return error?.response
  }
}
