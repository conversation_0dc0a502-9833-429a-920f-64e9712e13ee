import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { createStore } from 'redux'
import { reducers } from './logic/redux/reducers'

// Create a test store
const createTestStore = (initialState = {}) => {
  return createStore(reducers, initialState)
}

// Custom render function that includes providers
const customRender = (
  ui: ReactElement,
  {
    initialState = {},
    store = createTestStore(initialState),
    ...renderOptions
  }: {
    initialState?: any
    store?: any
  } & Omit<RenderOptions, 'wrapper'> = {}
) => {
  function Wrapper({ children }: { children?: React.ReactNode }) {
    return <Provider store={store}>{children}</Provider>
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Re-export everything
export * from '@testing-library/react'

// Override render method
export { customRender as render }
