import { ErrorMessage } from 'formik'
import React, { useEffect, useRef, useState } from 'react'

import RightArrowIcon from '../../assets/icons/RightArrowIcon'
import { getNameFrom_Id } from '../helpers/util'
import { useClickOutside } from '../hooks/useClickOutside'
import { NormalInput } from '../normalInput/NormalInput'
import * as Styled from './style'
import { SearchLoader } from '../components/loader/style'
import { getLeadSrcDropdownName } from '../../modules/leadSource/LeadSource'
import { UpArrowIcon } from '../../assets/icons/UpArrowIcon'
import { DownArrowIcon } from '../../assets/icons/DownArrowIcon'

interface I_AutoCompleteProps {
  addNewText?: string
  apiSearch?: boolean
  autoFillData?: any
  className?: string
  disabled?: boolean
  error?: boolean
  labelName: string
  onAddClick?: any
  onChange?: any
  options: Array<any>
  setFieldValue?: any
  setSearchTerm?: any
  setValueOnClick?: any
  showAddOption?: boolean
  stateName: string
  value?: string
  setTypeForAction?: any
  setDuration?: any
  dropdownHeight?: string
  borderRadius?: string
  selectedValue?: string
  validate?: boolean
  preSelected?: boolean
  setClientAddress?: React.Dispatch<React.SetStateAction<string>>
  searchLoader?: boolean
  isCrewProjectReport?: boolean
  isWarranty?: boolean
}

const AutoCompleteWithId: React.FC<I_AutoCompleteProps> = (props) => {
  const {
    addNewText,
    apiSearch,
    autoFillData,
    className,
    disabled,
    error,
    labelName,
    onAddClick,
    onChange,
    options,
    setFieldValue,
    setSearchTerm,
    setValueOnClick,
    showAddOption,
    stateName,
    value,
    setTypeForAction,
    setDuration,
    dropdownHeight,
    borderRadius,
    selectedValue,
    validate,
    setClientAddress,
    searchLoader,
    preSelected,
    isCrewProjectReport,
    isWarranty,
  } = props

  const [filterOptions, setFilterOptions] = useState(() => options)
  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const [onFocused, setOnFocused] = useState<boolean>(false)
  const [localSearchTerm, setLocalSearchTerm] = useState('')

  const ref = useRef(null)
  const ref1 = useRef(null)

  useClickOutside(ref, setShowDropdown)
  useClickOutside(ref1, setOnFocused)

  useEffect(() => {
    setFilterOptions(options)
  }, [options])

  useEffect(() => {
    if (preSelected && !localSearchTerm) setFilterOptions(options)
  }, [options, localSearchTerm])

  const handleFilterOptions = (str: string) => {
    if (!str) return setFilterOptions(options)
    setFilterOptions(options.filter((item: any) => item.label.toLowerCase().includes(str.toLowerCase())))
  }

  const handleSelectOption = (selected: any) => {
    // Show label in the input, but keep id in background fields
    setFieldValue(stateName, selected.label)
    onChange && onChange(selected.label)
    setShowDropdown(false)
    setOnFocused(false)
    if (autoFillData) {
      handleDataChange(selected)
    }
    if (setValueOnClick) setValueOnClick(selected)
    if (preSelected) setLocalSearchTerm('')
  }

  const handleInputChange = (str: string) => {
    setShowDropdown(true)
    setFieldValue && setFieldValue(stateName, str)
    onChange && onChange(str)
    handleFilterOptions(str)
    if (setSearchTerm) setSearchTerm(str)
    if (str && preSelected) setLocalSearchTerm(str)
    if (setTypeForAction) setTypeForAction(str)
  }

  const handleDataChange = (selectedData: any) => {
    if (!selectedData) return
    setFieldValue('street', selectedData.street || '')
    setFieldValue('city', selectedData.city || '')
    setFieldValue('state', selectedData.state || '')
    setFieldValue('zip', selectedData.zip || '')
    setFieldValue('distance', selectedData.distance || 0)
    setFieldValue('contactId', selectedData.value) // _id
    if (selectedData.street && selectedData.city && selectedData.state && selectedData.zip) {
      setClientAddress?.(`${selectedData.street}, ${selectedData.city}, ${selectedData.state} ${selectedData.zip}, USA`)
    } else {
      setFieldValue('duration', 0)
    }
    if (setDuration) setDuration(selectedData.duration)
  }

  return (
    <Styled.DropDownOuterContainer
      ref={ref1}
      className={className}
      onBlur={() => {
        if (!filterOptions?.length && validate) {
          setFieldValue(stateName, value)
        }
      }}
    >
      <Styled.DropDownContainer
        marginTop="8px"
        onClick={
          disabled
            ? () => {}
            : () => {
                setShowDropdown((prev) => !prev)
                setOnFocused((prev) => !prev)
              }
        }
        ref={ref}
        focus={onFocused}
        error={error}
        disabled={disabled}
        onKeyDown={(e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            setOnFocused(false)
            setShowDropdown(false)
          }
        }}
      >
        <NormalInput
          padding={'20px 40px 8px 16px'}
          labelName={labelName}
          stateName={stateName}
          error={error}
          twoInput={true}
          value={
            // Show label for selected value
            options.find((opt: any) => opt.value === value)?.label || value
          }
          noMessage
          disabled={disabled}
          selectTextOnFocus
          onChange={(e: any) => {
            handleInputChange(e.target.value)
          }}
        />

        <Styled.DropdownIconDiv
          onClick={
            disabled
              ? () => {}
              : (e) => {
                  e.stopPropagation()
                  setShowDropdown((prev) => !prev)
                }
          }
        >
          {searchLoader ? <SearchLoader /> : showDropdown ? <UpArrowIcon /> : <DownArrowIcon />}
        </Styled.DropdownIconDiv>

        <Styled.DropDownContentContainer $visibility={showDropdown} height={dropdownHeight} borderRadius={borderRadius}>
          {filterOptions.length > 0 ? (
            filterOptions.map((data: any) => (
              <Styled.DropDownItem
                borderRadius={borderRadius}
                key={data.value}
                onClick={(e: any) => {
                  e.stopPropagation()
                  handleSelectOption(data)
                }}
                active={value === data.value}
              >
                {data.label}
              </Styled.DropDownItem>
            ))
          ) : !showAddOption ? (
            <Styled.DropDownItem noHover={true}>Nothing found</Styled.DropDownItem>
          ) : null}
          {showAddOption ? (
            <Styled.DropDownItem
              borderRadius={borderRadius}
              fontWeight={700}
              onClick={(e: any) => {
                e.stopPropagation()
                onAddClick && onAddClick(value)
                setShowDropdown(false)
                setOnFocused(false)
              }}
            >
              {addNewText ?? '+ Add New'}
            </Styled.DropDownItem>
          ) : null}
        </Styled.DropDownContentContainer>
      </Styled.DropDownContainer>
      {error && (
        <Styled.ErrorMsg width="100%">
          {/* @ts-ignore */}
          <ErrorMessage component="div" name={stateName} />
        </Styled.ErrorMsg>
      )}
    </Styled.DropDownOuterContainer>
  )
}

export default AutoCompleteWithId
