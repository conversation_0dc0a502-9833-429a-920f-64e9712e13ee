import React from 'react'
import Modal from '../../../../../shared/customModal/Modal'
import { FlexCol, FlexRow } from '../../../../../styles/styled'
import Button from '../../../../../shared/components/button/Button'
import { Form, Formik } from 'formik'
import { TextAreaWithValidation } from '../../../../../shared/textAreaWithValidation'
import * as Yup from 'yup'

const CloseActionModal = ({
  setShowCloseOutModal,
  onSubmit,
}: {
  setShowCloseOutModal: React.Dispatch<React.SetStateAction<boolean>>
  onSubmit: ({ reason }: { reason: string }) => void
}) => {
  const initialValues = {
    closingReason: '',
  }
  const handleSubmit = (submittedValues: typeof initialValues) => {
    onSubmit({
      reason: submittedValues.closingReason,
    })
  }
  return (
    <Modal
      title="Close Out Action"
      onClose={() => {
        setShowCloseOutModal(false)
      }}
    >
      <FlexCol alignItems="center" gap="16px">
        <h1
          style={{
            fontSize: '20px',
          }}
        >
          Are you sure?
        </h1>

        <p className="text-center">Only do this if you have nothing left to do on this Contact or Opportunity.</p>

        <Formik
          initialValues={initialValues}
          validateOnChange={true}
          validateOnBlur={false}
          validationSchema={Yup.object().shape({
            closingReason: Yup.string().required('Required'),
          })}
          onSubmit={handleSubmit}
        >
          {({ errors, touched }) => {
            return (
              <Form style={{ width: '100%' }}>
                <TextAreaWithValidation
                  labelName="Reason for Closing*"
                  stateName="closingReason"
                  error={touched.closingReason && errors.closingReason ? true : false}
                />

                <FlexRow width="100%" gap="12px" margin="24px 0 0 0" justifyContent="space-between">
                  <Button
                    type="button"
                    className="delete"
                    onClick={() => {
                      setShowCloseOutModal(false)
                    }}
                    width="150px"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" className="outline" width="150px">
                    Close Out
                  </Button>
                </FlexRow>
              </Form>
            )
          }}
        </Formik>
      </FlexCol>
    </Modal>
  )
}

export default CloseActionModal
