import { Field, Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import {
  deleteTimeCard,
  getMemberTimeCards,
  updateActivityTimeCard,
  updateTimeCard,
} from '../../../../logic/apis/approveTimeCard'
import { getOppClockInMobile } from '../../../../logic/apis/projects'
import { getMemberTasks } from '../../../../logic/apis/task'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import {
  ARROWMARK,
  DAILY_LOG_STATUS,
  LAYERS,
  PITCHES,
  STATUS,
  StorageKey,
  TimeCardStatusEnum,
} from '../../../../shared/helpers/constants'
import {
  onlyMmDdYyyy,
  onlyNumber,
  onlyPositiveNumberRegex,
  onlyPositiveNumberWithTwoDecimalsRegex,
  onlyText,
  onlyTextWithSpaces,
  onlyTextWithSpacesHypens,
  twoDecimal,
} from '../../../../shared/helpers/regex'
import {
  camelCaseToSentenceCase,
  createCustomDate,
  dayjsFormat,
  getChangedValues,
  getDataFromLocalStorage,
  getEditDate,
  getFormattedDate,
  getFormattedDateForHistory,
  getHoursAndMinutes,
  handleWheel,
  isCustomTruthy,
  isSuccess,
  isValidDate,
  notify,
  simplifyBackendError,
  startOfDate,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { getPieceWorkByTaskId } from '../../../../logic/apis/pieceWork'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { TimecardCont } from '../addTimeCardPopUp/style'
import useWindowDimensions from '../../../../shared/hooks/useWindowDimensions'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import dayjs from 'dayjs'
import { removeDuplicates } from '../addTimeCardPopUp/AddTimeCardPopUp'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  date: string
  project: string
  task: string
  taskName?: string
  allHourlyPayCheckbox: boolean
  removeFromLeadBonusCheckbox: boolean
  workDone: Array<any>
  extraTime: Array<any>
  notes: string
  managerNotes: string
  status: string
  timeIn: string
  timeOut: string
  pieceWork?: any[]
}

interface I_TimeCardPopUp {
  setShowTimeCardPopUp: React.Dispatch<React.SetStateAction<boolean>>
  editPopUpValues: InitialValues
  timeCardData: any
  setDataUpdate: React.Dispatch<React.SetStateAction<boolean>>
  setIsLoaded?: React.Dispatch<React.SetStateAction<boolean>>
  setModifiedTimecardId?: React.Dispatch<React.SetStateAction<string>>
  isTimecardScreen?: boolean
}

interface PieceworkSetting {
  name: string
  unit: string
  usesPitch: boolean
  pitch: PitchAmount[]
  _id: string
  hasLayer: boolean
  description: string
}

interface PitchAmount {
  amount: number
  pitch: number
}

export const removeCheckboxSuffix = (text: string): string => {
  const suffix = 'Checkbox'
  if (text.endsWith(suffix)) {
    return text.slice(0, -suffix.length) // Remove the "Checkbox" suffix
  }
  return text
}

export const EditTimeCardPopUp = (props: I_TimeCardPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */

  const {
    setShowTimeCardPopUp,
    editPopUpValues,
    timeCardData,
    setDataUpdate,
    setIsLoaded,
    setModifiedTimecardId,
    isTimecardScreen,
  } = props

  const hasExtraTime = editPopUpValues?.extraTime?.[0]?.hours || editPopUpValues?.extraTime?.[0]?.minutes
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: editPopUpValues?.name,
    date: editPopUpValues?.date,
    project: editPopUpValues?.project,
    task: editPopUpValues?.taskName!,
    allHourlyPayCheckbox: timeCardData?.work?.hasOwnProperty('allHourly') ? timeCardData?.work?.allHourly : false,
    removeFromLeadBonusCheckbox: timeCardData?.work?.removeLeadBonus,
    workDone: editPopUpValues?.workDone,
    extraTime: editPopUpValues?.extraTime,
    notes: editPopUpValues?.notes,
    managerNotes: editPopUpValues?.managerNotes,
    status: editPopUpValues?.status,
    timeIn: editPopUpValues?.timeIn,
    taskName: editPopUpValues?.taskName,
    timeOut: editPopUpValues?.timeOut,
    pieceWork: editPopUpValues?.workDone?.length
      ? [...editPopUpValues?.workDone, hasExtraTime ? { name: 'Extra hours' } : null]?.filter(Boolean)
      : [hasExtraTime ? { name: 'Extra hours' } : null]?.filter(Boolean),
  })

  const [isClockOut, setIsClockOut] = useState(false)

  const [selectedPoName, setSelectedPoName] = useState(editPopUpValues?.project)
  const [selectedTask, setSelectedTask] = useState(editPopUpValues?.taskName)

  const [loading, setLoading] = useState<boolean>(false)
  const [onDeleteLoading, setOnDeleteLoading] = useState<boolean>(false)
  const [showHistory, setShowHistory] = useState<boolean>(false)
  const [taskData, setTaskData] = useState<any>([])
  const [taskIdData, setTaskIdData] = useState<any>([])
  const [IdTaskData, setIdTaskData] = useState<any>([])
  const [poData, setPoData] = useState<any>([])
  const [pitch, setPitch] = useState([])
  const [layer, setLayer] = useState([])

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, position, positionDetails } = globalSelector.company

  const [allTaskData, setAllTaskData] = useState([])
  const { isMobile } = useWindowDimensions()

  const [isHourly, setIsHourly] = useState(false)

  const [pieceworkByTaskId, setPieceworkByTaskId] = useState<PieceworkSetting[]>()
  const [pieceworkLoading, setPieceworkLoading] = useState(false)

  const isPieceworkPosition = isTimecardScreen
    ? positionDetails?.pieceworkPositions?.includes(timeCardData?.positionId)
    : positionDetails?.isPieceworkPosition

  const workDoneValidationSchema = Yup.object().shape({
    name: Yup.string().when([], {
      is: () => isClockOut && isPieceworkPosition,
      then: Yup.string().required('Required'),
    }),
    amount: Yup.string()
      .matches(twoDecimal, 'Only numbers with upto two decimals are allowed')
      .matches(onlyPositiveNumberWithTwoDecimalsRegex, 'Enter number greater than zero')
      .when('name', {
        is: (val: string) => val !== 'Extra hours' && isClockOut && isPieceworkPosition,
        then: Yup.string().required('Required'),
      }),
    layers: Yup.string()
      .nullable()
      .when('name', {
        is: (val: string) =>
          pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.hasLayer && !!layer?.length,
        then: Yup.string().required('Required'),
      }),
    pitch: Yup.string().when('name', {
      is: (val: string) =>
        pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.usesPitch && !!pitch?.length,
      then: Yup.string().required('Required'),
    }),
  })

  const extraTimeValidationSchema = Yup.object().shape({
    hours: Yup.string(),
    minutes: Yup.string(),
  })

  const isForeman = position === 'Foreman' || position === 'CrewLead'

  /**
   * TimeCardPopUpSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const TimeCardPopUpSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyTextWithSpacesHypens, 'Enter Valid Name'),
    // date: Yup.string().required('Required').matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
    project: Yup.string().required('Required'),
    task: Yup.string().required('Required'),
    timeIn: Yup.string().required('Required'),
    timeOut: Yup.string().when('name', {
      is: (val: string) => isClockOut,
      then: Yup.string().required('Required'),
    }),
    pieceWork: isHourly ? Yup.array() : Yup.array().of(workDoneValidationSchema),
    extraTime: Yup.array().of(extraTimeValidationSchema),
  })
  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      const currentTime = new Date().getTime()

      // send below variable as payload to activity changed API
      if (submittedValues?.allHourlyPayCheckbox) {
        submittedValues.pieceWork = []
        submittedValues.extraTime[0].hours = 0
        submittedValues.extraTime[0].minutes = 0
      }

      const editedValues = getChangedValues(submittedValues, initialValues)

      const formattedValue =
        Object.keys(editedValues)?.length &&
        Object.entries(editedValues)?.reduce((acc: Record<string, unknown>, [k, v]) => {
          if (k === 'pieceWork') {
            const val = (v as Array<any>)?.map(
              (element: { name: string; amount: number; pitch: number; layers: number; isRemoved: boolean }) => {
                if (element?.name !== 'Extra hours')
                  return {
                    [element?.name]: element?.amount ?? '',
                    pitch: element?.pitch,
                    layer: element?.layers,
                    isRemoved: element?.isRemoved,
                  }
              }
            )

            acc[k] = val
          } else {
            acc[k] = v
          }

          return acc
        }, {})

      const isExtraTimeAdded = submittedValues?.pieceWork?.findIndex((itm) => itm?.name === 'Extra hours') !== -1
      setLoading(true)

      const modifiedPieceWork = submittedValues?.pieceWork
        ?.filter((itm) => itm?.name !== 'Extra hours')
        ?.map((p) => {
          const selectedPiecework = pieceworkByTaskId?.find((pw: { name: string }) => pw?.name === p?.name)

          return {
            pitch:
              typeof p?.pitch === 'string' && !!pitch?.length && selectedPiecework?.usesPitch
                ? Number(p?.pitch?.split('/')[0]?.trim())
                : !!pitch?.length && selectedPiecework?.usesPitch
                ? p?.pitch
                : undefined,
            layers: p?.layers && !!layer?.length && selectedPiecework?.hasLayer ? Number(p?.layers) : undefined,
            amount: p?.value ? p?.value : p?.amount,
            id: selectedPiecework?._id,
            // name: p?.name,
            // unit: pieceworkByTaskId?.find((d: any) => d?.name === p?.name)?.unit,
          }
        })

      let currentTimeInDate = new Date(submittedValues.date)
      currentTimeInDate.setHours(Number(submittedValues.timeIn.split(':')[0]))
      currentTimeInDate.setMinutes(Number(submittedValues.timeIn.split(':')[1]))
      currentTimeInDate.setSeconds(0)
      currentTimeInDate.setMilliseconds(0)

      let currentTimeOutDate =
        submittedValues.timeOut.split(':')[0] === 'NaN' || !submittedValues.timeOut
          ? null
          : new Date(submittedValues.date) || undefined

      currentTimeOutDate?.setSeconds(0)
      currentTimeOutDate?.setMilliseconds(0)
      currentTimeOutDate?.setHours(Number(submittedValues.timeOut.split(':')[0]))
      currentTimeOutDate?.setMinutes(Number(submittedValues.timeOut.split(':')[1]))
      let clockInTimeInSeconds = new Date(currentTimeInDate).getTime()
      let currentTimeInSeconds = currentTimeOutDate && new Date(currentTimeOutDate).getTime()

      let timeClockedIn = currentTimeInSeconds! - clockInTimeInSeconds
      let hoursInSeconds = Number(submittedValues.extraTime[0].hours) * 60 * 60
      let minutesInSeconds = Number(submittedValues.extraTime[0].minutes) * 60
      let totalExtraTimeInMilliseconds = (hoursInSeconds + minutesInSeconds) * 1000
      let diffBetweenClockedInAndExtraTime = timeClockedIn - totalExtraTimeInMilliseconds

      if (timeClockedIn < 0 && currentTimeInSeconds) {
        notify('Clock-in time must be before clock-out time', 'error')
        setLoading(false)
        return
      }

      if (currentTimeInSeconds! > currentTime) {
        notify('Cannot add timecard in future', 'error')
        setLoading(false)
        return
      }

      if (diffBetweenClockedInAndExtraTime < 0) {
        notify('Cannot put extra time more than actual worked time!', 'error')
        setLoading(false)
        return
      }

      let hrsInMillisecs = Number(currentTimeOutDate) - Number(currentTimeInDate) || 0
      let hours = Math.round((hrsInMillisecs / (1000 * 60 * 60)) * 100) / 100

      // if (!submittedValues.allHourlyPayCheckbox) {
      //   if (submittedValues.task === 'Repairs' || submittedValues.task === 'Office') {
      //     submittedValues.allHourlyPayCheckbox = true
      //   }
      // }
      // if (submittedValues.allHourlyPayCheckbox) {
      //   if (submittedValues.task === 'Meetings') {
      //     submittedValues.allHourlyPayCheckbox = false
      //   }
      // }

      // let workDoneObj

      // const hasWork = ['Tear Off', 'Roofing', 'Repairs']

      // const newData = submittedValues?.workDone?.map((item) => ({
      //   pitch: Number(item.pitch),
      //   sqs: Number(item.sqs ?? 0),
      //   layers: Number(item.layers),
      // }))

      let workDoneObj = {
        workDone: pieceworkByTaskId?.length ? (isPieceworkPosition ? removeDuplicates(modifiedPieceWork) : []) : [],
        // extra: extras,
        extraTime: isExtraTimeAdded
          ? { extraHrs: submittedValues.extraTime[0].hours, extraMin: submittedValues.extraTime[0].minutes }
          : { extraHrs: 0, extraMin: 0 },
      }

      if (
        pieceworkByTaskId?.length &&
        !submittedValues?.allHourlyPayCheckbox &&
        !workDoneObj?.workDone?.length &&
        submittedValues?.timeOut &&
        !isExtraTimeAdded &&
        isPieceworkPosition
      ) {
        notify('Please add piecework', 'error')
        setLoading(false)
        return
      }

      let dataObj: any = {
        active: currentTimeOutDate ? false : undefined,
        _id: timeCardData._id,
        memberId: timeCardData.memberId,
        projectId: poData
          ?.filter((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}` === submittedValues.project)
          ?.map((value: any) => String(value?._id))
          ?.join(','),
        projectPO: submittedValues.project,
        task: taskIdData[submittedValues.task],
        workTaskId: taskIdData[submittedValues.task],
        // timeIn: `${formattedDate.split('T')[0]}T${submittedValues.timeIn}:00.000Z`,
        // timeOut: `${formattedDate.split('T')[0]}T${submittedValues.timeOut}:00.000Z`,
        // timeIn: currentTimeInDate,
        // timeOut: currentTimeOutDate,
        timeIn: currentTimeInDate?.toISOString(),
        timeOut: currentTimeOutDate?.toISOString(),
        allHourly: submittedValues.allHourlyPayCheckbox,
        // timeIn: `${submittedValues.date}T${submittedValues.timeIn}:00.000Z`,
        // timeOut: `${submittedValues.date}T${submittedValues.timeOut}:00.000Z`,

        status: submittedValues.status,
        date: submittedValues.date,
        hrs: hours > 0 ? hours : 0,
        work: workDoneObj,
        notes: submittedValues.notes,
        managerNotes: submittedValues.managerNotes,
        removeLeadBonus: submittedValues.removeFromLeadBonusCheckbox,
      }

      const response = await updateTimeCard(dataObj)

      if (isSuccess(response)) {
        await updateActivityTimeCard(timeCardData._id, {
          ...formattedValue,
        })
        notify('Edited Timecard Successfully', 'success')
        setLoading(false)
        setShowTimeCardPopUp(false)
        setIsLoaded?.(true)
        setDataUpdate((prev) => !prev)
      } else {
        notify(simplifyBackendError(response?.data?.message), 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('handleSubmit error', error)
    } finally {
      setLoading(false)
      setModifiedTimecardId?.(timeCardData?._id)
    }
  }

  const onDelete = async (timeCardId: string) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setOnDeleteLoading(true)

      let dataObj = {
        timeCardId: timeCardId,
      }

      const response = await deleteTimeCard(dataObj)

      if (isSuccess(response)) {
        setDataUpdate((prev) => !prev)
        setOnDeleteLoading(false)
        setShowTimeCardPopUp(false)
        notify('Deleted Time card Successfully', 'success')
      } else {
        notify(response?.data?.message, 'error')
        setOnDeleteLoading(false)
      }
      // }
    } catch (error) {
      console.error('onDelete error', error)
      setOnDeleteLoading(false)
    }
  }

  const isApproved = editPopUpValues?.status === 'LeadApproved' || editPopUpValues?.status === 'Approved'

  const getTasks = async () => {
    try {
      if (Object.keys(timeCardData).length > 0) {
        let taskResponse = await getMemberTasks({}, timeCardData.memberId)

        if (isSuccess(taskResponse)) {
          let workTasks = taskResponse?.data?.data?.workTask
          let taskObj: any = []
          let taskIdObj: any = {}
          let idTaskObj: any = {}

          workTasks.forEach((workTask: any) => {
            taskObj.push(workTask.name)
            taskIdObj = { ...taskIdObj, [`${workTask.name}`]: workTask._id }
            idTaskObj = { ...idTaskObj, [`${workTask._id}`]: workTask.name }
          })
          setTaskData(taskObj)
          setTaskIdData(taskIdObj)
          setAllTaskData(workTasks)

          setIdTaskData(idTaskObj)
        } else {
          notify(taskResponse?.data?.message, 'error')
        }
      }
    } catch (error) {
      console.error('getTasks error', error)
    }
  }

  useEffect(() => {
    setInitialValues({ ...initialValues, ...editPopUpValues })
  }, [editPopUpValues])

  useEffect(() => {
    getTasks()
  }, [timeCardData])

  useEffect(() => {
    setInitialValues({ ...initialValues, task: IdTaskData[editPopUpValues?.task] })
  }, [Object.values(IdTaskData)?.length])

  const getPO = async () => {
    try {
      let clockObj: any = []
      const result = await getOppClockInMobile()
      if (result?.data?.data?.readyOpps?.length) {
        result?.data?.data?.readyOpps.forEach((clockIn: any) => {
          clockObj.push(clockIn)
        })
        setPoData(clockObj)
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  useEffect(() => {
    getPO()
  }, [])

  const getValueByKey = (poValue: string) => {
    const result = poData.find((item: any) => `${item?.PO}${item?.num ? `-${item?.num}` : ''}` === poValue)
    return result ? result._id : ''
  }

  const fetchPitchLayer = async (Id: string) => {
    try {
      const res = poData?.find((itm: any) => itm?._id === Id)

      setPitch(res?.pitches?.filter(Boolean)?.length ? res?.pitches?.filter(Boolean) : PITCHES)
      setLayer(res?.layers?.filter(Boolean)?.length ? res?.layers?.filter(Boolean) : LAYERS)
    } catch (error) {
      console.error(error)
    }
  }
  // useEffect(() => {
  //   if (values.project !== '') {
  //     const Id = getValueByKey(values.project)
  //     fetchPitchLayer(Id)
  //   }
  // }, [initialValues.project])
  return (
    <Styled.TimeCardPopUpContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={TimeCardPopUpSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          useEffect(() => {
            if (poData.length !== 0) {
              if (values.project !== '' || initialValues.project !== '') {
                const Id = getValueByKey(values.project !== '' ? values.project : initialValues.project)
                fetchPitchLayer(Id)
              }
            }
          }, [values.project, initialValues.project, poData])

          useEffect(() => {
            setIsHourly(values?.allHourlyPayCheckbox)
          }, [values?.allHourlyPayCheckbox])

          useEffect(() => {
            if (taskIdData && values?.task && Object.values(taskIdData)?.length) {
              const taskName = IdTaskData[editPopUpValues?.task]
              if (taskName !== values?.task) {
                values.pieceWork = pieceworkByTaskId?.length ? [{ name: '', amount: '' }] : []
              }
              ;(async () => {
                try {
                  setPieceworkLoading(true)
                  const res =
                    values?.task &&
                    (await getPieceWorkByTaskId({
                      taskId: taskIdData[values?.task],
                      date: startOfDate(values.date),
                      memberId: timeCardData.memberId,
                    }))

                  const customPiecework = res?.data?.data?.pieceWorkSettings?.map((item: any) => ({
                    name: item?.name,
                    unit: item?.unit?.split('(')?.[0]?.trim(),
                    usesPitch: item?.usesPitch,
                    pitch: item?.pitch?.map((p: any) => ({ amount: p?.amount, pitch: p?.pitchOrder })),
                    _id: item?._id,
                    hasLayer: item?.hasLayer,
                    description: item?.description,
                  }))
                  setPieceworkByTaskId(customPiecework)
                } catch (error) {
                  console.error('error=====>', error)
                } finally {
                  setPieceworkLoading(false)
                }
              })()
            }
          }, [taskIdData, values?.task])

          // if (editPopUpValues?.task && !values.pieceWork?.[0]?.task) {
          //   values.pieceWork = values.workDone
          // }

          // if (editPopUpValues?.task !== values?.task) {
          //   values.pieceWork = [{ name: '', value: '' }]
          // }

          const dropdownValues = pieceworkByTaskId?.map((item: any) => ({ ...item, amount: '' }))
          dropdownValues?.push({ name: 'Extra hours' })
          const selectedPws = values?.pieceWork?.map((v) => v?.name)

          return (
            <TimecardCont>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Time Card</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setShowTimeCardPopUp(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    gap="10px"
                    width="100%"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                      disabled={true}
                    />
                    <SharedDate
                      value={values.date}
                      labelName="Date"
                      stateName="date"
                      error={touched.date && errors.date ? true : false}
                      setFieldValue={setFieldValue}
                      disabled={isApproved || true}
                    />

                    {/* <CustomSelect
                      value={values.project}
                      labelName="PO"
                      stateName="project"
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      dropDownData={poData?.map((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}`)}
                      setFieldValue={setFieldValue}
                      error={touched.project && errors.project ? true : false}
                      disabled={isApproved}
                    /> */}

                    <AutoComplete
                      value={values?.project}
                      labelName="PO"
                      preSelected
                      stateName="project"
                      options={poData?.map((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}`)}
                      dropdownHeight="300px"
                      borderRadius="0px"
                      setFieldValue={setFieldValue}
                      setValueOnClick={(val: string) => {
                        setSelectedPoName(val)
                      }}
                      selectedValue={selectedPoName}
                      disabled={isApproved}
                      validate
                      error={touched.project && errors.project ? true : false}
                    />

                    <AutoComplete
                      value={values?.task}
                      labelName="Task"
                      stateName="task"
                      preSelected
                      options={taskData}
                      dropdownHeight="300px"
                      borderRadius="0px"
                      validate
                      setValueOnClick={(val: string) => {
                        setSelectedTask(val)
                      }}
                      selectedValue={selectedTask}
                      setFieldValue={setFieldValue}
                      error={touched.task && errors.task ? true : false}
                      disabled={isApproved}
                    />
                    {/* <CustomSelect
                      value={values.task ?? ''}
                      labelName="Task"
                      stateName="task"
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      dropDownData={taskData}
                      setFieldValue={setFieldValue}
                      error={touched.task && errors.task ? true : false}
                      disabled={isApproved}
                    /> */}
                    <InputWithValidation
                      labelName="Time In"
                      stateName="timeIn"
                      type="time"
                      error={touched.timeIn && errors.timeIn ? true : false}
                      disabled={isApproved}
                    />

                    {!values?.timeOut && !isClockOut ? (
                      <Button
                        type="button"
                        className="delete"
                        onClick={() => {
                          setIsClockOut(true)
                          setFieldValue('timeOut', dayjs().format('HH:mm'))
                        }}
                      >
                        Clock out
                      </Button>
                    ) : null}

                    {isClockOut || values?.timeOut ? (
                      <>
                        <InputWithValidation
                          labelName="Time Out"
                          stateName="timeOut"
                          type="time"
                          error={touched.timeOut && errors.timeOut ? true : false}
                          disabled={isApproved}
                        />
                        <CustomSelect
                          value={values.status}
                          labelName="Status"
                          stateName="status"
                          dropDownData={DAILY_LOG_STATUS}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          margin="10px 0 0 0"
                          error={touched.status && errors.status ? true : false}
                          disabled={position === 'Crew Member' || isForeman || isApproved}
                          showInitialValue
                        />

                        <>
                          {pieceworkLoading && isPieceworkPosition ? <SLoader height={100} /> : null}
                          <>
                            {isPieceworkPosition ? (
                              <>
                                {allTaskData?.find((itm: any) => itm?.name === values?.task)?.pieceWork && (
                                  <SharedStyled.FlexBox
                                    width="100%"
                                    alignItems="center"
                                    gap="5px"
                                    marginTop="6px"
                                    justifyContent="flex-start"
                                  >
                                    <Styled.CheckBox
                                      width="15px"
                                      height="20px"
                                      type="checkbox"
                                      defaultValue={values.allHourlyPayCheckbox}
                                      name="allHourlyPayCheckbox"
                                      disabled={isApproved}
                                    />
                                    <Styled.CheckBoxDescription>
                                      No Piece Work - all hourly pay
                                    </Styled.CheckBoxDescription>
                                  </SharedStyled.FlexBox>
                                )}

                                {pieceworkByTaskId?.length && values.allHourlyPayCheckbox === false ? (
                                  <div className="piecework">
                                    <Styled.WorkDoneContainer name="pieceWork">
                                      {(fieldArrayProps: any) => {
                                        const { push, remove } = fieldArrayProps
                                        return (
                                          <div className="piecework">
                                            {values.pieceWork?.map((work: any, index: number) => (
                                              <Fragment key={index}>
                                                <SharedStyled.FlexRow alignItems="flex-start">
                                                  <CustomSelect
                                                    value={work?.name ?? ''}
                                                    labelName="Piecework"
                                                    setValue={() => {}}
                                                    margin="10px 0 0 0"
                                                    showInitialValue
                                                    maxWidth="160px"
                                                    disabled={isApproved}
                                                    toolTipText={
                                                      dropdownValues?.find((item) => item?.name === work?.name)
                                                        ?.description
                                                    }
                                                    stateName={`pieceWork.${index}.name`}
                                                    dropDownData={
                                                      dropdownValues
                                                        ?.filter(
                                                          (item) =>
                                                            !selectedPws?.includes(item?.name) ||
                                                            item?.usesPitch ||
                                                            item?.hasLayer
                                                        )
                                                        ?.map((t) => t.name) as string[]
                                                    }
                                                    setFieldValue={setFieldValue}
                                                    error={errors?.pieceWork?.[index]?.name ? true : false}
                                                  />
                                                  <SharedStyled.FlexRow className="piece-values-container">
                                                    <SharedStyled.FlexRow
                                                      alignItems="flex-start"
                                                      className="piece-values"
                                                    >
                                                      {work?.name !== 'Extra hours' ? (
                                                        <InputWithValidation
                                                          labelName={
                                                            dropdownValues?.find(
                                                              (itm) => itm?.name === values?.pieceWork[index]?.name
                                                            )?.unit ?? 'Select piecework'
                                                          }
                                                          defaultValue={work?.amount}
                                                          minWidth="80px"
                                                          disabled={isApproved}
                                                          forceType="number"
                                                          stateName={`pieceWork.${index}.amount`}
                                                          error={errors?.pieceWork?.[index]?.amount ? true : false}
                                                        />
                                                      ) : (
                                                        <Styled.NameValueUnitContainer
                                                          justifyContent="flex-start"
                                                          width="100%"
                                                          marginTop="8px"
                                                          flexWrap="wrap"
                                                          gap="0px"
                                                          disabled={isApproved}
                                                          style={{ justifyContent: 'flex-start' }}
                                                          className="extra-container"
                                                        >
                                                          <SharedStyled.FlexBox alignItems="center">
                                                            <Styled.ValueInput
                                                              defaultValue={values?.extraTime?.[0]?.hours ?? 0}
                                                              type="number"
                                                              name={`extraTime[0].hours`}
                                                              onWheel={handleWheel}
                                                              disabled={isApproved}
                                                            />
                                                            <Styled.UnitDiv>HR</Styled.UnitDiv>
                                                          </SharedStyled.FlexBox>
                                                          <SharedStyled.FlexBox alignItems="center">
                                                            <Styled.ValueInput
                                                              type="number"
                                                              defaultValue={values?.extraTime?.[0]?.minutes ?? 0}
                                                              marginLeft="8px"
                                                              disabled={isApproved}
                                                              onWheel={handleWheel}
                                                              name={`extraTime[0].minutes`}
                                                            />
                                                            <Styled.UnitDiv>MIN</Styled.UnitDiv>
                                                          </SharedStyled.FlexBox>
                                                        </Styled.NameValueUnitContainer>
                                                      )}
                                                      {dropdownValues?.find(
                                                        (itm) => itm?.name === values?.pieceWork[index]?.name
                                                      )?.usesPitch &&
                                                        !!pitch?.length && (
                                                          <CustomSelect
                                                            value={
                                                              !isNaN(work?.pitch)
                                                                ? `${work.pitch}/12`
                                                                : work.pitch
                                                                ? work?.pitch
                                                                : pitch?.map((item: any) => `${item}/12`)[0]
                                                            }
                                                            labelName="Pitch"
                                                            setValue={() => {}}
                                                            margin="10px 0 0 0"
                                                            showInitialValue
                                                            isPieceWork
                                                            disabled={isApproved}
                                                            isPitch
                                                            stateName={`pieceWork.${index}.pitch`}
                                                            dropDownData={pitch?.map((item: any) => `${item}/12`)}
                                                            setFieldValue={setFieldValue}
                                                            error={errors?.pieceWork?.[index]?.pitch ? true : false}
                                                          />
                                                        )}

                                                      {
                                                        // values.task === 'Tear Off' &&
                                                        dropdownValues?.find(
                                                          (itm) => itm?.name === values?.pieceWork[index]?.name
                                                        )?.hasLayer &&
                                                          !!layer?.length && (
                                                            <CustomSelect
                                                              value={work.layers ?? layer[0]}
                                                              labelName="Layers"
                                                              setValue={() => {}}
                                                              margin="10px 0 0 0"
                                                              showInitialValue
                                                              isPieceWork
                                                              disabled={isApproved}
                                                              stateName={`pieceWork.${index}.layers`}
                                                              dropDownData={layer}
                                                              setFieldValue={setFieldValue}
                                                              error={errors?.pieceWork?.[index]?.layers ? true : false}
                                                            />
                                                          )
                                                      }
                                                    </SharedStyled.FlexRow>
                                                    <SharedStyled.FlexRow margin="10px 0 0 0" width="max-content">
                                                      <Button
                                                        type="button"
                                                        width="max-content"
                                                        className="delete"
                                                        padding="7px 14px"
                                                        onClick={() => {
                                                          remove(index)
                                                        }}
                                                        disabled={isApproved}
                                                        // style={{ visibility: index > 0 ? 'visible' : 'hidden' }}
                                                      >
                                                        -
                                                      </Button>
                                                    </SharedStyled.FlexRow>
                                                  </SharedStyled.FlexRow>
                                                </SharedStyled.FlexRow>

                                                {isMobile ? (
                                                  <SharedStyled.HorizontalDivider
                                                    margin="14px 0 0 0"
                                                    bg="rgba(0, 0, 0, 0.6)"
                                                  />
                                                ) : null}
                                              </Fragment>
                                            ))}
                                            {values?.pieceWork?.length + 1 <= dropdownValues?.length ? (
                                              <SharedStyled.FlexRow>
                                                <Button
                                                  type="button"
                                                  width="max-content"
                                                  disabled={isApproved}
                                                  onClick={() => push({ name: '', amount: '' })}
                                                >
                                                  Add New
                                                </Button>
                                              </SharedStyled.FlexRow>
                                            ) : null}
                                          </div>
                                        )
                                      }}
                                    </Styled.WorkDoneContainer>
                                  </div>
                                ) : null}
                              </>
                            ) : null}
                          </>
                        </>
                      </>
                    ) : null}
                    <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Manager Notes:
                    </Styled.LabelDiv>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="managerNotes"
                      marginTop="8px"
                      height="100px"
                      disabled={position === 'Crew Member' || isForeman || isApproved}
                    ></Styled.TextArea>
                    <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="notes"
                      marginTop="8px"
                      height="100px"
                      disabled={isApproved}
                    ></Styled.TextArea>

                    {isClockOut || values?.timeOut ? (
                      <SharedStyled.FlexBox
                        width="100%"
                        alignItems="center"
                        gap="5px"
                        marginTop="6px"
                        justifyContent="flex-start"
                      >
                        <Styled.CheckBox
                          width="15px"
                          height="20px"
                          type="checkbox"
                          name="removeFromLeadBonusCheckbox"
                          disabled={isApproved}
                        />
                        <Styled.CheckBoxDescription>Remove from Lead Bonus</Styled.CheckBoxDescription>
                      </SharedStyled.FlexBox>
                    ) : null}
                    <SharedStyled.FlexRow justifyContent="space-between" margin="10px 0 0 0">
                      <SharedStyled.FlexRow>
                        <Button type="submit" width="max-content" isLoading={loading} disabled={isApproved}>
                          Save Changes
                        </Button>
                        <Button
                          type="button"
                          width="max-content"
                          className="gray"
                          onClick={() => {
                            resetForm()
                            setShowTimeCardPopUp(false)
                          }}
                        >
                          Cancel
                        </Button>
                      </SharedStyled.FlexRow>
                      <Button
                        type="button"
                        width="max-content"
                        className="delete"
                        onClick={() => onDelete(timeCardData?._id)}
                        isLoading={onDeleteLoading}
                        disabled={isApproved}
                      >
                        Delete
                      </Button>
                    </SharedStyled.FlexRow>

                    <Styled.ShowHistoryOption onClick={() => setShowHistory((prev) => !prev)} marginTop="10px">
                      {showHistory ? 'Hide' : 'Show'} History
                    </Styled.ShowHistoryOption>

                    {timeCardData && showHistory && (
                      <Styled.TimeCardHistoryDiv marginTop="14px">
                        {timeCardData?.activities
                          ?.sort(
                            (a: { createdAt: string }, b: { createdAt: string }) =>
                              new Date(b?.createdAt)?.getTime() - new Date(a?.createdAt)?.getTime()
                          )
                          .map((timeCard: any, idx: number) => (
                            <Styled.TimeCardHistoryHeading key={idx} marginTop={idx === 0 ? '4px' : '12px'}>
                              <span className="capitalize">{timeCard?.name}</span> - &nbsp;
                              <span className="value">
                                <>
                                  {timeCard?.createdAt ? getFormattedDateForHistory(timeCard.createdAt) : ''}{' '}
                                  {timeCard?.createdAt ? getHoursAndMinutes(timeCard.createdAt) : ''} <br />
                                </>
                                {timeCard?.data &&
                                  Object.entries(timeCard.data).map(([k, v]) => {
                                    return (
                                      <Fragment key={k}>
                                        {(Array.isArray(v) ? v?.length : isCustomTruthy(v)) ? (
                                          <>
                                            <span>{camelCaseToSentenceCase(removeCheckboxSuffix(k))}: </span>
                                            <span>{renderValueContent(v)}</span>
                                          </>
                                        ) : null}
                                      </Fragment>
                                    )
                                  })}
                              </span>
                            </Styled.TimeCardHistoryHeading>
                          ))}
                      </Styled.TimeCardHistoryDiv>
                    )}
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </TimecardCont>
          )
        }}
      </Formik>
    </Styled.TimeCardPopUpContainer>
  )
}

const renderValueContent = (value: any) => {
  switch (true) {
    case Array.isArray(value):
      return (
        <>
          {value?.filter(Boolean)?.map((itm, index) => {
            const keysArray = Object.keys(itm)

            return (
              <SharedStyled.FlexRow width="max-content" style={{ marginLeft: '20px' }} gap="4px" key={index}>
                {keysArray?.map((key, idx) => (
                  <Fragment key={idx}>
                    {itm?.['isRemoved'] ? (
                      <SharedStyled.FlexRow key={idx} width="max-content" gap="4px">
                        <Styled.StatusText isStriked className="capitalize">
                          {key === 'isRemoved' ? null : `${key}:`}{' '}
                        </Styled.StatusText>
                        <Styled.StatusText isStriked>{formatMessage(itm[key])}</Styled.StatusText>
                        {keysArray?.length - 1 === idx || !formatMessage(itm[key]) ? null : ','}
                      </SharedStyled.FlexRow>
                    ) : (
                      <SharedStyled.FlexRow key={idx} width="max-content" gap="4px">
                        <span className="capitalize">{key}: </span>
                        <span>{formatMessage(itm[key])}</span>
                        {keysArray?.length - 1 === idx || !formatMessage(itm[key]) ? null : ','}
                      </SharedStyled.FlexRow>
                    )}
                  </Fragment>
                ))}
              </SharedStyled.FlexRow>
            )
          })}
        </>
      )

    case typeof value === 'object':
      return null
    case typeof value === 'boolean':
      return (
        <>
          <Styled.StatusText isStriked>{JSON.stringify(!value)}</Styled.StatusText>
          &nbsp; &#8594; &nbsp;
          <Styled.StatusText>{JSON.stringify(value)}</Styled.StatusText> <br />
        </>
      )

    default:
      return (
        <>
          {isValidDate(value) ? dayjsFormat(value, 'h:mm a on MM/DD/YYYY') : formatMessage(value)} <br />
        </>
      )
  }
}

const formatMessage = (msg: string) => {
  return String(msg)?.includes(ARROWMARK) ? (
    <>
      <Styled.StatusText isStriked>{msg?.split(ARROWMARK)?.[0]}</Styled.StatusText>
      &nbsp; &#8594; &nbsp;
      <Styled.StatusText>{msg?.split(ARROWMARK)?.[1]}</Styled.StatusText>
    </>
  ) : (
    msg
  )
}
