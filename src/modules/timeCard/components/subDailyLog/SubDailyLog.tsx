import { ErrorMessage, Field, Form, Formik } from 'formik'
import { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import {
  createDailyLog,
  getDailyLog,
  getDailyLogPO,
  getDailyLogProjectValue,
  updateDailyLog,
} from '../../../../logic/apis/dailyLog'
import { getSubContractors } from '../../../../logic/apis/subcontractor'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, subInitialProjData } from '../../../../shared/helpers/constants'
import { onlyMmDdYyyy, onlyNumber, onlyText, onlyUsNumber } from '../../../../shared/helpers/regex'
import {
  getDataFromLocalStorage,
  getPoIdFromName,
  getSubcontractorIdFromName,
  getSubcontractorNameFromId,
  handleWheel,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { ErrorContainer, ErrorMsg } from '../dailyLog/style'

interface I_SubDailyLog {
  setShowSubDailyLogModal: React.Dispatch<React.SetStateAction<boolean>>
  setIsSubEditDailyLog?: React.Dispatch<React.SetStateAction<boolean>>
  setSubDailyUpdate: React.Dispatch<React.SetStateAction<boolean>>
  subcontractorData: any
  action: string
  subDailyDataDropDown?: any
  subDailyLogId?: string
  editDailyLogData?: any
  fetchSubContractorToApprove?: any
  selectedContractor?: any
  isSubEditDailyLog?: boolean
}

export const SubDailyLog = (props: I_SubDailyLog) => {
  const {
    setShowSubDailyLogModal,
    setSubDailyUpdate,
    action,
    setIsSubEditDailyLog,
    subcontractorData,
    subDailyDataDropDown,
    subDailyLogId,
    editDailyLogData,
    fetchSubContractorToApprove,
    selectedContractor,
    isSubEditDailyLog,
  } = props
  const [subDailyDropdownData, setsubDailyDropdownData] = useState<any>([])
  const [getValueData, setGetValueData] = useState<any>([])
  const [showAllFieldsTearOff, setShowAllFieldsTearOff] = useState(false)
  const [showAllFieldsRoofing, setShowAllFieldsRoofing] = useState(false)
  const [showAllFieldsExtras, setShowAllFieldsExtras] = useState(false)
  const [getProjectValueData, setProjectValueData] = useState<any>([])
  const [indexValue, setIndexValue] = useState<number>(0)
  const [isShowmore, setIsShowmore] = useState(false)

  const [allPoList, setAllPoList] = useState([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */
  interface InitialValues {
    date: string
    subContractorName: string
    projects: Array<any>
  }
  useEffect(() => {
    // subDailyDataDropDown
    const filterWithName = subDailyDataDropDown.map((item: any) => `${item.name} :-${item._id}`)
    setsubDailyDropdownData(filterWithName)
  }, [subDailyDataDropDown])
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const tearOff = [
    {
      name: 'Removed',
      value: '',
      unit: 'SQ',
      codeName: 'tearOffSQ',
      done: { name1: 'doneTearSQ', name2: 'totalTearSQ' },
    },
    { name: 'R&R Plywood', value: '', unit: 'SHT', codeName: 'instSheet', done: { name: 'instSheet' } },
    // { name: 'Remove Plywood', value: '', unit: 'EA', codeName: 'rmvSheet', done: { name: 'rmvSheet' } },
    { name: 'Non-billable Plywood', value: '', unit: 'EA', codeName: 'nonBillPly', done: { name: 'nonBillPly' } },
    { name: `Add'l felt layers`, value: '', unit: 'SQ', codeName: 'tExtraFelt', done: { name: 'tExtraFelt' } },
    { name: 'Cut ridge vent', value: '', unit: 'LF', codeName: 'tCutRV', done: { name: 'tCutRV' } },
    { name: 'Cut can vents', value: '', unit: 'EA', codeName: 'tCutCan', done: { name: 'tCutCan' } },
    { name: 'Install vent plug', value: '', unit: 'EA', codeName: 'tVentPlug', done: { name: 'tVentPlug' } },
    { name: 'Install vent baffle', value: '', unit: 'EA', codeName: 'tBaffle', done: { name: 'tBaffle' } },
  ]
  const roofing = [
    {
      name: 'Installed',
      value: '',
      unit: 'SQ',
      codeName: 'roofingSQ',
      done: { name1: 'doneRoofSQ', name2: 'totalRoofSQ' },
    },
    { name: 'Install Windsor', value: '', unit: 'SQ', codeName: 'install50Yr', done: { name: 'install50Yr' } },
    { name: 'Chimney C/F', value: '', unit: 'EA', codeName: 'chimCF', done: { name: 'chimCF' } },
    { name: 'R&R skylight', value: '', unit: 'EA', codeName: 'replaceSkylight', done: { name: 'replaceSkylight' } },
    { name: 'Eyebrow', value: '', unit: 'EA', codeName: 'eyebrows', done: { name: 'eyebrows' } },
    { name: 'Bay window', value: '', unit: 'EA', codeName: 'bayWindows', done: { name: 'bayWindows' } },
  ]
  const replacementExtras = [
    { name: `Add'l man-hours`, value: '', unit: 'HR', codeName: 'addManHours', done: { name: 'addManHours' } },
    { name: 'Non-billable hours', value: '', unit: 'HR', codeName: 'nonBillHours', done: { name: 'nonBillHours' } },
    { name: `Add'l materials`, value: '', unit: '$', codeName: 'addMaterials', done: { name: 'addMaterials' } },
    { name: 'Install fascia', value: '', unit: 'LF', codeName: 'instFascia', done: { name: 'instFascia' } },
    { name: 'Remove fascia', value: '', unit: 'LF', codeName: 'rmvFascia', done: { name: 'rmvFascia' } },
    { name: 'Hand load roof', value: '', unit: 'SQ', codeName: 'handLoadSQ', done: { name: 'handLoadSQ' } },
    { name: '3+ stories', value: '', unit: 'SQ', codeName: 'highRoof', done: { name: 'highRoof' } },
  ]
  const nonReplacementExtras = [
    { name: 'R&R Plywood', value: '', unit: 'SHT', codeName: 'instSheet', done: { name: 'instSheet' } },
    { name: 'Non-billable Plywood', value: '', unit: 'HR', codeName: 'nonBillPly', done: { name: 'nonBillPly' } },
    { name: `Add'l man-hours`, value: '', unit: '$', codeName: 'addManHours', done: { name: 'addManHours' } },
    { name: 'Non-billable hours', value: '', unit: 'HR', codeName: 'nonBillHours', done: { name: 'nonBillHours' } },
    { name: `Add'l materials`, value: '', unit: '$', codeName: 'addMaterials', done: { name: 'addMaterials' } },
  ]

  const dateFromLocalStorage: any = getDataFromLocalStorage('currentDate')
  const [initialValues, setInitialValues] = useState<InitialValues>({
    date: dateFromLocalStorage.replace(/^"(.*)"$/, '$1') ?? '',
    subContractorName: '',
    projects: [
      {
        oppPO: '',
        oppId: '',
        notes: '',
        isTypeValue: false,
        tearOffSQ: 0,
        instSheet: 0,
        rmvSheet: 0,
        nonBillPly: 0,
        tExtraFelt: 0,
        tCutRV: 0,
        tCutCan: 0,
        tVentPlug: 0,
        tBaffle: 0,

        roofingSQ: 0,
        install50Yr: 0,
        chimCF: 0,
        replaceSkylight: 0,
        eyebrows: 0,
        bayWindows: 0,

        addManHours: 0,
        nonBillHours: 0,
        addMaterials: 0,
        instFascia: 0,
        rmvFascia: 0,
        handLoadSQ: 0,
        highRoof: 0,

        percentDone: 0,
      },
    ],
  })
  const [loading, setLoading] = useState<boolean>(false)
  const [subData, setSubData] = useState<any>([])
  const [subIdData, setSubIdData] = useState<any>([])
  const [IdSubData, setIdSubData] = useState<any>([])
  const [pickPo, setPickPo] = useState<any>([])

  const SubDailyLogSchema = Yup.object().shape({
    date: Yup.string().required('Required').matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
  })

  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)

      let subId: any = subcontractorData[0]._id
      submittedValues.date = startOfDate(submittedValues.date)

      // let tearOffValue = {
      //   tearOffSQ: submittedValues.projects[0].tearOff[0].value,
      //   instSheet: submittedValues.projects[0].tearOff[1].value,
      //   rmvSheet: submittedValues.projects[0].tearOff[2].value,
      //   nonBillPly: submittedValues.projects[0].tearOff[3].value,
      //   tExtraFelt: submittedValues.projects[0].tearOff[4].value,
      //   tCutRV: submittedValues.projects[0].tearOff[5].value,
      //   tCutCan: submittedValues.projects[0].tearOff[6].value,
      //   tVentPlug: submittedValues.projects[0].tearOff[7].value,
      //   tBaffle: submittedValues.projects[0].tearOff[8].value,
      // }

      // let roofingValue = {
      //   roofingSQ: submittedValues.projects[0].roofing[0].value,
      //   install50Yr: submittedValues.projects[0].roofing[1].value,
      //   chimCF: submittedValues.projects[0].roofing[2].value,
      //   replaceSkylight: submittedValues.projects[0].roofing[3].value,
      //   eyebrows: submittedValues.projects[0].roofing[4].value,
      //   bayWindows: submittedValues.projects[0].roofing[5].value,
      // }

      // let extrasValue = {
      //   addManHours: submittedValues.projects[0].extras[0].value,
      //   nonBillHours: submittedValues.projects[0].extras[1].value,
      //   addMaterials: submittedValues.projects[0].extras[2].value,
      //   instFascia: submittedValues.projects[0].extras[3].value,
      //   rmvFascia: submittedValues.projects[0].extras[4].value,
      //   handLoadSQ: submittedValues.projects[0].extras[5].value,
      //   highRoof: submittedValues.projects[0].extras[6].value,
      // }

      let dataObj = {
        ...submittedValues,
        crewId: getSubcontractorIdFromName(submittedValues.subContractorName, subDailyDataDropDown),

        createdBy: currentMember._id,
        auditLog: {
          editedBy: currentMember._id,
          editedAt: new Date(),
        },
        projects: submittedValues.projects.map((project: any) => ({
          ...project,
          oppPO: project?.oppPO,
          oppId: getPoIdFromName(project?.oppPO, allPoList),
          percentDone: project?.percentDone || 0,
          notes: project?.notes || '',
        })),
      }
      if (!action.includes('Edit')) {
        const response = await createDailyLog(dataObj)
        if (isSuccess(response)) {
          setLoading(false)
          notify('Daily Log Created Successfully', 'success')
          setSubDailyUpdate((prev) => !prev)
          setShowSubDailyLogModal(false)
          fetchSubContractorToApprove()
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      } else {
        let dataObj = {
          ...submittedValues,
          crewId: getSubcontractorIdFromName(submittedValues.subContractorName, subDailyDataDropDown),
          createdBy: currentMember._id,
          dailyLogId: subDailyLogId!,
          auditLog: {
            editedBy: currentMember._id,
            editedAt: new Date(),
          },
          projects: submittedValues.projects.map((project: any) => ({
            ...project,
            oppPO: project?.oppPO,
            oppId: getPoIdFromName(project?.oppPO, allPoList),
            percentDone: project?.percentDone || 0,
            notes: project?.notes || '',
          })),
        }
        const response = await updateDailyLog(dataObj)
        if (isSuccess(response)) {
          setLoading(false)
          notify('Daily Log Edited Successfully', 'success')
          setSubDailyUpdate((prev) => !prev)
          setShowSubDailyLogModal(false)
          fetchSubContractorToApprove()
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      }
      // }
    } catch (error) {
      console.error('handleSubmit error', error)
    }
  }

  useEffect(() => {
    if (action === 'Edit Daily Log' && editDailyLogData && subDailyDataDropDown) {
      setInitialValues({
        date: editDailyLogData?.date ? editDailyLogData?.date : '',
        subContractorName: editDailyLogData?.crewId
          ? getSubcontractorNameFromId(editDailyLogData?.crewId, subDailyDataDropDown)
          : '',
        projects: editDailyLogData?.projects,
      })
    }

    if (action === 'Add Daily Log' && selectedContractor?._id) {
      setInitialValues({
        ...initialValues,
        subContractorName: getSubcontractorNameFromId(selectedContractor?._id, subDailyDataDropDown),
      })
    }
  }, [editDailyLogData, subDailyDataDropDown, selectedContractor])
  const getPickPo = async () => {
    try {
      let clockObj: any = []
      let allClockObj: any = []

      const result = await getDailyLogPO(isShowmore)
      if (result?.data?.data?.projects?.length) {
        result?.data?.data?.projects.forEach((clockIn: any) => {
          clockObj.push(clockIn)
        })
        setPickPo(clockObj)
      }

      if (!isShowmore) {
        const result = await getDailyLogPO(true)
        if (result?.data?.data?.projects?.length) {
          result?.data?.data?.projects.forEach((clockIn: any) => {
            allClockObj.push(clockIn)
          })
          setAllPoList(allClockObj)
        }
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  // const getDetails = async () => {
  //   try {
  //     if (
  //       action === 'Edit Daily Log' &&
  //       Object.keys(currentCompany).length > 0 &&
  //       Object.keys(subDailyDataTemp[0]).length > 0
  //     ) {
  //       const idNameData = await getSubContractosubDailyDropdownDatarsDetail()
  //       const subResponse = await getDailyLog(currentCompany._id, subDailyLogId!)
  //
  //       if (isSuccess(subResponse)) {
  //         resetFormFunc()
  //         let statusRes = subResponse?.data?.data?.dailyLog[0]
  //         let subTearOff = [...initialValues.projects[0].tearOff]
  //         let subRoofing = [...initialValues.projects[0].roofing]
  //         let subExtras = [...initialValues.projects[0].extras]
  //         Object.entries(statusRes.projects[0].tearOff).forEach(
  //           ([key, values]: any, index: number) => (subTearOff[index].value = values)
  //         )
  //         Object.entries(statusRes.projects[0].roofing).forEach(
  //           ([key, values]: any, index: number) => (subRoofing[index].value = values)
  //         )
  //         Object.entries(statusRes.projects[0].extras).forEach(
  //           ([key, values]: any, index: number) => (subExtras[index].value = values)
  //         )
  //         let dataObj = {
  //           ...statusRes,
  //           projects: [
  //             {
  //               ...statusRes.projects[0],
  //               tearOff: subTearOff,
  //               roofing: subRoofing,
  //               extras: subExtras,
  //             },
  //           ],
  //         }
  //         delete dataObj.__v
  //         delete dataObj._id
  //         delete dataObj.updatedAt
  //         delete dataObj.createdAt
  //         setInitialValues({ ...initialValues, ...dataObj, subContractorName: idNameData[statusRes.crewId] })
  //         // setCityDropdown(cityStateData)
  //       } else {
  //         notify(subResponse?.data?.message, 'error')
  //       }
  //     } else {
  //       resetFormFunc()
  //     }
  //   } catch (error) {
  //     console.error('getDetails error', error)
  //   }
  // }

  const getSubContractorsDetail = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const subResponse = await getSubContractors({ deleted: false, retired: false })

      if (isSuccess(subResponse)) {
        let subObj: any = []
        let subIdObj: any = {}
        let idSubObj: any = {}
        let subDataArray = subResponse?.data?.data?.subcontractor

        subDataArray.forEach((sub: any) => {
          subObj.push(sub.name)
          subIdObj = { ...subIdObj, [`${sub.name}`]: sub._id }
          idSubObj = { ...idSubObj, [`${sub._id}`]: sub.name }
        })
        setSubData(subObj)
        setSubIdData(subIdObj)
        setIdSubData(idSubObj)
        return idSubObj
      } else {
        notify(subResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getSubContractorsDetail error', error)
    }
  }

  const resetFormFunc = () => {
    try {
      setInitialValues({
        date: '',
        subContractorName: '',
        projects: [
          {
            oppPO: '',
            oppId: '',
            notes: '',
            isTypeValue: false,
            tearOffSQ: 0,
            instSheet: 0,
            rmvSheet: 0,
            nonBillPly: 0,
            tExtraFelt: 0,
            tCutRV: 0,
            tCutCan: 0,
            tVentPlug: 0,
            tBaffle: 0,

            roofingSQ: 0,
            install50Yr: 0,
            chimCF: 0,
            replaceSkylight: 0,
            eyebrows: 0,
            bayWindows: 0,

            addManHours: 0,
            nonBillHours: 0,
            addMaterials: 0,
            instFascia: 0,
            rmvFascia: 0,
            handLoadSQ: 0,
            highRoof: 0,
          },
        ],
      })
    } catch (error) {
      console.error('resetFormFunc error', error)
    }
  }

  const getDailyPRojectvalues = useCallback(async (projectObjectByName: any) => {
    try {
      const res =
        projectObjectByName?.oppId &&
        (await getDailyLogProjectValue(projectObjectByName.oppId, projectObjectByName.type))
      return res?.data?.data?.SQ
      // setGetProjectValueData((prevApiResponses: any) => [...prevApiResponses, [`projectObjectByName.po`]:res?.data?.SQ])
    } catch (error) {
      console.error(error)
    }
  }, [])

  // const getProjectDetails1 = useCallback(async () => {
  //   const promiseArray: any = pickPo?.map((item: any, index: number) => {
  //     return getDailyPRojectvalues(item)
  //   })
  //   const response = await Promise.all(promiseArray)
  //   let data: any = {}
  //   response.forEach((_, index: number) => {
  //     data[pickPo?.[index].po] = response[index]
  //     setProjectValueData(data)
  //   })
  // }, [pickPo, getDailyPRojectvalues])

  // useEffect(() => {
  //   if (pickPo) {
  //     getProjectDetails1()
  //   }
  // }, [pickPo])

  useEffect(() => {
    // getDetails()
    getPickPo()
  }, [isShowmore])

  return (
    <Styled.SubDailyLogContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={SubDailyLogSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, handleChange, resetForm, setFieldValue }) => {
          const getDailyProjectValue = async () => {
            try {
              const dataValue = pickPo?.filter((val: any) => val.po === getValueData)
              setFieldValue(`projects.${indexValue}.isTypeValue`, dataValue[0]?.type)
              setFieldValue(`projects.${indexValue}.oppPO`, dataValue[0]?.po)
              setFieldValue(`projects.${indexValue}.oppId`, dataValue[0]?.oppId)
            } catch (e) {
              console.warn(e)
            }
          }

          const poArray = values.projects?.filter((a) => a?.oppPO)

          useEffect(() => {
            if (poArray?.length && pickPo?.length) {
              ;(async () => {
                const promiseArray: any = poArray?.map((selected: any, index: number) => {
                  return getDailyPRojectvalues(allPoList?.filter((item: any) => item?.po === selected?.oppPO)[0])
                })
                const response = await Promise.all(promiseArray)

                setProjectValueData(response)

                // let data: any = {}
                // response.forEach((_, index: number) => {
                //   data[pickPo?.[index]?.po] = response[index]
                //   setProjectValueData(data)
                // })
              })()
            }
          }, [poArray?.length, pickPo?.length, allPoList?.length, getValueData])

          useEffect(() => {
            getDailyProjectValue()
          }, [getValueData, indexValue])

          let selectedPo: string[] = []

          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{action}</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setIsSubEditDailyLog?.(false)
                    setShowSubDailyLogModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    gap="10px"
                    width="100%"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    <SharedDate
                      value={values.date}
                      labelName="Date"
                      stateName="date"
                      setFieldValue={setFieldValue}
                      error={touched.date && errors.date ? true : false}
                    />
                    <CustomSelect
                      value={values.subContractorName}
                      setValue={() => {}}
                      labelName="Subcontractor"
                      showInitialValue
                      innerHeight="45px"
                      margin="10px 0 0 0"
                      stateName="subContractorName"
                      dropDownData={subDailyDropdownData.map((item: any) => item.split(' :-')[0])}
                      setFieldValue={setFieldValue}
                      error={touched.subContractorName && errors.subContractorName ? true : false}
                    />
                    <Styled.HeaderDiv marginTop="8px">Project</Styled.HeaderDiv>
                    <Styled.WorkDoneContainer name="projects">
                      {(fieldArrayProps: any) => {
                        const { push, remove } = fieldArrayProps
                        const handleProjectValue = (index: number, name: string, value: string) => {
                          setIndexValue(index)
                        }

                        return (
                          <>
                            {values.projects.map((work: any, index: number) => {
                              const data = getProjectValueData[index]

                              selectedPo.push(values?.projects[index].oppPO)
                              return (
                                <>
                                  <Styled.ProjectContainer marginTop="6px">
                                    <Styled.ProjectHeader>
                                      <Styled.ProjectSmallHeader>#{index + 1} PO:</Styled.ProjectSmallHeader>

                                      <CustomSelect
                                        error={false}
                                        value={values?.projects[index].oppPO ?? ''}
                                        setValue={setGetValueData}
                                        dropDownData={
                                          values?.projects[index].oppPO !== ''
                                            ? [values?.projects[index].oppPO].concat(
                                                pickPo
                                                  .filter((item: any) => !selectedPo?.includes(item?.po))
                                                  .map((values: any) => values?.po)
                                                  ?.sort((a: any, b: any) => a?.localeCompare(b))
                                              )
                                            : pickPo
                                                .filter((item: any) => !selectedPo?.includes(item?.po))
                                                .map((values: any) => values?.po)
                                                ?.sort((a: any, b: any) => a?.localeCompare(b))
                                        }
                                        setFieldValue={setFieldValue}
                                        innerHeight="45px"
                                        margin="10px 0 0 0"
                                        showInitialValue
                                        stateName={`projects.${index}.oppPO`}
                                        index={index}
                                        handleProjectValue={handleProjectValue}
                                        object={pickPo}
                                        isShowmore={isShowmore}
                                        showSeeMoreOption
                                        onAddClick={() => {
                                          setIsShowmore(true)
                                        }}
                                      />
                                    </Styled.ProjectHeader>
                                    {values?.projects[index].oppPO ? (
                                      <>
                                        <Styled.WorkHeader marginTop="8px" textAlign="center">
                                          Work Completed
                                        </Styled.WorkHeader>
                                        {values?.projects[index]?.isTypeValue ? (
                                          <>
                                            <Styled.WorkHeader marginTop="8px">
                                              <b>Tear Off:</b>
                                            </Styled.WorkHeader>
                                            {tearOff.map((tear: any, index1: number) => {
                                              return (
                                                <Styled.SingleFieldNameContainer>
                                                  {!showAllFieldsTearOff ? (
                                                    index1 < 4 ? (
                                                      <>
                                                        <Styled.NameValueUnitContainer
                                                          marginTop="10px"
                                                          className="grid"
                                                        >
                                                          <Styled.NameDiv paddingRight="10px">
                                                            {tear.name}:
                                                          </Styled.NameDiv>
                                                          <SharedStyled.FlexRow
                                                            gap="0"
                                                            alignItems="center"
                                                            style={{ justifySelf: 'center' }}
                                                          >
                                                            <Styled.ValueInput
                                                              name={`projects.${index}.${tear.codeName}`}
                                                              type="number"
                                                              onChange={(e: any) => {
                                                                const re = /^[0-9]*\.?[0-9]+$/
                                                                if (e.target.value === '' || re.test(e.target.value)) {
                                                                  handleChange(e)
                                                                }
                                                              }}
                                                              onWheel={handleWheel}
                                                              borderRadius="4px 0px 0px 4px"
                                                              validate={(value: any) => {
                                                                if (tear?.codeName !== 'tearOffSQ') return
                                                                const oldData =
                                                                  initialValues?.projects[index]?.tearOffSQ

                                                                if (
                                                                  !isSubEditDailyLog &&
                                                                  value + data?.doneTearSQ > data?.totalTearSQ
                                                                )
                                                                  return 'You have entered too many SQ done in Tear Off'

                                                                if (
                                                                  (isSubEditDailyLog &&
                                                                    value > (data?.totalTearSQ ?? 0)) ||
                                                                  (isSubEditDailyLog &&
                                                                    value < (data?.totalTearSQ ?? 0) &&
                                                                    (data?.doneTearSQ ?? 0) - oldData + value >
                                                                      (data?.totalTearSQ ?? 0))
                                                                ) {
                                                                  return 'You have entered too many SQ done in Tear Off'
                                                                }
                                                              }}
                                                            />
                                                            <Styled.UnitDiv>{tear.unit}</Styled.UnitDiv>
                                                          </SharedStyled.FlexRow>
                                                          <Styled.DoneDiv marginLeft="6px">
                                                            {Object.keys(tear.done).length > 1
                                                              ? `(${data?.[tear.done.name1] ?? 0}/${
                                                                  data?.[tear.done.name2] ?? 0
                                                                } done)`
                                                              : `(${data?.[tear.done.name] ?? 0} so far)`}
                                                          </Styled.DoneDiv>
                                                        </Styled.NameValueUnitContainer>
                                                        <ErrorContainer>
                                                          <ErrorMsg>
                                                            <ErrorMessage
                                                              name={`projects.${index}.${tear.codeName}`}
                                                              component="div"
                                                              className="error"
                                                            />
                                                          </ErrorMsg>
                                                        </ErrorContainer>
                                                      </>
                                                    ) : null
                                                  ) : (
                                                    <>
                                                      <Styled.NameValueUnitContainer
                                                        marginTop="10px"
                                                        className={'grid'}
                                                      >
                                                        <Styled.NameDiv paddingRight="10px">
                                                          {tear.name}:
                                                        </Styled.NameDiv>

                                                        <SharedStyled.FlexRow gap="0px">
                                                          <Styled.ValueInput
                                                            name={`projects.${index}.${tear.codeName}`}
                                                            type="number"
                                                            onChange={(e: any) => {
                                                              const re = /^[0-9]*\.?[0-9]+$/
                                                              if (e.target.value === '' || re.test(e.target.value)) {
                                                                handleChange(e)
                                                              }
                                                            }}
                                                            onWheel={handleWheel}
                                                            borderRadius="4px 0px 0px 4px"
                                                            validate={(value: any) => {
                                                              if (tear?.codeName !== 'tearOffSQ') return
                                                              const oldData = initialValues?.projects[index]?.tearOffSQ

                                                              if (
                                                                !isSubEditDailyLog &&
                                                                value + data?.doneTearSQ > data?.totalTearSQ
                                                              )
                                                                return 'You have entered too many SQ done in Tear Off'

                                                              if (
                                                                (isSubEditDailyLog &&
                                                                  value > (data?.totalTearSQ ?? 0)) ||
                                                                (isSubEditDailyLog &&
                                                                  value < (data?.totalTearSQ ?? 0) &&
                                                                  (data?.doneTearSQ ?? 0) - oldData + value >
                                                                    (data?.totalTearSQ ?? 0))
                                                              ) {
                                                                return 'You have entered too many SQ done in Tear Off'
                                                              }
                                                            }}
                                                          />
                                                          <Styled.UnitDiv>{tear.unit}</Styled.UnitDiv>
                                                        </SharedStyled.FlexRow>
                                                        <Styled.DoneDiv marginLeft="6px">
                                                          {Object.keys(tear.done).length > 1
                                                            ? `(${data?.[tear.done.name1] ?? 0}/${
                                                                data?.[tear.done.name2] ?? 0
                                                              } done)`
                                                            : `(${data?.[tear.done.name] ?? 0} so far)`}
                                                        </Styled.DoneDiv>
                                                      </Styled.NameValueUnitContainer>

                                                      <ErrorContainer>
                                                        <ErrorMsg>
                                                          <ErrorMessage
                                                            name={`projects.${index}.${tear.codeName}`}
                                                            component="div"
                                                            className="error"
                                                          />
                                                        </ErrorMsg>
                                                      </ErrorContainer>
                                                    </>
                                                  )}
                                                </Styled.SingleFieldNameContainer>
                                              )
                                            })}
                                            <p
                                              onClick={() => setShowAllFieldsTearOff(!showAllFieldsTearOff)}
                                              className="more"
                                            >
                                              {showAllFieldsTearOff ? 'Show Less' : 'Show More'}
                                            </p>
                                            <Styled.WorkHeader marginTop="8px">
                                              <b>Roofing:</b>
                                            </Styled.WorkHeader>

                                            {roofing.map((roofing: any, index1: number) => {
                                              return (
                                                <Styled.SingleFieldNameContainer>
                                                  {!showAllFieldsRoofing ? (
                                                    index1 < 1 && (
                                                      <>
                                                        <Styled.NameValueUnitContainer
                                                          marginTop="10px"
                                                          className="grid"
                                                        >
                                                          <Styled.NameDiv paddingRight="10px">
                                                            {roofing.name}:
                                                          </Styled.NameDiv>
                                                          <SharedStyled.FlexRow gap="0px">
                                                            <Styled.ValueInput
                                                              name={`projects.${index}.${roofing.codeName}`}
                                                              type="number"
                                                              onChange={(e: any) => {
                                                                const re = /^[0-9]*\.?[0-9]+$/
                                                                if (e.target.value === '' || re.test(e.target.value)) {
                                                                  handleChange(e)
                                                                }
                                                              }}
                                                              borderRadius="4px 0px 0px 4px"
                                                              validate={(value: any) => {
                                                                if (roofing?.codeName !== 'roofingSQ') return
                                                                const oldData =
                                                                  initialValues?.projects[index]?.roofingSQ

                                                                if (
                                                                  !isSubEditDailyLog &&
                                                                  value + data?.doneRoofSQ > data?.totalRoofSQ
                                                                )
                                                                  return 'You have entered too many SQ done in Roofing'

                                                                if (
                                                                  (isSubEditDailyLog &&
                                                                    value > (data?.totalRoofSQ ?? 0)) ||
                                                                  (isSubEditDailyLog &&
                                                                    value < (data?.totalRoofSQ ?? 0) &&
                                                                    (data?.doneRoofSQ ?? 0) - oldData + value >
                                                                      (data?.totalRoofSQ ?? 0))
                                                                ) {
                                                                  return 'You have entered too many SQ done in Roofing'
                                                                }
                                                              }}
                                                            />
                                                            <Styled.UnitDiv>{roofing.unit}</Styled.UnitDiv>
                                                          </SharedStyled.FlexRow>
                                                          <Styled.DoneDiv marginLeft="6px">
                                                            {Object.keys(roofing.done).length > 1
                                                              ? `(${
                                                                  Number(data?.[roofing.done?.name1]?.toFixed(2)) ?? 0
                                                                }/${data?.[roofing.done?.name2] ?? 0} done)`
                                                              : `(${data?.[roofing.done.name] ?? 0} so far)`}
                                                          </Styled.DoneDiv>
                                                        </Styled.NameValueUnitContainer>
                                                        <ErrorContainer>
                                                          <ErrorMsg>
                                                            <ErrorMessage
                                                              name={`projects.${index}.${roofing.codeName}`}
                                                              component="div"
                                                              className="error"
                                                            />
                                                          </ErrorMsg>
                                                        </ErrorContainer>
                                                      </>
                                                    )
                                                  ) : (
                                                    <>
                                                      <Styled.NameValueUnitContainer marginTop="10px" className="grid">
                                                        <Styled.NameDiv paddingRight="10px">
                                                          {roofing.name}:
                                                        </Styled.NameDiv>
                                                        <SharedStyled.FlexRow gap="0px">
                                                          <Styled.ValueInput
                                                            name={`projects.${index}.${roofing.codeName}`}
                                                            type="number"
                                                            onChange={(e: any) => {
                                                              const re = /^[0-9]*\.?[0-9]+$/
                                                              if (e.target.value === '' || re.test(e.target.value)) {
                                                                handleChange(e)
                                                              }
                                                            }}
                                                            borderRadius="4px 0px 0px 4px"
                                                            validate={(value: any) => {
                                                              if (roofing?.codeName !== 'roofingSQ') return
                                                              const oldData = initialValues?.projects[index]?.roofingSQ

                                                              if (
                                                                !isSubEditDailyLog &&
                                                                value + data?.doneRoofSQ > data?.totalRoofSQ
                                                              )
                                                                return 'You have entered too many SQ done in Roofing'

                                                              if (
                                                                (isSubEditDailyLog &&
                                                                  value > (data?.totalRoofSQ ?? 0)) ||
                                                                (isSubEditDailyLog &&
                                                                  value < (data?.totalRoofSQ ?? 0) &&
                                                                  (data?.doneRoofSQ ?? 0) - oldData + value >
                                                                    (data?.totalRoofSQ ?? 0))
                                                              ) {
                                                                return 'You have entered too many SQ done in Roofing'
                                                              }
                                                            }}
                                                          />
                                                          <Styled.UnitDiv>{roofing.unit}</Styled.UnitDiv>
                                                        </SharedStyled.FlexRow>
                                                        <Styled.DoneDiv marginLeft="6px">
                                                          {Object.keys(roofing.done).length > 1
                                                            ? `(${data?.[roofing.done?.name1] ?? 0}/${
                                                                data?.[roofing.done?.name2] ?? 0
                                                              } done)`
                                                            : `(${data?.[roofing.done.name] ?? 0} so far)`}
                                                        </Styled.DoneDiv>
                                                      </Styled.NameValueUnitContainer>

                                                      <ErrorContainer>
                                                        <ErrorMsg>
                                                          <ErrorMessage
                                                            name={`projects.${index}.${roofing.codeName}`}
                                                            component="div"
                                                            className="error"
                                                          />
                                                        </ErrorMsg>
                                                      </ErrorContainer>
                                                    </>
                                                  )}
                                                </Styled.SingleFieldNameContainer>
                                              )
                                            })}
                                            <p
                                              onClick={() => setShowAllFieldsRoofing(!showAllFieldsRoofing)}
                                              className="more"
                                            >
                                              {showAllFieldsRoofing ? 'Show Less' : 'Show More'}
                                            </p>
                                            <Styled.WorkHeader marginTop="8px">
                                              <b>Extras:</b>
                                            </Styled.WorkHeader>
                                            {replacementExtras.map((extra: any, index1: number) => {
                                              return (
                                                <Styled.SingleFieldNameContainer>
                                                  {!showAllFieldsExtras ? (
                                                    index1 < 3 && (
                                                      <Styled.NameValueUnitContainer marginTop="10px" className="grid">
                                                        <Styled.NameDiv paddingRight="10px">
                                                          {extra.name}:
                                                        </Styled.NameDiv>
                                                        <SharedStyled.FlexRow gap="0px">
                                                          <Styled.ValueInput
                                                            name={`projects.${index}.${extra.codeName}`}
                                                            type="number"
                                                            onChange={(e: any) => {
                                                              const re = /^[0-9]*\.?[0-9]+$/
                                                              if (e.target.value === '' || re.test(e.target.value)) {
                                                                handleChange(e)
                                                              }
                                                            }}
                                                            borderRadius="4px 0px 0px 4px"
                                                          />
                                                          <Styled.UnitDiv>{extra.unit}</Styled.UnitDiv>
                                                        </SharedStyled.FlexRow>
                                                        <Styled.DoneDiv marginLeft="6px">
                                                          {Object.keys(extra.done).length > 1
                                                            ? `(${data?.[extra.done?.name1] ?? 0}/${
                                                                data?.[extra.done?.name2] ?? 0
                                                              } done)`
                                                            : `(${data?.[extra.done.name] ?? 0} so far)`}
                                                        </Styled.DoneDiv>
                                                      </Styled.NameValueUnitContainer>
                                                    )
                                                  ) : (
                                                    <Styled.NameValueUnitContainer marginTop="10px" className="grid">
                                                      <Styled.NameDiv paddingRight="10px">{extra.name}:</Styled.NameDiv>
                                                      <SharedStyled.FlexRow gap="0px">
                                                        <Styled.ValueInput
                                                          name={`projects.${index}.${extra.codeName}`}
                                                          type="number"
                                                          onChange={(e: any) => {
                                                            const re = /^[0-9]*\.?[0-9]+$/
                                                            if (e.target.value === '' || re.test(e.target.value)) {
                                                              handleChange(e)
                                                            }
                                                          }}
                                                          borderRadius="4px 0px 0px 4px"
                                                        />
                                                        <Styled.UnitDiv>{extra.unit}</Styled.UnitDiv>
                                                      </SharedStyled.FlexRow>
                                                      <Styled.DoneDiv marginLeft="6px">
                                                        {Object.keys(extra.done).length > 1
                                                          ? `(${data?.[extra.done?.name1] ?? 0}/${
                                                              data?.[extra.done?.name2] ?? 0
                                                            } done)`
                                                          : `(${data?.[extra.done.name] ?? 0} so far)`}
                                                      </Styled.DoneDiv>
                                                    </Styled.NameValueUnitContainer>
                                                  )}
                                                </Styled.SingleFieldNameContainer>
                                              )
                                            })}
                                            <p
                                              onClick={() => setShowAllFieldsExtras(!showAllFieldsExtras)}
                                              className="more"
                                            >
                                              {showAllFieldsExtras ? 'Show Less' : 'Show More'}
                                            </p>
                                          </>
                                        ) : (
                                          <>
                                            <Styled.WorkHeader marginTop="8px">
                                              <b>Repair:</b>
                                            </Styled.WorkHeader>
                                            <Styled.SingleFieldNameContainer>
                                              <Styled.NameValueUnitContainer marginTop="10px" className="grid">
                                                <Styled.NameDiv paddingRight="10px">Percent Done:</Styled.NameDiv>
                                                <SharedStyled.FlexRow gap="0px">
                                                  <Styled.ValueInput
                                                    name={`projects.${index}.percentDone`}
                                                    type="number"
                                                    onChange={(e: any) => {
                                                      const re = /^[0-9]*\.?[0-9]+$/
                                                      if (e.target.value === '' || re.test(e.target.value)) {
                                                        handleChange(e)
                                                      }
                                                    }}
                                                    borderRadius="4px 0px 0px 4px"
                                                    onWheel={handleWheel}
                                                    defaultValue={initialValues?.projects[index]?.percentDone}
                                                    validate={(value: any) => {
                                                      const oldData = initialValues?.projects[index]?.percentDone
                                                      if (!isSubEditDailyLog && value + data?.percentDone > 100) {
                                                        return 'You have entered More than 100%'
                                                      }

                                                      if (
                                                        (isSubEditDailyLog && value > 100) ||
                                                        (isSubEditDailyLog &&
                                                          value < 100 &&
                                                          data?.percentDone - oldData + value > 100)
                                                      ) {
                                                        return 'You have entered More than 100%'
                                                      }
                                                    }}
                                                  />
                                                  <Styled.UnitDiv>%</Styled.UnitDiv>
                                                </SharedStyled.FlexRow>

                                                <Styled.DoneDiv marginLeft="6px">{`${
                                                  data?.percentDone ?? 0
                                                }% so far`}</Styled.DoneDiv>
                                              </Styled.NameValueUnitContainer>
                                            </Styled.SingleFieldNameContainer>

                                            <ErrorContainer>
                                              <ErrorMsg>
                                                <ErrorMessage
                                                  name={`projects.${index}.percentDone`}
                                                  component="div"
                                                  className="error"
                                                />
                                              </ErrorMsg>
                                            </ErrorContainer>

                                            <Styled.WorkHeader marginTop="8px">
                                              <b>Extras:</b>
                                            </Styled.WorkHeader>
                                            {nonReplacementExtras.map((extra: any, index1: number) => {
                                              return (
                                                <Styled.SingleFieldNameContainer>
                                                  <Styled.NameValueUnitContainer marginTop="10px" className="grid">
                                                    <Styled.NameDiv paddingRight="10px">{extra.name}:</Styled.NameDiv>
                                                    <SharedStyled.FlexRow gap="0px">
                                                      <Styled.ValueInput
                                                        name={`projects.${index}.${extra.codeName}`}
                                                        type="number"
                                                        onChange={(e: any) => {
                                                          const re = /^[0-9]*\.?[0-9]+$/
                                                          if (e.target.value === '' || re.test(e.target.value)) {
                                                            handleChange(e)
                                                          }
                                                        }}
                                                        borderRadius="4px 0px 0px 4px"
                                                      />
                                                      <Styled.UnitDiv>{extra.unit}</Styled.UnitDiv>
                                                    </SharedStyled.FlexRow>

                                                    <Styled.DoneDiv marginLeft="6px">
                                                      {Object.keys(extra.done).length > 1
                                                        ? `(${data?.[extra.done?.name1] ?? 0}/${
                                                            data?.[extra.done?.name2] ?? 0
                                                          } done)`
                                                        : `(${data?.[extra.done.name] ?? 0}% so far)`}{' '}
                                                      {/* %  */}
                                                    </Styled.DoneDiv>
                                                  </Styled.NameValueUnitContainer>
                                                </Styled.SingleFieldNameContainer>
                                              )
                                            })}
                                          </>
                                        )}
                                        <Styled.WorkMiniHeader marginTop="6px">
                                          Notes (on extra work, problems, or other issues):
                                        </Styled.WorkMiniHeader>
                                        <Styled.TextArea
                                          component="textarea"
                                          as={Field}
                                          name={`projects.${index}.notes`}
                                          marginTop="8px"
                                          height="50px"
                                        />
                                      </>
                                    ) : (
                                      <SharedStyled.FlexRow justifyContent="center" margin="20px 0 0 0">
                                        <h4>Please select PO</h4>
                                      </SharedStyled.FlexRow>
                                    )}{' '}
                                    <SharedStyled.FlexBox
                                      gap="5px"
                                      alignItems="center"
                                      justifyContent="flex-start"
                                      margin="14px 0"
                                    >
                                      {index > 0 && (
                                        <Button
                                          type="button"
                                          width="max-content"
                                          className="delete"
                                          onClick={() => remove(index)}
                                        >
                                          Remove Project
                                        </Button>
                                      )}
                                    </SharedStyled.FlexBox>
                                  </Styled.ProjectContainer>
                                </>
                              )
                            })}
                            <SharedStyled.FlexRow justifyContent="flex-start">
                              <Button type="button" width="max-content" onClick={() => push(subInitialProjData)}>
                                Add Project
                              </Button>
                            </SharedStyled.FlexRow>
                          </>
                        )
                      }}
                    </Styled.WorkDoneContainer>
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading} disabled={!poArray?.length}>
                        Save
                      </Button>
                      <Button
                        type="submit"
                        onClick={() => {
                          setIsSubEditDailyLog?.(false)
                          setShowSubDailyLogModal(false)
                        }}
                      >
                        Close
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.SubDailyLogContainer>
  )
}
