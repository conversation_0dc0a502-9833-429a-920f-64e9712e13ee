import { Field, Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { createTimeCard, deleteTimeCard } from '../../../../logic/apis/approveTimeCard'
import { getMemberTasks } from '../../../../logic/apis/task'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import {
  DAILY_LOG_STATUS,
  LAYERS,
  PITCHES,
  STATUS,
  StorageKey,
  TimeCardStatusEnum,
} from '../../../../shared/helpers/constants'
import {
  onlyMmDdYyyy,
  onlyNumber,
  onlyPositiveNumberRegex,
  onlyPositiveNumberWithTwoDecimalsRegex,
  onlyTextWithSpaces,
  onlyTextWithSpacesHypens,
  twoDecimal,
} from '../../../../shared/helpers/regex'
import {
  getDataFromLocalStorage,
  getFormattedDate,
  handleWheel,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { getOppClockInMobile } from '../../../../logic/apis/projects'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { getPieceWorkByTaskId } from '../../../../logic/apis/pieceWork'
import useWindowDimensions from '../../../../shared/hooks/useWindowDimensions'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  date: string
  project: string
  task: string
  allHourlyPayCheckbox: boolean
  removeFromLeadBonusCheckbox: boolean
  workDone: Array<any>
  extraTime: Object<any>
  notes: string
  managerNotes: string
  status: string
  timeIn: string
  timeOut: string
  pieceWork: any[]
}

interface I_TimeCardPopUp {
  setShowTimeCardPopUp: React.Dispatch<React.SetStateAction<boolean>>
  timeCardData: any
  setDataUpdate: React.Dispatch<React.SetStateAction<boolean>>
  setModifiedTimecardId?: React.Dispatch<React.SetStateAction<string>>
  setIsLoaded?: React.Dispatch<React.SetStateAction<boolean>>
  setIsAddTimeCard?: React.Dispatch<React.SetStateAction<boolean>>
}

export const removeDuplicates = (data: any[]) => {
  const uniqueItems: any[] = []

  data?.forEach((item) => {
    const duplicate = uniqueItems?.find(
      (existingItem) =>
        existingItem.id === item?.id &&
        existingItem.amount === item?.amount &&
        existingItem.layers === item?.layers &&
        existingItem.pitch === item?.pitch
    )

    if (!duplicate) {
      uniqueItems?.push(item)
    }
  })

  return uniqueItems
}

export const AddTimeCardPopUp = (props: I_TimeCardPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const { setShowTimeCardPopUp, timeCardData, setDataUpdate, setModifiedTimecardId, setIsLoaded, setIsAddTimeCard } =
    props
  const dateFromLocalStorage: any = getDataFromLocalStorage('currentDate')
  const [booleanFlag, setBooleanFlag] = useState(false)

  const [selectedPoName, setSelectedPoName] = useState('')
  const [selectedTask, setSelectedTask] = useState('')

  const [pieceworkByTaskId, setPieceworkByTaskId] = useState([])
  const [pieceworkLoading, setPieceworkLoading] = useState(false)
  const [allTaskData, setAllTaskData] = useState([])
  const [isHourly, setIsHourly] = useState(false)

  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    date: dateFromLocalStorage.replace(/^"(.*)"$/, '$1') ?? '',
    project: '',
    task: '',
    allHourlyPayCheckbox: false,
    removeFromLeadBonusCheckbox: false,
    workDone: [{ pitch: '', sqs: '', layers: '' }],
    pieceWork: pieceworkByTaskId?.length
      ? [
          {
            name: '',
            value: '',
          },
        ]
      : [],
    extraTime: {
      hours: '0',
      minutes: '0',
    },
    notes: '',
    managerNotes: '',
    status: 'Unapproved',
    timeIn: '',
    timeOut: '',
  })

  const [showExtra, setShowExtra] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [taskData, setTaskData] = useState<any>([])
  const [taskIdData, setTaskIdData] = useState<any>([])
  const [IdTaskData, setIdTaskData] = useState<any>([])
  const [poData, setPoData] = useState<any>([])
  const [pitches, setPitch] = useState([])
  const [layer, setLayer] = useState([])

  const { isMobile } = useWindowDimensions()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, positionDetails } = globalSelector.company

  const isPieceworkPosition = positionDetails?.pieceworkPositions?.includes(timeCardData?.positionId)

  const workDoneValidationSchema = Yup.object().shape({
    name: Yup.string()
      .optional()
      .when([], {
        is: (_val: string) => isPieceworkPosition,
        then: Yup.string().required('Required'),
      }),

    value: Yup.string()
      .matches(twoDecimal, 'Only numbers with upto two decimals are allowed')
      .matches(onlyPositiveNumberWithTwoDecimalsRegex, 'Enter number greater than zero')
      .when('name', {
        is: (val: string) => val !== 'Extra hours' && isPieceworkPosition,
        then: Yup.string().required('Required'),
      }),
    layers: Yup.string()
      .nullable()
      .when('name', {
        is: (val: string) =>
          pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.hasLayer && !!layer?.length,
        then: Yup.string().required('Required'),
      }),
    pitch: Yup.string().when('name', {
      is: (val: string) =>
        pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.usesPitch && !!pitches?.length,
      then: Yup.string().required('Required'),
    }),
  })

  const extraTimeValidationSchema = Yup.object().shape({
    hours: Yup.string().matches(onlyNumber, 'Enter Valid value'),
    minutes: Yup.string().matches(onlyNumber, 'Enter Valid value'),
  })

  /**
   * TimeCardPopUpSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const TimeCardPopUpSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyTextWithSpacesHypens, 'Enter Valid Name'),
    date: Yup.string().required('Required').matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
    project: Yup.string().required('Required'),
    task: Yup.string().required('Required'),
    timeIn: Yup.string().required('Required'),
    timeOut: Yup.string().required('Required'),
    pieceWork: isHourly ? Yup.array() : Yup.array().of(workDoneValidationSchema),
    // extraTime: Yup.array().of(extraTimeValidationSchema),
    extraTime: extraTimeValidationSchema,
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      const currentTime = new Date(new Date()?.toISOString())?.getTime()

      setLoading(true)

      const modifiedPieceWork = submittedValues?.pieceWork
        ?.filter((itm) => itm?.name !== 'Extra hours')
        ?.map((p) => {
          const selectedPiecework = pieceworkByTaskId?.find((pw: { name: string }) => pw?.name === p?.name)

          return {
            pitch:
              typeof p?.pitch === 'string' && !!pitches?.length && selectedPiecework?.usesPitch
                ? Number(p?.pitch?.split('/')[0]?.trim())
                : !!pitches?.length && selectedPiecework?.usesPitch
                ? p?.pitch
                : undefined,
            layers: p?.layers && layer?.length && selectedPiecework?.hasLayer ? Number(p?.layers) : undefined,
            amount: p?.value ? p?.value : p?.amount,
            id: selectedPiecework?._id,
            // name: p?.name,
            // unit: pieceworkByTaskId?.find((d: any) => d?.name === p?.name)?.unit,
          }
        })

      let currentTimeInDate = new Date(getFormattedDate(submittedValues.date))
      currentTimeInDate.setHours(Number(submittedValues.timeIn.split(':')[0]))
      currentTimeInDate.setMinutes(Number(submittedValues.timeIn.split(':')[1]))
      let currentTimeOutDate = new Date(getFormattedDate(submittedValues.date))
      currentTimeOutDate.setHours(Number(submittedValues.timeOut.split(':')[0]))
      currentTimeOutDate.setMinutes(Number(submittedValues.timeOut.split(':')[1]))
      let clockInTimeInSeconds = new Date(currentTimeInDate).getTime()
      let currentTimeInSeconds = new Date(currentTimeOutDate).getTime()
      let timeClockedIn = currentTimeInSeconds - clockInTimeInSeconds
      let hoursInSeconds = Number(submittedValues.extraTime.hours) * 60 * 60
      let minutesInSeconds = Number(submittedValues.extraTime.minutes) * 60
      // let hoursInSeconds = Number(submittedValues.extraTime[0].hours) * 60 * 60
      // let minutesInSeconds = Number(submittedValues.extraTime[0].minutes) * 60
      let totalExtraTimeInMilliseconds = (hoursInSeconds + minutesInSeconds) * 1000
      let diffBetweenClockedInAndExtraTime = timeClockedIn - totalExtraTimeInMilliseconds

      if (timeClockedIn < 0) {
        notify('Clock-in time must be before clock-out time', 'error')
        setLoading(false)
        return
      }

      if (currentTimeInSeconds > currentTime) {
        notify('Cannot add timecard in future', 'error')
        setLoading(false)
        return
      }

      if (diffBetweenClockedInAndExtraTime < 0) {
        notify('Cannot put extra time more than actual worked time!', 'error')
        setLoading(false)
        return
      }

      let hrsInMillisecs = Number(currentTimeOutDate) - Number(currentTimeInDate)

      let hours = Math.round((hrsInMillisecs / (1000 * 60 * 60)) * 100) / 100

      // if (!submittedValues.allHourlyPayCheckbox) {
      //   if (submittedValues.task === 'Repairs' || submittedValues.task === 'Office') {
      //     submittedValues.allHourlyPayCheckbox = true
      //   }
      // }
      // const hasWork = ['Tear Off', 'Roofing', 'Repairs']
      // let workDoneObj
      // const newData = submittedValues.workDone.map((item) => ({
      //   pitch: Number(item.pitch),
      //   sqs: Number(item.sqs ?? 0),
      //   layers: submittedValues.task === 'Tear Off' ? Number(item.layers) : undefined,
      // }))

      const isExtraTimeAdded = submittedValues?.pieceWork?.findIndex((itm) => itm?.name === 'Extra hours') !== -1

      let workDoneObj = {
        workDone: isPieceworkPosition ? removeDuplicates(modifiedPieceWork) : [],
        // extra: extras,
        extraTime: {
          extraHrs: isExtraTimeAdded ? Number(submittedValues.extraTime.hours) : 0,
          extraMin: isExtraTimeAdded ? Number(submittedValues.extraTime.minutes) : 0,
        },
        // extraTime: { extraHrs: submittedValues.extraTime[0].hours, extraMin: submittedValues.extraTime[0].minutes },
      }

      const dateValue = getFormattedDate(submittedValues.date)
      const customDateObj = new Date(dateValue)
      const formattedDate = customDateObj.toISOString()

      if (
        pieceworkByTaskId?.length &&
        !submittedValues?.allHourlyPayCheckbox &&
        !workDoneObj?.workDone?.length &&
        submittedValues?.timeOut &&
        !isExtraTimeAdded
      ) {
        notify('Please add piecework', 'error')
        setLoading(false)
        return
      }

      let dataObj: any = {
        memberId: timeCardData.memberId,
        projectId: poData
          ?.filter((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}` === submittedValues.project)
          .map((value: any) => String(value?._id))
          .join(','),
        projectPO: submittedValues.project,
        task: taskIdData[submittedValues.task],
        workTaskId: taskIdData[submittedValues.task],
        timeIn: currentTimeInDate,
        timeOut: currentTimeOutDate,
        allHourly: submittedValues.allHourlyPayCheckbox,
        // timeIn: `${submittedValues.date}T${submittedValues.timeIn}:00.000Z`,
        // timeOut: `${submittedValues.date}T${submittedValues.timeOut}:00.000Z`,
        status: TimeCardStatusEnum[submittedValues.status.replace(' ', '_')],
        date: startOfDate(submittedValues.date),
        hrs: hours,
        work: workDoneObj ? workDoneObj : [],
        notes: submittedValues.notes,
        managerNotes: submittedValues.managerNotes,
        removeLeadBonus: submittedValues.removeFromLeadBonusCheckbox,
      }

      const response = await createTimeCard(dataObj)

      if (isSuccess(response)) {
        notify('Created Timecard Successfully', 'success')
        setModifiedTimecardId?.(timeCardData.memberId)
        setLoading(false)
        setIsLoaded?.(true)
        setDataUpdate((prev) => !prev)
        resetForm()
        setShowTimeCardPopUp(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('handleSubmit error', error)
    }
  }

  const setInitialData = () => {
    try {
      setInitialValues({ ...initialValues, name: timeCardData.name })
    } catch (error) {
      console.error('setInitialData error', error)
    }
  }

  const getTasks = async () => {
    try {
      if (Object.keys(timeCardData).length > 0) {
        let taskResponse = await getMemberTasks({}, timeCardData.memberId)

        if (isSuccess(taskResponse)) {
          let workTasks = taskResponse?.data?.data?.workTask
          let taskObj: any = []
          let taskIdObj: any = {}
          let idTaskObj: any = {}

          workTasks.forEach((workTask: any) => {
            taskObj.push(workTask.name)
            taskIdObj = { ...taskIdObj, [`${workTask.name}`]: workTask._id }
            idTaskObj = { ...idTaskObj, [`${workTask._id}`]: workTask.name }
          })
          setTaskData(taskObj)
          setAllTaskData(workTasks)

          setTaskIdData(taskIdObj)
          setIdTaskData(idTaskObj)
        } else {
          notify(taskResponse?.data?.message, 'error')
        }
      }
    } catch (error) {
      console.error('getTasks error', error)
    }
  }

  useEffect(() => {
    setInitialData()
  }, [timeCardData])

  const getPO = async () => {
    try {
      let clockObj: any = []
      const result = await getOppClockInMobile()
      if (result?.data?.data?.readyOpps?.length) {
        result?.data?.data?.readyOpps.forEach((clockIn: any) => {
          clockObj.push(clockIn)
        })
        setPoData(clockObj)
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  useEffect(() => {
    getTasks()
    getPO()
  }, [timeCardData])

  const getValueByKey = (poValue: string) => {
    const result = poData.find((item: any) => `${item?.PO}${item?.num ? `-${item?.num}` : ''}` === poValue)
    return result ? result._id : ''
  }

  const fetchPitchLayer = async (Id: string) => {
    try {
      const res = poData?.find((itm: any) => itm?._id === Id)

      setPitch(res?.pitches?.filter(Boolean)?.length ? res?.pitches?.filter(Boolean) : PITCHES)
      setLayer(res?.layers?.filter(Boolean)?.length ? res?.layers?.filter(Boolean) : LAYERS)
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Styled.TimeCardPopUpContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={TimeCardPopUpSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          useEffect(() => {
            if (values.project !== '') {
              const Id = getValueByKey(values.project)
              fetchPitchLayer(Id)
            }
          }, [values.project])

          const hasErrors = errors && !!Object.keys(errors)?.length
          useEffect(() => {
            if (hasErrors) {
              const errorFields = Object.keys(errors) // Get all fields with errors

              for (let i = 0; i < errorFields?.length; i++) {
                const field = `${errorFields[i]}`
                const element = document.querySelector(`[name="${field}"]`)

                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                  return // Exit after scrolling to the first missing field within userInputData
                }
              }
            }
          }, [hasErrors, booleanFlag])

          useEffect(() => {
            if (taskIdData && values?.task && Object.values(taskIdData)?.length) {
              values.pieceWork = pieceworkByTaskId?.length ? [{ name: '', value: '' }] : []
              ;(async () => {
                try {
                  setPieceworkLoading(true)
                  const res = await getPieceWorkByTaskId({
                    taskId: taskIdData[values?.task],
                    date: startOfDate(values.date),
                    memberId: timeCardData.memberId,
                  })

                  const customPiecework = res?.data?.data?.pieceWorkSettings?.map((item: any) => ({
                    name: item?.name,
                    unit: item?.unit?.split('(')?.[0]?.trim(),
                    usesPitch: item?.usesPitch,
                    pitch: item?.pitch?.map((p: any) => ({ amount: p?.amount, pitch: p?.pitchOrder })),
                    _id: item?._id,
                    hasLayer: item?.hasLayer,
                    isExtra: item?.isExtra,
                    description: item?.description,
                  }))
                  setPieceworkByTaskId(customPiecework)
                } catch (error) {
                  console.error('error=====>', error)
                } finally {
                  setPieceworkLoading(false)
                }
              })()
            }
          }, [taskIdData, values?.task, pieceworkByTaskId?.length])

          useEffect(() => {
            setIsHourly(values?.allHourlyPayCheckbox)
          }, [values?.allHourlyPayCheckbox])
          const dropdownValues = pieceworkByTaskId?.map((item: any) => ({ ...item, value: '' }))
          dropdownValues?.push({ name: 'Extra hours' })
          const selectedPws = values?.pieceWork?.map((v) => v.name)

          return (
            <Styled.TimecardCont>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Add Time Card</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setIsAddTimeCard?.(false)
                    setShowTimeCardPopUp(false)
                    setSelectedPoName('')
                    setSelectedTask('')
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    gap="10px"
                    width="100%"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                      disabled={true}
                    />
                    <SharedDate
                      value={values.date}
                      labelName="Date"
                      stateName="date"
                      error={touched.date && errors.date ? true : false}
                      setFieldValue={setFieldValue}
                      disabled
                    />
                    {/* <CustomSelect
                      value={values.project}
                      labelName="PO"
                      stateName="project"
                      dropDownData={poData?.map((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}`)}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.project && errors.project ? true : false}
                    /> */}

                    <AutoComplete
                      value={values?.project}
                      labelName="PO"
                      stateName="project"
                      options={poData?.map((value: any) => `${value?.PO}${value?.num ? `-${value?.num}` : ''}`)}
                      dropdownHeight="300px"
                      borderRadius="0px"
                      validate
                      preSelected
                      setValueOnClick={(val: string) => {
                        setSelectedPoName(val)
                      }}
                      selectedValue={selectedPoName}
                      setFieldValue={setFieldValue}
                      error={touched.project && errors.project ? true : false}
                    />

                    <AutoComplete
                      value={values?.task}
                      labelName="Task"
                      stateName="task"
                      preSelected
                      options={taskData}
                      dropdownHeight="300px"
                      borderRadius="0px"
                      validate
                      setFieldValue={setFieldValue}
                      setValueOnClick={(val: string) => {
                        setSelectedTask(val)
                      }}
                      selectedValue={selectedTask}
                      error={touched.task && errors.task ? true : false}
                    />
                    {/* <CustomSelect
                      value={values.task}
                      labelName="Task"
                      stateName="task"
                      dropDownData={taskData}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.task && errors.task ? true : false}
                    /> */}
                    <InputWithValidation
                      labelName="Time In"
                      stateName="timeIn"
                      type="time"
                      error={touched.timeIn && errors.timeIn ? true : false}
                    />
                    <InputWithValidation
                      labelName="Time Out"
                      stateName="timeOut"
                      type="time"
                      error={touched.timeOut && errors.timeOut ? true : false}
                    />
                    <CustomSelect
                      value={values.status}
                      labelName="Status"
                      stateName="status"
                      dropDownData={DAILY_LOG_STATUS}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.status && errors.status ? true : false}
                    />

                    <>
                      {pieceworkLoading && isPieceworkPosition ? <SLoader height={100} /> : null}
                      {isPieceworkPosition ? (
                        <>
                          {allTaskData?.find((itm: any) => itm?.name === values?.task)?.pieceWork && (
                            <SharedStyled.FlexBox
                              width="100%"
                              alignItems="center"
                              gap="5px"
                              marginTop="6px"
                              justifyContent="flex-start"
                            >
                              <Styled.CheckBox width="15px" height="20px" type="checkbox" name="allHourlyPayCheckbox" />
                              <Styled.CheckBoxDescription>No Piece Work - all hourly pay</Styled.CheckBoxDescription>
                            </SharedStyled.FlexBox>
                          )}

                          {pieceworkByTaskId?.length && values.allHourlyPayCheckbox === false ? (
                            <div className="piecework">
                              <Styled.WorkDoneContainer name="pieceWork">
                                {(fieldArrayProps: any) => {
                                  const { push, remove } = fieldArrayProps

                                  return (
                                    <>
                                      {values.pieceWork.map((work: any, index: number) => {
                                        return (
                                          <Fragment key={index}>
                                            <SharedStyled.FlexRow alignItems="flex-start">
                                              <CustomSelect
                                                value={
                                                  work.name
                                                    ? work?.name
                                                    : dropdownValues?.filter(
                                                        (item) => !selectedPws?.includes(item?.name)
                                                      )?.length
                                                    ? dropdownValues
                                                        ?.filter((item) => !selectedPws?.includes(item?.name))
                                                        ?.map((t) => t.name)[0]
                                                    : ''
                                                }
                                                labelName="Piecework"
                                                setValue={() => {}}
                                                margin="10px 0 0 0"
                                                showInitialValue
                                                maxWidth="160px"
                                                isPieceWork
                                                toolTipText={
                                                  dropdownValues?.find((item) => item?.name === work?.name)?.description
                                                }
                                                stateName={`pieceWork.${index}.name`}
                                                dropDownData={dropdownValues
                                                  ?.filter(
                                                    (item) =>
                                                      !selectedPws?.includes(item?.name) ||
                                                      item?.usesPitch ||
                                                      item?.hasLayer
                                                  )
                                                  ?.map((t) => t.name)}
                                                setFieldValue={setFieldValue}
                                                error={errors?.pieceWork?.[index]?.name ? true : false}
                                              />
                                              <SharedStyled.FlexRow alignItems="flex-start">
                                                <SharedStyled.FlexRow alignItems="flex-start">
                                                  {work?.name !== 'Extra hours' ? (
                                                    <InputWithValidation
                                                      labelName={
                                                        dropdownValues?.find(
                                                          (itm) => itm?.name === values?.pieceWork[index].name
                                                        )?.unit ?? 'Select piecework'
                                                      }
                                                      defaultValue={work?.amount}
                                                      minWidth="80px"
                                                      forceType="number"
                                                      stateName={`pieceWork.${index}.value`}
                                                      error={errors?.pieceWork?.[index]?.value ? true : false}
                                                    />
                                                  ) : (
                                                    <Styled.NameValueUnitContainer
                                                      justifyContent="flex-start"
                                                      width="100%"
                                                      marginTop="8px"
                                                      flexWrap="wrap"
                                                      gap="0px"
                                                      style={{ justifyContent: 'flex-start' }}
                                                      className="extra-container"
                                                    >
                                                      <SharedStyled.FlexBox alignItems="center">
                                                        <Styled.ValueInput type="number" name={`extraTime.hours`} />
                                                        <Styled.UnitDiv>HR</Styled.UnitDiv>
                                                      </SharedStyled.FlexBox>
                                                      <SharedStyled.FlexBox alignItems="center">
                                                        <Styled.ValueInput
                                                          type="number"
                                                          marginLeft="8px"
                                                          name={`extraTime.minutes`}
                                                        />
                                                        <Styled.UnitDiv>MIN</Styled.UnitDiv>
                                                      </SharedStyled.FlexBox>
                                                    </Styled.NameValueUnitContainer>
                                                  )}
                                                  {!!dropdownValues?.find(
                                                    (itm) => itm?.name === values?.pieceWork[index].name
                                                  )?.usesPitch &&
                                                    !!pitches?.length && (
                                                      <CustomSelect
                                                        value={
                                                          !isNaN(work?.pitch)
                                                            ? `${work.pitch}/12`
                                                            : work.pitch
                                                            ? work?.pitch
                                                            : pitches?.map((item: any) => `${item}/12`)[0]
                                                        }
                                                        labelName="Pitch"
                                                        setValue={() => {}}
                                                        margin="10px 0 0 0"
                                                        showInitialValue
                                                        isPieceWork
                                                        isPitch
                                                        stateName={`pieceWork.${index}.pitch`}
                                                        dropDownData={pitches?.map((item: any) => `${item}/12`)}
                                                        setFieldValue={setFieldValue}
                                                        error={errors?.pieceWork?.[index]?.pitch ? true : false}
                                                      />
                                                    )}

                                                  {
                                                    // values.task === 'Tear Off' &&
                                                    !!dropdownValues?.find(
                                                      (itm) => itm?.name === values?.pieceWork[index].name
                                                    )?.hasLayer &&
                                                      !!layer?.length && (
                                                        <CustomSelect
                                                          value={work.layers ?? layer[0]}
                                                          labelName="Layers"
                                                          setValue={() => {}}
                                                          margin="10px 0 0 0"
                                                          showInitialValue
                                                          isPieceWork
                                                          stateName={`pieceWork.${index}.layers`}
                                                          dropDownData={layer}
                                                          setFieldValue={setFieldValue}
                                                          error={errors?.pieceWork?.[index]?.layers ? true : false}
                                                        />
                                                      )
                                                  }
                                                </SharedStyled.FlexRow>
                                                <SharedStyled.FlexRow margin="14px 0 0 0" width="max-content">
                                                  <Button
                                                    type="button"
                                                    width="max-content"
                                                    className="delete"
                                                    padding="7px 14px"
                                                    onClick={() => remove(index)}
                                                    // style={{ visibility: index > 0 ? 'visible' : 'hidden' }}
                                                  >
                                                    -
                                                  </Button>
                                                </SharedStyled.FlexRow>
                                              </SharedStyled.FlexRow>
                                            </SharedStyled.FlexRow>
                                            {isMobile ? (
                                              <SharedStyled.HorizontalDivider
                                                margin="14px 0 0 0"
                                                bg="rgba(0, 0, 0, 0.6)"
                                              />
                                            ) : null}
                                          </Fragment>
                                        )
                                      })}
                                      {values.pieceWork?.length + 1 <= dropdownValues?.length ? (
                                        <SharedStyled.FlexRow>
                                          <Button
                                            type="button"
                                            width="max-content"
                                            onClick={() => push({ name: '', value: '' })}
                                          >
                                            Add New
                                          </Button>
                                        </SharedStyled.FlexRow>
                                      ) : null}
                                    </>
                                  )
                                }}
                              </Styled.WorkDoneContainer>
                            </div>
                          ) : null}
                        </>
                      ) : null}
                    </>

                    <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Manager Notes:
                    </Styled.LabelDiv>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="managerNotes"
                      $marginTop="8px"
                      height="100px"
                    ></Styled.TextArea>
                    <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="notes"
                      $marginTop="8px"
                      height="100px"
                    ></Styled.TextArea>

                    <SharedStyled.FlexBox
                      width="100%"
                      alignItems="center"
                      gap="5px"
                      marginTop="6px"
                      justifyContent="flex-start"
                    >
                      <Styled.CheckBox width="15px" height="20px" type="checkbox" name="removeFromLeadBonusCheckbox" />
                      <Styled.CheckBoxDescription>Remove from Lead Bonus</Styled.CheckBoxDescription>
                    </SharedStyled.FlexBox>

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button
                        type="submit"
                        isLoading={loading}
                        onClick={() => {
                          setBooleanFlag((prev) => !prev)
                        }}
                      >
                        Add Time Card
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </Styled.TimecardCont>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Styled.TimeCardPopUpContainer>
  )
}
