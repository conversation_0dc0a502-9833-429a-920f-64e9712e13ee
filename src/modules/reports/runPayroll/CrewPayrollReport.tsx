import { Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'

import { BoldText, FormCont, PayrollReportCont } from './style'
import { dayjsFormat, getHoursAndMinutes } from '../../../shared/helpers/util'
import { FlexCol, FlexRow } from '../../../styles/styled'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import PriceModal, { ISelecctedData } from './components/PriceModal'
import { SharedDate } from '../../../shared/date/SharedDate'
import Button from '../../../shared/components/button/Button'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import { getCrewPayRollReport } from '../../../logic/apis/report'
import { ICrewPayrollReport } from './interfaces/crewPayrolInterface'
import Timecard from '../../../shared/timecard/Timecard'
import { EditTimeCardPopUp } from '../../timeCard/components/editTimeCardPopUp/EditTimeCardPopUp'
import { createCompanyName } from '../../app/Newsidebar/Sidebar'

const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
  const params = new URLSearchParams()
  params.append('startDate', selectedStartDate)
  params.append('endDate', selectedEndDate)
  const newURL = `${window.location.pathname}?${params.toString()}`
  window.history.pushState({}, '', newURL)
}

const CrewPayrollReport = () => {
  const [tableName, setTableName] = useState<{ [key: string]: boolean }>({})
  const [selectedData, setSelectedData] = useState<Partial<ISelecctedData>>({})
  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [_bool, setBool] = useState(false)
  const [payRollData, setPayRollData] = useState<ICrewPayrollReport>()
  const [buttonCall, setbuttonCall] = useState(false)
  const [showEditTimeCardPopUp, setShowEditTimeCardPopUp] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [timeCardData, setTimeCardData] = useState<any>({})

  const [selectedPoHour, setSelectedPoHour] = useState<{ [key: string]: boolean }>({})
  const [selectedTtlHour, setSelectedTtlHour] = useState<{ [key: string]: boolean }>({})
  const [selectedNestedHour, setSelectedNestedHour] = useState<{ [key: string]: boolean }>({})
  const [selectedNestedTtlHour, setSelectedNestedTtlHour] = useState<{ [key: string]: boolean }>({})

  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })

  const [editPopUpValues, setEditPopUpValues] = useState<any>({})
  const companyName = createCompanyName(currentCompany?.companyName)
  const [loading, setLoading] = useState(false)
  const [showPriceModal, setShowPriceModal] = useState(false)

  const onEditingTimeCard = (timeCard: any, name: string, date?: string) => {
    try {
      setTimeCardData(timeCard)
      let timeCardDate = date

      let editDataObj = {
        name: name,
        date: timeCardDate,
        allHourlyPayCheckbox: timeCard.hasOwnProperty('allHourly') ? timeCard.allHourly : false,
        project: timeCard.projectPO,
        task: timeCard.task.replace('_', ' '),
        taskName: timeCard?.taskName,
        timeIn: getHoursAndMinutes(timeCard.timeIn),
        timeOut: getHoursAndMinutes(timeCard.timeOut),
        status: timeCard.status.replace('_', ' '),
        workDone: timeCard?.work?.work?.workDone,
        extraTime: [
          {
            hours: timeCard?.work?.work?.extraTime?.extraHrs,
            minutes: timeCard?.work?.work?.extraTime?.extraMin,
          },
        ],
        notes: timeCard?.work?.notes,
        managerNotes: timeCard?.work?.managerNotes,
        removeFromLeadBonusCheckbox: timeCard?.work?.removeLeadBonus,
      }

      setEditPopUpValues({ ...editPopUpValues, ...editDataObj })
      setShowEditTimeCardPopUp(true)
    } catch (error) {
      console.log('onEditingTimeCard', error)
    }
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate && paramEndDate && currentCompany?._id) {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleSubmitForm({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search, currentCompany, dataUpdate])

  const handleSubmitForm = async (values: any) => {
    handleDateSelection(values.startDate, values.endDate)
    setBool((prev) => !prev)

    setLoading(true)
    try {
      const res = await getCrewPayRollReport({
        startDate: values.startDate,
        endDate: values.endDate,
      })

      setPayRollData(res?.data?.data?.crewPayrollReport)
    } catch (error) {
      console.error('getCrewPayRollReport Error=====>', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <PayrollReportCont>
        <BoldText className="heading">
          Crew Payroll Report{' '}
          {initialValues?.startDate && initialValues?.endDate ? (
            <>
              for {dayjsFormat(initialValues?.startDate, 'MM/DD/YYYY')} -{' '}
              {dayjsFormat(initialValues?.endDate, 'MM/DD/YYYY')}
            </>
          ) : null}
        </BoldText>

        <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
          {({ values, setFieldValue, touched, errors }) => (
            <FlexCol margin="0 0 30px 0">
              <Form>
                <BoldText className="medium">Date range</BoldText>
                <FormCont>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                  <p>to</p>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    setFieldValue={setFieldValue}
                  />

                  <FlexRow margin="8px 0 0 0">
                    <Button width="max-content" height="52px" isLoading={loading} onClick={() => setbuttonCall(true)}>
                      Get Report
                    </Button>
                  </FlexRow>
                </FormCont>
              </Form>
            </FlexCol>
          )}
        </Formik>

        <>
          {payRollData && Object.values(payRollData)?.length && (
            <>
              <BoldText className="underline medium">All Crew Members</BoldText>

              <FlexCol margin="20px 0" gap="4px">
                <BoldText>Total Hours: {payRollData?.totalHours?.toFixed(2)}</BoldText>
                <BoldText>Total Earnings: ${payRollData?.totalEarned?.toFixed(2)}</BoldText>
                <p>Company Hours: {payRollData?.defaultPOHours?.toFixed(2)}</p>
                <p>Company Earnings: ${payRollData?.defaultPOEarned?.toFixed(2)}</p>
                <p>Project Hours: {payRollData?.totalPOHours?.toFixed(2)}</p>
                <p>Project Earnings: ${payRollData?.totalPOEarned?.toFixed(2)}</p>
              </FlexCol>
            </>
          )}
        </>

        {!loading &&
          payRollData?.crewData?.map((crewPayrollData) => (
            <FlexCol key={crewPayrollData?.crewId}>
              <>
                <BoldText className="underline medium">Crew: {crewPayrollData?.crewName}</BoldText>
                <FlexCol margin="20px 0" gap="4px">
                  <BoldText>Crew Total Hours: {crewPayrollData?.totalCrewHours?.toFixed(2)}</BoldText>
                  <BoldText>Crew Total Earnings: ${crewPayrollData?.totalCrewEarned?.toFixed(2)}</BoldText>
                  <p>Crew Company Hours: {crewPayrollData?.companyP0Hours?.toFixed(2)}</p>
                  <p>Crew Company Earnings: ${crewPayrollData?.companyP0Earned?.toFixed(2)}</p>
                  <p>Crew Project Hours: {crewPayrollData?.projectP0Hours?.toFixed(2)}</p>
                  <p>Crew Project Earnings: ${crewPayrollData?.projectP0Earned?.toFixed(2)}</p>
                </FlexCol>
              </>
              <table className="full-width">
                <thead>
                  <tr className="row-heading">
                    <th>Name</th>
                    <th>Earned</th>
                    <th>Ttl Hrs</th>
                    <th>Co. Hrs</th>
                  </tr>
                </thead>
                <tbody>
                  {crewPayrollData?.crewMembers?.map((crewMemberData, idx) => (
                    <Fragment key={idx}>
                      <tr key={crewMemberData?.memberId} className="member">
                        <td
                          className="pointer"
                          onClick={() => {
                            setTableName((prevState) => ({
                              ...prevState,
                              [crewMemberData?.memberName + idx]: !prevState[crewMemberData?.memberName + idx],
                            }))
                          }}
                        >
                          {tableName[crewMemberData?.memberName + idx] ? '▼ ' : '▶ '}

                          {crewMemberData?.memberName}
                        </td>
                        <td
                          className="pointer"
                          onClick={() => {
                            setSelectedData({
                              totalCrewEarned: crewMemberData?.memberTotal?.earned,
                              ...crewMemberData?.memberTotal?.breakdown,
                            })
                            setShowPriceModal(true)
                          }}
                        >
                          {crewMemberData?.memberTotal?.earned
                            ? `$${crewMemberData?.memberTotal?.earned?.toFixed(2)}`
                            : '---'}
                        </td>
                        <td
                          onClick={() => {
                            setSelectedPoHour((prevState) => ({
                              ...prevState,
                              [crewMemberData?.memberId]: false,
                            }))
                            setSelectedTtlHour((prevState) => ({
                              ...prevState,
                              [crewMemberData?.memberId]: !prevState[crewMemberData?.memberId],
                            }))
                          }}
                          className={selectedTtlHour[crewMemberData?.memberId] ? 'active pointer' : 'pointer'}
                          style={{ pointerEvents: crewMemberData?.memberTotal?.hours ? 'inherit' : 'none' }}
                        >
                          {crewMemberData?.memberTotal?.hours ? crewMemberData?.memberTotal?.hours?.toFixed(2) : '---'}
                        </td>

                        <td
                          onClick={() => {
                            setSelectedPoHour((prevState) => ({
                              ...prevState,
                              [crewMemberData?.memberId]: !prevState[crewMemberData?.memberId],
                            }))

                            setSelectedTtlHour((prevState) => ({
                              ...prevState,
                              [crewMemberData?.memberId]: false,
                            }))
                          }}
                          className={selectedPoHour[crewMemberData?.memberId] ? 'active pointer' : 'pointer'}
                          style={{ pointerEvents: crewMemberData?.defaultPO?.hours ? 'inherit' : 'none' }}
                        >
                          {crewMemberData?.defaultPO?.hours ? crewMemberData?.defaultPO?.hours?.toFixed(2) : '---'}
                        </td>
                      </tr>

                      {selectedPoHour[crewMemberData?.memberId] && (
                        <tr>
                          <td colSpan={4}>
                            <div className="nested">
                              {crewMemberData?.defaultPO?.timecards?.map((cardData) => (
                                <Timecard
                                  timeCard={cardData}
                                  isReport
                                  key={cardData?._id!}
                                  onTimeCardClick={(data) => {
                                    onEditingTimeCard(
                                      data?.timeCard,
                                      crewMemberData?.memberName,
                                      dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                    )
                                  }}
                                />
                              ))}
                            </div>
                          </td>
                        </tr>
                      )}
                      {selectedTtlHour[crewMemberData?.memberId] && (
                        <tr>
                          <td colSpan={4}>
                            <div className="nested">
                              {crewMemberData?.allTimeCards?.map((cardData) => (
                                <Timecard
                                  timeCard={cardData}
                                  isReport
                                  key={cardData?._id}
                                  onTimeCardClick={(data) => {
                                    onEditingTimeCard(
                                      data?.timeCard,
                                      crewMemberData?.memberName,
                                      dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                    )
                                  }}
                                />
                              ))}
                            </div>
                          </td>
                        </tr>
                      )}

                      {tableName[crewMemberData?.memberName + idx] &&
                        crewMemberData?.dayData?.map((dayData, index) => (
                          <Fragment key={dayData?.date + index}>
                            <tr key={index}>
                              <td className="date">{dayjsFormat(dayData?.date, 'MM/DD dddd')}</td>
                              <td
                                className={dayData?.ptoUsed || dayData?.dayOff ? '' : 'pointer'}
                                onClick={() => {
                                  if (dayData?.ptoUsed || dayData?.dayOff || !dayData?.earned) {
                                    return
                                  }
                                  setSelectedData({
                                    totalCrewEarned: dayData?.earned,
                                    ...dayData?.breakdown,
                                  })
                                  setShowPriceModal(true)
                                }}
                              >
                                {dayData?.ptoUsed
                                  ? 'PTO'
                                  : dayData?.dayOff
                                  ? 'Day Off'
                                  : dayData?.earned
                                  ? `$${dayData?.earned?.toFixed(2)}`
                                  : '---'}
                              </td>
                              <td
                                onClick={() => {
                                  setSelectedNestedHour((prevState) => ({
                                    ...prevState,
                                    [crewMemberData?.memberId + dayData?.date]: false,
                                  }))
                                  setSelectedNestedTtlHour((prevState) => ({
                                    ...prevState,
                                    [crewMemberData?.memberId + dayData?.date]:
                                      !prevState[crewMemberData?.memberId + dayData?.date],
                                  }))
                                }}
                                className={
                                  selectedNestedTtlHour[crewMemberData?.memberId + dayData?.date]
                                    ? 'active pointer'
                                    : 'pointer'
                                }
                                style={{ pointerEvents: dayData?.hours ? 'inherit' : 'none' }}
                              >
                                {dayData?.hours ? dayData?.hours?.toFixed(2) : '---'}
                              </td>
                              <td
                                onClick={() => {
                                  setSelectedNestedTtlHour((prevState) => ({
                                    ...prevState,
                                    [crewMemberData?.memberId + dayData?.date]: false,
                                  }))
                                  setSelectedNestedHour((prevState) => ({
                                    ...prevState,
                                    [crewMemberData?.memberId + dayData?.date]:
                                      !prevState[crewMemberData?.memberId + dayData?.date],
                                  }))
                                }}
                                className={
                                  selectedNestedHour[crewMemberData?.memberId + dayData?.date]
                                    ? 'active pointer'
                                    : 'pointer'
                                }
                                style={{ pointerEvents: dayData?.poHours ? 'inherit' : 'none' }}
                              >
                                {dayData?.poHours ? dayData?.poHours?.toFixed(2) : '---'}
                              </td>
                            </tr>

                            {selectedNestedHour[crewMemberData?.memberId + dayData?.date] && (
                              <tr>
                                <td colSpan={4}>
                                  <div className="nested">
                                    {dayData?.defaultPoTimeCards?.map((cardData) => (
                                      <Timecard
                                        timeCard={cardData}
                                        isReport
                                        key={cardData?._id}
                                        onTimeCardClick={(data) => {
                                          onEditingTimeCard(
                                            data?.timeCard,
                                            crewMemberData?.memberName,
                                            dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                          )
                                        }}
                                      />
                                    ))}
                                  </div>
                                </td>
                              </tr>
                            )}
                            {selectedNestedTtlHour[crewMemberData?.memberId + dayData?.date] && (
                              <tr>
                                <td colSpan={4}>
                                  <div className="nested">
                                    {dayData?.allTimeCards?.map((cardData) => (
                                      <Timecard
                                        timeCard={cardData}
                                        isReport
                                        key={cardData?._id}
                                        onTimeCardClick={(data) => {
                                          onEditingTimeCard(
                                            data?.timeCard,
                                            crewMemberData?.memberName,
                                            dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                          )
                                        }}
                                      />
                                    ))}
                                  </div>
                                </td>
                              </tr>
                            )}
                          </Fragment>
                        ))}
                    </Fragment>
                  ))}
                </tbody>
              </table>
            </FlexCol>
          ))}
      </PayrollReportCont>

      <CustomModal show={showEditTimeCardPopUp} className="top timecard">
        {showEditTimeCardPopUp && (
          <EditTimeCardPopUp
            setShowTimeCardPopUp={setShowEditTimeCardPopUp}
            editPopUpValues={editPopUpValues}
            timeCardData={timeCardData}
            setDataUpdate={setDataUpdate}
          />
        )}
      </CustomModal>

      <CustomModal show={showPriceModal} toggleModal={() => setShowPriceModal(false)}>
        <PriceModal setShowPriceModal={setShowPriceModal} selectedData={selectedData} />
      </CustomModal>
    </>
  )
}

export default CrewPayrollReport
