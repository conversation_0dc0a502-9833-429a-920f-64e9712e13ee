import { Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'

import { BoldText, FormCont, PayrollReportCont } from './style'
import { FlexCol, FlexRow } from '../../../styles/styled'
import { SharedDate } from '../../../shared/date/SharedDate'
import Button from '../../../shared/components/button/Button'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import { getNonCrewPayRollReport } from '../../../logic/apis/report'
import { dayjsFormat, getHoursAndMinutes, hasValues } from '../../../shared/helpers/util'
import { NonCrewPayrollResponse } from './interfaces/nonCrewPayrolInterface'
import Timecard from '../../../shared/timecard/Timecard'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { EditTimeCardPopUp } from '../../timeCard/components/editTimeCardPopUp/EditTimeCardPopUp'

export const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
  const params = new URLSearchParams()
  params.append('startDate', selectedStartDate)
  params.append('endDate', selectedEndDate)
  const newURL = `${window.location.pathname}?${params.toString()}`
  window.history.pushState({}, '', newURL)
}

const NoncrewPayroll = () => {
  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })

  const [showEditTimeCardPopUp, setShowEditTimeCardPopUp] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [timeCardData, setTimeCardData] = useState<any>({})
  const [editPopUpValues, setEditPopUpValues] = useState<any>()

  const [tableName, setTableName] = useState<{ [key: string]: boolean }>({})
  const [selectedHour, setSelectedHour] = useState<{ [key: string]: boolean }>({})

  const [buttonCall, setbuttonCall] = useState(false)
  const [loading, setLoading] = useState(false)
  const [noncrewPayRollData, setNoncrewPayRollData] = useState<NonCrewPayrollResponse>()
  const globalSelector = useAppSelector((state: any) => state)

  const onEditingTimeCard = (timeCard: any, name: string, date?: string) => {
    try {
      setTimeCardData(timeCard)
      let timeCardDate = date

      let editDataObj = {
        name: name,
        date: timeCardDate,
        allHourlyPayCheckbox: timeCard.hasOwnProperty('allHourly') ? timeCard.allHourly : false,
        project: timeCard.projectPO,
        task: timeCard.task.replace('_', ' '),
        taskName: timeCard?.taskName,
        timeIn: getHoursAndMinutes(timeCard.timeIn),
        timeOut: getHoursAndMinutes(timeCard.timeOut),
        status: timeCard.status.replace('_', ' '),
        workDone: timeCard?.work?.work?.workDone,
        extraTime: [
          {
            hours: timeCard?.work?.work?.extraTime?.extraHrs,
            minutes: timeCard?.work?.work?.extraTime?.extraMin,
          },
        ],
        notes: timeCard?.work?.notes,
        managerNotes: timeCard?.work?.managerNotes,
        removeFromLeadBonusCheckbox: timeCard?.work?.removeLeadBonus,
      }

      setEditPopUpValues({ ...editPopUpValues, ...editDataObj })
      setShowEditTimeCardPopUp(true)
    } catch (error) {
      console.log('onEditingTimeCard', error)
    }
  }

  const { currentCompany } = globalSelector.company

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate && paramEndDate && currentCompany?._id) {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleSubmitForm({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search, currentCompany, dataUpdate])

  const handleSubmitForm = async (values: any) => {
    handleDateSelection(values.startDate, values.endDate)
    // setBool((prev) => !prev)

    setLoading(true)
    try {
      const res = await getNonCrewPayRollReport({
        startDate: values.startDate,
        endDate: values.endDate,
      })
      setNoncrewPayRollData(res?.data?.data?.nonCrewReport)
    } catch (error) {
      console.error('getCrewPayRollReport Error=====>', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <PayrollReportCont>
      <BoldText className="heading">
        Non Crew Payroll Report{' '}
        {initialValues?.startDate && initialValues?.endDate ? (
          <>
            for {dayjsFormat(initialValues?.startDate, 'MM/DD/YYYY')} -{' '}
            {dayjsFormat(initialValues?.endDate, 'MM/DD/YYYY')}
          </>
        ) : null}
      </BoldText>

      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <FlexCol margin="0 0 30px 0">
            <Form>
              <BoldText className="medium">Date range</BoldText>
              <FormCont>
                <SharedDate
                  value={values.startDate}
                  labelName="From"
                  stateName="startDate"
                  error={touched.startDate && errors.startDate ? true : false}
                  setFieldValue={setFieldValue}
                />
                <p>to</p>
                <SharedDate
                  value={values.endDate}
                  labelName="To"
                  stateName="endDate"
                  error={touched.endDate && errors.endDate ? true : false}
                  setFieldValue={setFieldValue}
                />

                <FlexRow margin="8px 0 0 0">
                  <Button width="max-content" height="52px" isLoading={loading} onClick={() => setbuttonCall(true)}>
                    Get Report
                  </Button>
                </FlexRow>
              </FormCont>
            </Form>
          </FlexCol>
        )}
      </Formik>

      {hasValues(noncrewPayRollData) ? (
        <>
          <FlexRow margin="0 0 20px 0">
            <BoldText>Total Hours: {noncrewPayRollData?.totalHours?.toFixed(2)}</BoldText>
          </FlexRow>

          <table className="full-width">
            <thead>
              <tr className="row-heading">
                <th>Name</th>
                <th>Hrs</th>
              </tr>
            </thead>
            <tbody>
              {noncrewPayRollData?.members?.map((crewMemberData, idx) => (
                <Fragment key={crewMemberData?.name + idx}>
                  <tr key={crewMemberData?.name} className="member">
                    <td
                      className="pointer"
                      onClick={() => {
                        setTableName((prevState) => ({
                          ...prevState,
                          [crewMemberData?.name + idx]: !prevState[crewMemberData?.name + idx],
                        }))
                      }}
                    >
                      {tableName[crewMemberData?.name + idx] ? '▼ ' : '▶ '}

                      {crewMemberData?.name}
                    </td>

                    <td>{crewMemberData?.totalHours ? crewMemberData?.totalHours?.toFixed(2) : '---'}</td>
                  </tr>

                  {tableName[crewMemberData?.name + idx] &&
                    crewMemberData?.dates?.map((dayData, index) => (
                      <Fragment key={dayData?.date + index}>
                        <tr key={index}>
                          <td className="date">{dayjsFormat(dayData?.date, 'MM/DD dddd')}</td>

                          <td
                            className={
                              selectedHour[crewMemberData?.name + dayData?.date] ? 'active pointer' : 'pointer'
                            }
                            onClick={() => {
                              setSelectedHour((prevState) => ({
                                ...prevState,
                                [crewMemberData?.name + dayData?.date]:
                                  !prevState[crewMemberData?.name + dayData?.date],
                              }))
                            }}
                            style={{ pointerEvents: dayData?.hours ? 'inherit' : 'none' }}
                          >
                            {dayData?.ptoUsed
                              ? 'PTO'
                              : dayData?.dayOff
                              ? 'Day Off'
                              : dayData?.hours
                              ? dayData?.hours?.toFixed(2)
                              : '---'}
                          </td>
                        </tr>

                        {selectedHour[crewMemberData?.name + dayData?.date] && (
                          <tr>
                            <td colSpan={2}>
                              <div className="nested">
                                {dayData?.cards?.map((cardData) => (
                                  <Timecard
                                    timeCard={cardData}
                                    isReport
                                    key={cardData?._id}
                                    onTimeCardClick={(data) => {
                                      onEditingTimeCard(
                                        data?.timeCard,
                                        crewMemberData?.name,
                                        dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                      )
                                    }}
                                  />
                                ))}
                              </div>
                            </td>
                          </tr>
                        )}
                      </Fragment>
                    ))}
                </Fragment>
              ))}
            </tbody>
          </table>
        </>
      ) : null}

      <CustomModal show={showEditTimeCardPopUp} className="top timecard">
        {showEditTimeCardPopUp && (
          <EditTimeCardPopUp
            setShowTimeCardPopUp={setShowEditTimeCardPopUp}
            editPopUpValues={editPopUpValues}
            timeCardData={timeCardData}
            setDataUpdate={setDataUpdate}
          />
        )}
      </CustomModal>
    </PayrollReportCont>
  )
}

export default NoncrewPayroll
