import React, { useEffect, useState } from 'react'
import {
  RunPayRollContainer,
  RunPayRollHeading,
  RunPayRollSubHeading,
  RunPayRollTableContainer,
  RunPayRollTableContent,
  RunPayRollTableHeading,
  TableInputField,
  TableInputWrapper,
} from './style'
import { getPayRollReport } from '../../../logic/apis/report'
import { useLocation, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'

const RunPayRoll = () => {
  const { startDate, endDate, paySchId }: any = useParams()
  const { state } = useLocation()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [runRayRollData, setRunRayRollData] = useState<any>([])
  const getRunPayRollReport = async () => {
    const data = {
      paySchId,
      startDate,
      endDate,
    }
    const result = await getPayRollReport(data)
    setRunRayRollData(result?.data?.data?.workerArray)
  }
  useEffect(() => {
    getRunPayRollReport()
  }, [currentCompany])

  return (
    <RunPayRollContainer>
      <RunPayRollHeading>Run Payroll For {state?.paySchedule}</RunPayRollHeading>

      <RunPayRollSubHeading>
        Pay Period: {startDate} - {endDate}
      </RunPayRollSubHeading>
      <RunPayRollTableContainer>
        <RunPayRollTableHeading>
          <div>Name</div>
          <div>Hours</div>
          <div>Actual</div>
          <div>Cards To Approve</div>
        </RunPayRollTableHeading>
        {runRayRollData?.map((value: any, i: number) => (
          <RunPayRollTableContent key={i}>
            <div>{value?.name}</div>
            <div>{value?.report?.hrs}</div>
            <div>
              <TableInputWrapper>
                <div>$</div>
                <TableInputField type="text" />
              </TableInputWrapper>
            </div>{' '}
            <div>{value?.report?.unapproved}</div>
          </RunPayRollTableContent>
        ))}
      </RunPayRollTableContainer>
    </RunPayRollContainer>
  )
}

export default RunPayRoll
