import { Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'

import { BoldText, FormCont, PayrollReportCont } from './style'
import { FlexCol, FlexRow, Text } from '../../../styles/styled'
import { SharedDate } from '../../../shared/date/SharedDate'
import Button from '../../../shared/components/button/Button'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import { getPayRollReport } from '../../../logic/apis/report'
import { dayjsFormat, getHoursAndMinutes, hasValues, isSuccess, notify } from '../../../shared/helpers/util'
import { NonCrewPayrollResponse } from './interfaces/nonCrewPayrolInterface'
import Timecard from '../../../shared/timecard/Timecard'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { EditTimeCardPopUp } from '../../timeCard/components/editTimeCardPopUp/EditTimeCardPopUp'
import { SLoader } from '../../../shared/components/loader/Loader'
import DropdownWithCheckboxes from '../../../shared/dropdownWithCheckboxes/DropdownWithCheckboxes'
import { getPositionMembersById } from '../../../logic/apis/sales'
import { I_SalesPerson } from '../../sales/AddOpportunityModal'
import { getPosition } from '../../../logic/apis/position'
import { I_Position } from '../../leads/AddNewLead'
import { useLocation } from 'react-router-dom'

const PayrollReport = () => {
  const [showEditTimeCardPopUp, setShowEditTimeCardPopUp] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [timeCardData, setTimeCardData] = useState<any>({})
  const [editPopUpValues, setEditPopUpValues] = useState<any>()
  const [tableName, setTableName] = useState<{ [key: string]: boolean }>({})
  const [selectedHour, setSelectedHour] = useState<{ [key: string]: boolean }>({})
  const [optionLoader, setOptionLoader] = useState(false)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])

  const [buttonCall, setbuttonCall] = useState(false)
  const [loading, setLoading] = useState(false)
  const [payrollData, setPayrollData] = useState<NonCrewPayrollResponse>()
  const globalSelector = useAppSelector((state: any) => state)

  const location = useLocation()
  const params = new URLSearchParams(location.search)
  const paramSalesPerson = params.get('salesPerson')
  const paramStartDate = params.get('startDate')
  const paramEndDate = params.get('endDate')

  const selectedParamOptions = salesPersonDrop
    .filter((item) => paramSalesPerson?.includes(item._id))
    ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))

  const handleDateSelection = (salesPersonId: string, selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('salesPerson', salesPersonId)
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
  }
  const [filterData, setFilterData] = useState({
    salesPerson: '',
    startDate: '',
    endDate: '',
    selectedOptions: [],
  })
  const [initialValues, setInitialValues] = useState({
    salesPerson: '',
    startDate: '',
    endDate: '',
    selectedOptions: selectedParamOptions || [],
  })
  const onEditingTimeCard = (timeCard: any, name: string, date?: string) => {
    try {
      setTimeCardData(timeCard)
      let timeCardDate = date

      let editDataObj = {
        name: name,
        date: timeCardDate,
        allHourlyPayCheckbox: timeCard.hasOwnProperty('allHourly') ? timeCard.allHourly : false,
        project: timeCard.projectPO,
        task: timeCard.task.replace('_', ' '),
        taskName: timeCard?.taskName,
        timeIn: getHoursAndMinutes(timeCard.timeIn),
        timeOut: getHoursAndMinutes(timeCard.timeOut),
        status: timeCard.status.replace('_', ' '),
        workDone: timeCard?.work?.work?.workDone,
        extraTime: [
          {
            hours: timeCard?.work?.work?.extraTime?.extraHrs,
            minutes: timeCard?.work?.work?.extraTime?.extraMin,
          },
        ],
        notes: timeCard?.work?.notes,
        managerNotes: timeCard?.work?.managerNotes,
        removeFromLeadBonusCheckbox: timeCard?.work?.removeLeadBonus,
      }

      setEditPopUpValues({ ...editPopUpValues, ...editDataObj })
      setShowEditTimeCardPopUp(true)
    } catch (error) {
      console.log('onEditingTimeCard', error)
    }
  }

  const { currentCompany, currentMember, positionDetails } = globalSelector.company

  const getPositionMembers = async (startDate: string, endDate: string) => {
    try {
      const response = await getPositionMembersById({ startDate, endDate, hasOpportunity: false }, false)
      if (isSuccess(response)) {
        // return response?.data?.data?.memberData

        if (positionDetails?.symbol === 'SalesPerson' || positionDetails?.symbol === 'RRTech') {
          const filterResponse = response?.data?.data?.memberData?.filter(
            (member: any) => member?._id === currentMember?._id
          )

          setSalesPersonDrop(filterResponse)
        } else {
          setSalesPersonDrop(response?.data?.data?.memberData)
        }
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    } finally {
      setOptionLoader(false)
    }
  }

  useEffect(() => {
    if (paramStartDate && paramEndDate && paramSalesPerson) {
      if (paramStartDate !== '' && paramEndDate !== '' && paramSalesPerson !== '') {
        setInitialValues((prev) => ({
          ...prev,
          startDate: paramStartDate,
          endDate: paramEndDate,
        }))
        if (salesPersonDrop.length > 0) {
          handleSubmitForm({
            selectedOptions: selectedParamOptions || [],
            startDate: paramStartDate,
            endDate: paramEndDate,
          })
        }
      }
    }
  }, [location.search])

  useEffect(() => {
    if (salesPersonDrop.length > 0) {
      setInitialValues((prev) => ({ ...prev, selectedOptions: selectedParamOptions || [] }))
    }
  }, [salesPersonDrop])

  const handleSubmitForm = async (values: any) => {
    // setBool((prev) => !prev)

    try {
      setLoading(true)
      const salesPersonId: string = values.selectedOptions
        ?.filter((v: any) => salesPersonDrop?.map((v) => v._id)?.includes(v._id))
        ?.map((v: any) => v._id)
        .join(',')

      handleDateSelection(salesPersonId, values.startDate, values.endDate)
      const res = await getPayRollReport({
        startDate: values.startDate,
        endDate: values.endDate,
        salesPersonId,
      })
      setFilterData(values)
      if (isSuccess(res)) {
        setPayrollData(res?.data?.data?.payrollReport)
      }
    } catch (error) {
      console.error('payrollReport Error=====>', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <PayrollReportCont>
      <BoldText className="heading">
        Payroll Report{' '}
        {/* {initialValues?.startDate && initialValues?.endDate ? (
          <>
            for {dayjsFormat(initialValues?.startDate, 'MM/DD/YYYY')} -{' '}
            {dayjsFormat(initialValues?.endDate, 'MM/DD/YYYY')}
          </>
        ) : null} */}
        <div>
          <Text fontSize="28px" textAlign="center" fontWeight="500" margin="0">
            {filterData.startDate
              ? `${dayjsFormat(filterData.startDate, 'M/D/YY')} to ${dayjsFormat(filterData.endDate, 'M/D/YY')}`
              : ''}
          </Text>
        </div>
      </BoldText>

      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {(formik) => {
          const { values, setFieldValue, touched, errors } = formik
          useEffect(() => {
            if (currentMember?._id && positionDetails?.symbol && values.startDate !== '' && values.endDate !== '') {
              setInitialValues((prev) => ({ ...prev, endDate: values.endDate, startDate: values.startDate }))
              setOptionLoader(true)
              getPositionMembers(values.startDate, values.endDate)
            }
          }, [positionDetails, currentMember, values.startDate, values.endDate])
          return (
            <FlexCol margin="0 0 30px 0">
              <Form>
                <BoldText className="medium">Date range</BoldText>
                <FormCont>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                  <p>to</p>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    setFieldValue={setFieldValue}
                  />

                  {optionLoader ? (
                    <div>
                      <SLoader margin="10px 0 0 0" height={52} width={200} />
                    </div>
                  ) : (
                    <DropdownWithCheckboxes
                      options={salesPersonDrop?.map((v) => ({ name: v.name, _id: v._id })) || []}
                      formik={formik}
                      // selectedOptions={values?.selectedSalesPerson}
                    />
                  )}

                  <FlexRow margin="8px 0 0 0">
                    <Button
                      width="max-content"
                      height="52px"
                      isLoading={loading}
                      type="submit"
                      //   onClick={() => handleSubmitForm(values)}
                      disabled={
                        values.selectedOptions?.length === 0 || values.startDate === '' || values.endDate === ''
                      }
                    >
                      Run Report
                    </Button>
                  </FlexRow>
                </FormCont>
              </Form>
            </FlexCol>
          )
        }}
      </Formik>

      {hasValues(payrollData) ? (
        <>
          <FlexRow margin="0 0 20px 0">
            <BoldText>Total Hours: {payrollData?.totalHours?.toFixed(2)}</BoldText>
          </FlexRow>

          <table className="full-width">
            <thead>
              <tr className="row-heading">
                <th>Name</th>
                <th>Hrs</th>
              </tr>
            </thead>
            <tbody>
              {payrollData?.members?.map((crewMemberData, idx) => (
                <Fragment key={crewMemberData?.name + idx}>
                  <tr key={crewMemberData?.name} className="member">
                    <td
                      className="pointer"
                      onClick={() => {
                        setTableName((prevState) => ({
                          ...prevState,
                          [crewMemberData?.name + idx]: !prevState[crewMemberData?.name + idx],
                        }))
                      }}
                    >
                      {tableName[crewMemberData?.name + idx] ? '▼ ' : '▶ '}

                      {crewMemberData?.name}
                    </td>

                    <td>{crewMemberData?.totalHours ? crewMemberData?.totalHours?.toFixed(2) : '---'}</td>
                  </tr>

                  {tableName[crewMemberData?.name + idx] &&
                    crewMemberData?.dates?.map((dayData, index) => (
                      <Fragment key={dayData?.date + index}>
                        <tr key={index}>
                          <td className="date">{dayjsFormat(dayData?.date, 'MM/DD dddd')}</td>

                          <td
                            className={
                              selectedHour[crewMemberData?.name + dayData?.date] ? 'active pointer' : 'pointer'
                            }
                            onClick={() => {
                              setSelectedHour((prevState) => ({
                                ...prevState,
                                [crewMemberData?.name + dayData?.date]:
                                  !prevState[crewMemberData?.name + dayData?.date],
                              }))
                            }}
                            style={{ pointerEvents: dayData?.hours ? 'inherit' : 'none' }}
                          >
                            {dayData?.ptoUsed
                              ? 'PTO'
                              : dayData?.dayOff
                              ? 'Day Off'
                              : dayData?.hours
                              ? dayData?.hours?.toFixed(2)
                              : '---'}
                          </td>
                        </tr>

                        {selectedHour[crewMemberData?.name + dayData?.date] && (
                          <tr>
                            <td colSpan={2}>
                              <div className="nested">
                                {dayData?.cards?.map((cardData) => (
                                  <Timecard
                                    timeCard={cardData}
                                    isReport
                                    key={cardData?._id}
                                    onTimeCardClick={(data) => {
                                      onEditingTimeCard(
                                        data?.timeCard,
                                        crewMemberData?.name,
                                        dayjsFormat(data?.timeCard?.timeIn, 'M/D/YY')
                                      )
                                    }}
                                  />
                                ))}
                              </div>
                            </td>
                          </tr>
                        )}
                      </Fragment>
                    ))}
                </Fragment>
              ))}
            </tbody>
          </table>
        </>
      ) : null}

      <CustomModal show={showEditTimeCardPopUp} className="top timecard">
        {showEditTimeCardPopUp && (
          <EditTimeCardPopUp
            setShowTimeCardPopUp={setShowEditTimeCardPopUp}
            editPopUpValues={editPopUpValues}
            timeCardData={timeCardData}
            setDataUpdate={setDataUpdate}
          />
        )}
      </CustomModal>
    </PayrollReportCont>
  )
}

export default PayrollReport
