import { Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import * as SharedStyled from '../../../styles/styled'

import { getClientValueReport } from '../../../logic/apis/report'
import { SharedDate } from '../../../shared/date/SharedDate'
import * as Styled from './style'
import Button from '../../../shared/components/button/Button'
import {
  dayjsFormat,
  formatNumberToCommaS,
  getEnumValue,
  isSuccess,
  renderClientName,
} from '../../../shared/helpers/util'
import Timecard from '../../../shared/timecard/Timecard'
import GreenCheckSvg from '../../../assets/newIcons/greenCheck.svg'
import RedCheckSvg from '../../../assets/newIcons/redCheck.svg'

const ClientValueReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [clientValueReport, setClientValueReport] = useState<any>([])
  const [buttonCall, setbuttonCall] = useState(false)
  const [toggleConvCount, setToggleConvCount] = useState<{ [key: string]: boolean }>({})
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate !== '' && paramEndDate !== '' && currentCompany?._id) {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleClientValueReport({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search, currentCompany])

  const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const handleClientValueReport = async (values: any) => {
    try {
      handleDateSelection(values.startDate, values.endDate)
      setIsLoading(true)
      if (values) {
        const response = await getClientValueReport({
          endDate: values.endDate,
          startDate: values.startDate,
        })
        if (isSuccess(response)) {
          setClientValueReport(response?.data?.data)
        }
      }
    } catch (e) {
      console.log(e)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleCount = (type: string) => {
    setToggleConvCount((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }
  const renderBox = (
    name: string,
    value: number,
    toggleName?: string,
    isClickable?: boolean,
    isNotDollar?: boolean
  ) => {
    return (
      <div>
        <Fragment>
          <Styled.RateCard
            pointer={isClickable ? 'pointer' : 'unset'}
            scaleOnHover={true}
            background={toggleConvCount[`${toggleName}`] ? '#f4f3f3' : 'none'}
            onClick={() => {
              isClickable ? toggleCount(`${toggleName}`) : () => {}
            }}
          >
            <SharedStyled.Text textAlign="center" fontSize="18px">
              {name || <></>}
            </SharedStyled.Text>
            <br />
            <SharedStyled.Text textAlign="center" fontSize="14px">
              {isNotDollar || name === 'Ratio' ? <>{value || '--'}</> : <>${formatNumberToCommaS(value) || '--'}</>}
            </SharedStyled.Text>
          </Styled.RateCard>
        </Fragment>
      </div>
    )
  }

  const renderClientTable = (clients: any[], fromLeadSource?: string) => {
    return (
      <>
        <Styled.TableContainer>
          {clients.length ? (
            <>
              <Styled.TableHeading column="repeat(auto-fit, minmax(80px, 1fr))">
                <Styled.TableTitle>CLIENT NAME</Styled.TableTitle>
                <Styled.TableTitle className="center-align">Jobs</Styled.TableTitle>
                <Styled.TableTitle className="right-align">Gross</Styled.TableTitle>
                <Styled.TableTitle className="right-align">Volume</Styled.TableTitle>
              </Styled.TableHeading>
            </>
          ) : (
            <></>
          )}
          {clients?.map((v, index: number) => {
            return (
              <>
                <tr>
                  <Styled.TableContent
                    onClick={() =>
                      toggleCount(`${fromLeadSource || ''}-client-${index}-${v?.businessName || v?.fullName}`)
                    }
                    key={v?._id}
                    column="repeat(auto-fit, minmax(80px, 1fr))"
                  >
                    <Styled.CrewReportTableContentLabel>
                      <SharedStyled.FlexRow gap="5px" alignItems="center">
                        {v?.opps.every((op) => op.jobDone === true) ? (
                          <img src={GreenCheckSvg} alt="Completed" width="15" height="15" />
                        ) : (
                          <img src={RedCheckSvg} alt="Not Completed" width="15" height="15" />
                        )}
                        &nbsp;
                        {renderClientName(v?.isBusiness, v?.fullName, v?.businessName)}
                      </SharedStyled.FlexRow>
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel>{v?.oppCount || '--'}</Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.grossProfit) || '--'}
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.volume) || '--'}
                    </Styled.CrewReportTableContentLabel>
                  </Styled.TableContent>
                </tr>

                {toggleConvCount[`${fromLeadSource || ''}-client-${index}-${v?.businessName || v?.fullName}`] ? (
                  <div style={{ background: '#e9e8e8' }}>{renderOppTable(v?.opps)}</div>
                ) : null}
              </>
            )
          })}
        </Styled.TableContainer>
      </>
    )
  }

  const renderLeadSournceTable = (leadSources: any[]) => {
    return (
      <>
        <Styled.TableContainer>
          {leadSources.length ? (
            <>
              <Styled.TableHeading column="repeat(auto-fit, minmax(80px, 1fr))">
                <Styled.TableTitle>Source</Styled.TableTitle>
                <Styled.TableTitle className="center-align">Clients</Styled.TableTitle>
                <Styled.TableTitle className="center-align">Ratio</Styled.TableTitle>
                <Styled.TableTitle className="right-align">Gpc</Styled.TableTitle>
                <Styled.TableTitle className="right-align">CAC</Styled.TableTitle>
                <Styled.TableTitle className="right-align">Gross</Styled.TableTitle>
                <Styled.TableTitle className="right-align">Spend</Styled.TableTitle>
              </Styled.TableHeading>
            </>
          ) : (
            <></>
          )}
          {leadSources?.map((v, index: number) => {
            return (
              <>
                <tr>
                  <Styled.TableContent
                    onClick={() => toggleCount(`leadSource-${index}`)}
                    key={v?._id}
                    column="repeat(auto-fit, minmax(80px, 1fr))"
                  >
                    <Styled.CrewReportTableContentLabel>{v?.name}</Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel>{v?.clientCount || '--'}</Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel>{v?.ratio || '--'}</Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.gpc) || '--'}
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.cac) || '--'}
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.grossProfit) || '--'}
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.spend) || '--'}
                    </Styled.CrewReportTableContentLabel>
                  </Styled.TableContent>
                </tr>
                {toggleConvCount[`leadSource-${index}`] ? (
                  <div style={{ width: '90%', marginLeft: 'auto' }}>
                    {renderClientTable(v?.clients || [], 'leadSource')}
                  </div>
                ) : null}
              </>
            )
          })}
        </Styled.TableContainer>
      </>
    )
  }

  const renderOppTable = (opps: any[]) => {
    return (
      <>
        <Styled.TableContainer>
          {opps?.map((v) => {
            return (
              <>
                <tr
                  onClick={() => window.open(`/${getEnumValue(v?.stageGroup)}/opportunity/${v?._id}`, '_blank')}
                  style={{ cursor: 'pointer' }}
                >
                  <Styled.TableContent key={v?._id} column="repeat(auto-fit, minmax(80px, 1fr))">
                    <Styled.CrewReportTableContentLabel>
                      <SharedStyled.FlexRow gap="5px" alignItems="center">
                        {'jobDone' in v ? (
                          v.jobDone ? (
                            <img src={GreenCheckSvg} alt="Completed" width="15" height="15" />
                          ) : (
                            <img src={RedCheckSvg} alt="Not Completed" width="15" height="15" />
                          )
                        ) : null}
                        &nbsp;
                        {v?.PO || '--'}-{v?.num || '--'}
                      </SharedStyled.FlexRow>
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel>{v?.oppTypeName || '--'}</Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.grossProfit) || '--'}
                    </Styled.CrewReportTableContentLabel>
                    <Styled.CrewReportTableContentLabel className="right-align">
                      ${formatNumberToCommaS(v?.volume) || '--'}
                    </Styled.CrewReportTableContentLabel>
                  </Styled.TableContent>
                </tr>
              </>
            )
          })}
        </Styled.TableContainer>
      </>
    )
  }

  return (
    <div>
      <SharedStyled.SectionTitle>Client Value Report</SharedStyled.SectionTitle>
      <SharedStyled.HorizontalDivider />

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={(values, { setFieldValue }) => {
          handleClientValueReport(values)
        }}
      >
        {({ values, setFieldValue, touched, errors }: any) => {
          return (
            <Form>
              <Styled.ClientValueDateFilterContainer>
                <div>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </div>
                <div>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </div>

                <Styled.ButtonContainer>
                  <Button
                    isLoading={isLoading}
                    width="max-content"
                    type="submit"
                    height="52px"
                    style={{ alignSelf: 'flex-end' }}
                    onClick={() => setbuttonCall(true)}
                  >
                    Run Report
                  </Button>
                </Styled.ButtonContainer>
              </Styled.ClientValueDateFilterContainer>
              {/* {values?.startDate && values?.endDate && (
                <Styled.ReportDate>
                  From {dayjsFormat(values?.startDate, 'M/D/YY')} to {dayjsFormat(values?.endDate, 'M/D/YY')}
                </Styled.ReportDate>
              )} */}
            </Form>
          )
        }}
      </Formik>

      {!clientValueReport?.result ? (
        <></>
      ) : (
        <>
          <br />
          <SharedStyled.FlexBox flexWrap="wrap" gap="10px" justifyContent="center" padding="10px">
            {renderBox('GPC', clientValueReport?.result?.gpc || 0)}
            {renderBox('Ratio', clientValueReport?.result?.ratio || 0)}
            {renderBox('CAC', clientValueReport?.result?.cac || 0)}
          </SharedStyled.FlexBox>
          <br />
          <SharedStyled.FlexBox flexWrap="wrap" gap="10px" justifyContent="center" padding="10px">
            {renderBox('Total Spend', clientValueReport?.result?.totalSpend, 'Total Spend', true)}
            {renderBox('Clients', clientValueReport?.result?.clientsCount, `Clients`, true, true)}
          </SharedStyled.FlexBox>

          <>
            <br />
            <SharedStyled.FlexBox flexWrap="wrap" gap="10px" justifyContent="center" padding="10px">
              {renderBox('Gross Profit', clientValueReport?.result?.grossProfit, `Clients`, true)}
              {renderBox('Volume', clientValueReport?.result?.volume, `Clients`, true)}
            </SharedStyled.FlexBox>
          </>

          {toggleConvCount['Total Spend'] ? (
            <>
              <br />
              <>{renderLeadSournceTable(clientValueReport?.result?.leadSources)}</>
            </>
          ) : null}

          {toggleConvCount[`Clients`] ? (
            <>
              <br />
              <>{renderClientTable(clientValueReport?.result?.clients)}</>
            </>
          ) : null}
        </>
      )}
    </div>
  )
}

export default ClientValueReport
