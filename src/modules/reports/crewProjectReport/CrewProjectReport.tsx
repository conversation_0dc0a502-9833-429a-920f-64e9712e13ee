import { useNavigate, useParams } from 'react-router-dom'
import { getCrewProjectReport, getSearchProjectReport } from '../../../logic/apis/projects'
import { useSelector } from 'react-redux'
import { Fragment, useEffect, useState } from 'react'
import { ProjectReportContentHeading, ProjectReportHeading } from './style'
import { getDataFromLocalStorage, isSuccess, isTextContainsWord } from '../../../shared/helpers/util'
import CrewProjectDetails from '../../../shared/crewProjectDetails/CrewProjectDetails'
import useDebounce from '../../../shared/hooks/useDebounce'
import { Form, Formik } from 'formik'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import * as SharedStyled from '../../../styles/styled'
import { FullpageLoader, SLoader } from '../../../shared/components/loader/Loader'
import { StorageKey, SubscriptionPlanType } from '../../../shared/helpers/constants'

const sortData = (data: any[]) => {
  return data.sort((a, b) => {
    const aHasTaskName = !!a.taskName
    const bHasTaskName = !!b.taskName
    const aHasCost = a.workTask && a.workTask.toLowerCase().includes('cost')
    const bHasCost = b.workTask && b.workTask.toLowerCase().includes('cost')

    if (aHasTaskName && !bHasTaskName) {
      return -1 // a comes first if it has taskName
    } else if (!aHasTaskName && bHasTaskName) {
      return 1 // b comes first if it has taskName
    } else if (aHasCost && !bHasCost) {
      return -1 // a comes first if it has cost
    } else if (!aHasCost && bHasCost) {
      return 1 // b comes first if it has cost
    } else {
      return 0 // maintain the original order
    }
  })
}

const CrewProjectReport = () => {
  const navigate = useNavigate()

  const initialValue = {
    crewProjectReport: '',
  }
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [projectReportData, setProjectReportData] = useState<any>([])
  const [newPieceworkData, setNewPieceworkData] = useState({
    // costData: [],
    hoursData: [],
    pieceWorkData: [],
  })
  const [dropDownProjectValue, setDropDownProjectValue] = useState<any>([])
  const [laborReportData, setLaborReportData] = useState([])
  const [searchValue, setSearchValue] = useState<string>('')
  const [dropDownProjectValueFlag, setDropDownProjectValueFlag] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [poName, setPoName] = useState<string>('')
  const [poValue, setPOValue] = useState<string>('')
  const [buttonCall, setbuttonCall] = useState(false)

  const params = new URLSearchParams(location.search)
  const projectIdURL = params.get('pId')
  const oppIdURL = params.get('oppId')

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  useEffect(() => {
    if (projectIdURL && oppIdURL) {
      const dataParams = {
        oppId: oppIdURL,
      }
      if (!buttonCall) {
        fetchReportByPo(dataParams)
      }
    }
  }, [projectIdURL, oppIdURL])

  const fetchReportByPo = async (dataParams: any) => {
    setIsLoading(true)

    const result = await getCrewProjectReport(dataParams)
    if (isSuccess(result)) {
      const crewReport = result?.data?.data
      setProjectReportData(crewReport)
      // const costData = result?.data?.pieceWorkData?.filter((item: { workTask: string }) =>
      //   isTextContainsWord({ text: item?.workTask, word: 'Cost' })
      // )
      const hoursData = crewReport?.pieceWorkData?.filter((item: { workTask: string }) =>
        isTextContainsWord({ text: item?.workTask, word: 'Hour' })
      )
      const pieceWorkData = crewReport?.pieceWorkData?.filter((item: { taskName: string }) =>
        item?.hasOwnProperty('taskName')
      )
      // ?.sort((a: { workTask: string }, b: { workTask: string }) => a?.workTask?.localeCompare(b?.workTask))

      setNewPieceworkData({ hoursData, pieceWorkData })

      setLaborReportData(crewReport?.laborReport?.map((itm: any) => ({ ...itm, pieceWork: sortData(itm?.pieceWork) })))

      setPoName(`${crewReport?.po}-${crewReport?.num}`)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }

  const fetchCrewProjectReport = async (POID: any) => {
    setIsLoading(true)
    const value = dropDownProjectValue?.find((value: any) =>
      POID?.includes('-') ? `${value?.opp?.PO}-${value?.opp?.num}` === POID : value?.opp?.PO === POID
    )
    const result = await getCrewProjectReport({ oppId: value?.oppId })
    if (isSuccess(result)) {
      const crewReport = result?.data?.data
      setProjectReportData(crewReport)

      const hoursData = crewReport?.pieceWorkData?.filter((item: { workTask: string }) =>
        isTextContainsWord({ text: item?.workTask, word: 'Hour' })
      )
      const pieceWorkData = crewReport?.pieceWorkData?.filter((item: { taskName: string }) =>
        item?.hasOwnProperty('taskName')
      )
      // ?.sort((a: { workTask: string }, b: { workTask: string }) => a?.workTask?.localeCompare(b?.workTask))

      setNewPieceworkData({ hoursData, pieceWorkData })

      setLaborReportData(crewReport?.laborReport?.map((itm: any) => ({ ...itm, pieceWork: sortData(itm?.pieceWork) })))
      setPoName(`${crewReport?.po}-${crewReport?.num}`)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }
  const debouncedSearch = useDebounce(searchValue, 500)

  useEffect(() => {
    handleSearch()
  }, [debouncedSearch])

  const handleSearch = async () => {
    try {
      const dataParams = {
        search: searchValue,
      }
      const data = await getSearchProjectReport(dataParams)
      if (isSuccess(data)) {
        setDropDownProjectValue(data?.data?.data?.opps)
        setDropDownProjectValueFlag(false)
      } else {
        setDropDownProjectValueFlag(false)
      }
    } catch (error) {
      console.log({ error })
      setDropDownProjectValue(false)
    }
  }

  const handleDateSelection = (selectedProjectId: string, selectedOppId: string) => {
    const params = new URLSearchParams()
    const newURL = `${window.location.pathname}?pId=${selectedProjectId}&oppId=${selectedOppId}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }
  return (
    <div>
      <ProjectReportHeading>Crew Job Cost Report</ProjectReportHeading>

      <div>
        <Formik initialValues={initialValue} onSubmit={() => {}}>
          {({ touched, errors, values, setFieldValue }) => {
            const handleSubmitForm = () => {
              if (values.crewProjectReport.split(':')[0]) {
                fetchCrewProjectReport(values?.crewProjectReport?.split(':')[0])
              }
            }
            useEffect(() => {
              handleSubmitForm()
              setPoName(values.crewProjectReport)
              if (values.crewProjectReport !== '') {
                const poName = values?.crewProjectReport?.split(':')[0]?.trim()

                const value = dropDownProjectValue?.find((value: any) =>
                  poName?.includes('-') ? `${value?.opp?.PO}-${value?.opp?.num}` === poName : value?.opp?.PO === poName
                )

                handleDateSelection(value?.projectId, value?.oppId)
              }
            }, [poValue])

            useEffect(() => {
              // if (values.crewProjectReport) {
              if (values.crewProjectReport) {
                setbuttonCall(true)
              }
              setSearchValue(values.crewProjectReport)
              // }
            }, [values.crewProjectReport])
            return (
              <Form>
                {!dropDownProjectValueFlag && (
                  <AutoComplete
                    isCrewProjectReport
                    borderRadius="0px"
                    labelName={isProPlusPlan ? 'Search name, PO#, phone, address' : 'Search name, PO#, address'}
                    stateName="crewProjectReport"
                    error={touched.crewProjectReport && errors.crewProjectReport ? true : false}
                    setFieldValue={setFieldValue}
                    options={dropDownProjectValue?.map((value: any) =>
                      `${value?.opp?.PO}${value?.opp?.num ? `-${value?.opp?.num}` : ''}${
                        value?.opp?.client?.firstName
                          ? `: ${value?.opp?.client?.firstName} ${value?.opp?.client?.lastName ?? ''}`
                          : ''
                      } ${value?.opp?.startDate ? `| start: ${value?.opp?.startDate?.split('T')[0] ?? ''}` : ''}${
                        value?.opp?.endDate ? ` | done: ${value?.opp?.endDate?.split('T')[0] ?? ''}` : ''
                      }
                          `?.trim()
                    )}
                    value={values.crewProjectReport}
                    autoFillData={''}
                    // onAddClick={() => {
                    //   setShowAddNewClientModal(true)
                    // }}
                    setValueOnClick={setPOValue}
                    apiSearch={true}
                  />
                )}
              </Form>
            )
          }}
        </Formik>
      </div>

      {isLoading ? (
        <>
          <br />
          <div>
            <SharedStyled.SkeletonLoader height={40} width={200}>
              <div className="skeleton"></div>
            </SharedStyled.SkeletonLoader>
            <br />
            <SharedStyled.SkeletonLoader width={200}>
              <div className="skeleton"></div>
              <div className="skeleton"></div>
              <div className="skeleton"></div>
            </SharedStyled.SkeletonLoader>
            <br />
          </div>
          <SharedStyled.FlexRow>
            <SLoader height={200} />
            <SLoader height={200} />
            <SLoader height={200} />
            <SLoader height={200} />
          </SharedStyled.FlexRow>
        </>
      ) : (
        <>
          <ProjectReportContentHeading>
            PO#: {projectReportData?.po}
            {projectReportData?.num ? `-${projectReportData?.num}` : ''}
          </ProjectReportContentHeading>
          <CrewProjectDetails
            projectReportData={projectReportData}
            newPieceworkData={newPieceworkData}
            laborReportData={laborReportData}
            isProPlusPlan={isProPlusPlan}
          />
        </>
      )}
    </div>
  )
}

export default CrewProjectReport
