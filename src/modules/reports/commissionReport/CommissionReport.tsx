import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { getCommissionReport } from '../../../logic/apis/report'
import { Table } from '../../../shared/table/Table'
import {
  CommissionReportDescription,
  CommissionReportHeading,
  CommissionReportMainContainer,
  CommissionReportMainHeading,
  CommissionReportMainSubHeading,
  CommissionReportSubHeading,
  CommissionReportWrapper,
  TableWrap,
} from './style'
import {
  modifiedSalesCommissionColumn,
  repairsColumn,
  roofColumn,
  roofCompletedColumn,
  totalCompletedColumn,
  totalSalesColumn,
} from './tableColumn'
import { FlexCol, FlexRow, HorizontalDivider } from '../../../styles/styled'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import {
  dayjsFormat,
  formatNumberToCommaS,
  getDataFromLocalStorage,
  getEnumValue,
  isSuccess,
} from '../../../shared/helpers/util'
import { getStages } from '../../../logic/apis/sales'
import Button from '../../../shared/components/button/Button'
import { StorageKey } from '../../../shared/helpers/constants'
import NewCommissionReport from './NewCommissionReport'

const CommissionReport = () => {
  const { startDate, endDate }: any = useParams()
  const [commissionReport, setCommissionReport] = useState<any>([])
  const [showTotalData, setShowTotalData] = useState<{ [key: number]: boolean }>({})
  const [showRoofsData, setShowRoofData] = useState<{ [key: number]: boolean }>({})
  const [showRoofsCompletedData, setShowRoofsCompletedData] = useState<{ [key: number]: boolean }>({})
  const [showRepairsData, setShowRepairsData] = useState<{ [key: number]: boolean }>({})
  const [stages, setStages] = useState<I_Stage[]>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [selectedName, setSelectedName] = useState<number>()
  const navigate = useNavigate()

  const getCommissionReportData = async () => {
    const response = await getCommissionReport({
      endDate: endDate,
      startDate: startDate,
    })
    setCommissionReport(response?.data?.data)
  }
  useEffect(() => {
    getCommissionReportData()
  }, [])

  const toggleTotalData = (index: number) => {
    setShowTotalData((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }))
  }

  const toggleReportData = (index: number) => {
    setShowRoofData((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }))
  }

  const toggleRoofsCompletedData = (type: number) => {
    setShowRoofsCompletedData((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  useEffect(() => {
    getStagesData()
  }, [])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const getpageById = (id: string, oppId: string) => {
    const matchedObject: any = stages.find((item) => item._id === id)
    // If a matching object is found, return its name property
    if (matchedObject) {
      const url = `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${oppId}`
      window.open(url, '_blank')
    } else {
      // If no matching object is found, return null or an appropriate default value
      return null
    }
  }

  const toggleRepairsData = (type: number) => {
    setShowRepairsData((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  useEffect(() => {
    if (selectedName || selectedName === 0) {
      const printContent = document.querySelector(`#print-commission-${selectedName}`) as HTMLElement
      // const headingContent = document.querySelector(`#pre-heading`) as HTMLElement
      // headingContent.style.display = 'flex'
      if (printContent) {
        const clone = printContent.cloneNode(true)
        document.body.innerHTML = ''
        // clone.insertBefore(headingContent, clone.firstChild)
        document.body.appendChild(clone)
        window.print()
        window.location.reload()
      }
    }
  }, [selectedName])
  return (
    <div>
      <CommissionReportMainContainer>
        <FlexCol padding="0 0 20px 0" alignItems="center" gap="4px" style={{ display: 'none' }} id="pre-heading">
          <h1>Commission Report</h1>
          <CommissionReportMainSubHeading>
            Pay Period : {dayjsFormat(startDate, 'M/D/YY')} to {dayjsFormat(endDate, 'M/D/YY')}
          </CommissionReportMainSubHeading>
        </FlexCol>

        <FlexCol padding="0 0 20px 0" style={{ borderBottom: '1px solid lightGrey' }}>
          <CommissionReportMainHeading>Commission Report</CommissionReportMainHeading>
          <CommissionReportMainSubHeading>
            Pay Period : {dayjsFormat(startDate, 'M/D/YY')} to {dayjsFormat(endDate, 'M/D/YY')}
          </CommissionReportMainSubHeading>
        </FlexCol>

        {commissionReport?.report?.salesPeople?.map((value: any, v: number) => (
          <div key={v} id={`print-commission-${v}`}>
            <NewCommissionReport
              data={{
                periodStart: dayjsFormat(startDate, 'M/D/YY'),
                periodEnd: dayjsFormat(endDate, 'M/D/YY'),
                ...value,
              }}
              onPrintClick={(idx) => {
                setSelectedName(idx)
              }}
            />
          </div>
        ))}
        {/*   {commissionReport?.report?.salesPeople?.map((value: any, v: number) => (
          <CommissionReportWrapper key={v} id={`print-commission-${v}`}>
            <FlexRow gap="30px">
              <CommissionReportHeading>{value?.name}</CommissionReportHeading>

              <Button
                onClick={() => {
                  value?.monthEnd && toggleTotalData(v)

                  value?.type?.forEach((type: any, index: number) => {
                    type?.sold?.num && toggleRoofsCompletedData(type?.name + index + v + 0)
                    type?.started?.num && toggleRoofsCompletedData(type?.name + index + v + 1)
                    type?.completed?.num && toggleRoofsCompletedData(type?.name + index + v + 2)
                  })

                  setSelectedName(v)
                }}
                width="max-content"
                type="button"
                padding="4px 12px"
                className="noPrint"
              >
                Print
              </Button>
            </FlexRow>

            {value?.noOrder?.num > 0 && (
              <>
                <CommissionReportHeading fontSize={'14px'} color={'red'}>
                  Opps w/ no order {value?.noOrder?.num}
                </CommissionReportHeading>
                {value?.noOrder?.opps?.map((val: any) => {
                  return (
                    <div>
                      <span
                        className="bold"
                        style={{ color: 'blue', cursor: 'pointer', margin: '0 0 0 100px' }}
                        onClick={() => {
                          getpageById(val.stage, val._id)
                        }}
                      >
                        {val?.clientId?.firstName + ' ' + `${val?.clientId?.lastName ?? ''}`}
                      </span>
                    </div>
                  )
                })}
              </>
            )}

            <>
              {!value?.monthEnd && (
                <CommissionReportSubHeading>
                  Month Total: {value?.month?.num} sales for ${formatNumberToCommaS(Number(value?.month?.volume))}
                </CommissionReportSubHeading>
              )}
              {value?.monthEnd && (
                <>
                  <CommissionReportSubHeading onClick={() => toggleTotalData(v)} cursor={true}>
                    Month Total: {value?.month?.num} sales for ${formatNumberToCommaS(Number(value?.month?.volume))}
                  </CommissionReportSubHeading>
                  <p>
                    {value?.benchmarks?.num} {value?.benchmarks?.text}, total bonus of $
                    {formatNumberToCommaS(Number(value?.benchmarks?.bonus))}
                  </p>
                </>
              )}

              <FlexCol>
                {showTotalData[v] && (
                  <>
                    <br />
                    <CommissionReportDescription>Sales in first half of month</CommissionReportDescription>
                    <Table
                      columns={totalSalesColumn}
                      data={value?.firstPeriod?.opps?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                      fetchData={() => {}}
                      noSearch
                      minWidth=""
                      noBorder
                      onRowClick={(val) => getpageById(val.stage, val._id)}
                    />
                  </>
                )}
                {showTotalData[v] && (
                  <>
                    <br />
                    <CommissionReportDescription>Completed in first half of month</CommissionReportDescription>
                    <Table
                      columns={totalCompletedColumn}
                      data={value?.firstPeriod?.completed?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                      fetchData={() => {}}
                      noSearch
                      minWidth=""
                      noBorder
                      onRowClick={(val) => getpageById(val.stage, val._id)}
                    />
                  </>
                )}
              </FlexCol>
            </>

            /~ <TableWrap gap="24px"> ~/
            <TableWrap>
              {value?.type.map((type: any, index: number) => {
                return (
                  <div>
                    /~ {type?.typeReplacement ? ( ~/
                    <>
                      {
                        <>
                          <CommissionReportDescription
                            type="roof"
                            onClick={() => type?.sold?.num && toggleRoofsCompletedData(type?.name + index + v + 0)}
                            cursor={true}
                          >
                            {type?.name} Sold: {type?.sold?.num || 0} | Volume: $
                            {formatNumberToCommaS(Number(type?.sold?.volume))}
                          </CommissionReportDescription>
                          <CommissionReportDescription type="commission">
                            Commission: ${formatNumberToCommaS(Number(type?.sold?.commission))}
                          </CommissionReportDescription>
                          {showRoofsCompletedData[type?.name + index + v + 0] && (
                            <Table
                              columns={roofColumn}
                              data={type?.sold?.opps?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                              fetchData={() => {}}
                              noSearch
                              minWidth=""
                              noBorder
                              onRowClick={(val) => getpageById(val.stage, val._id)}
                            />
                          )}
                        </>
                      }

                      /~ ============ Started ============ ~/

                      {!!type?.started?.commission && (
                        <>
                          <CommissionReportDescription
                            type="roof"
                            onClick={() => type?.started?.num && toggleRoofsCompletedData(type?.name + index + v + 1)}
                            cursor={true}
                          >
                            {type?.name} Started: {type?.started?.num || 0} | Volume: $
                            {formatNumberToCommaS(Number(type?.started?.volume))}
                          </CommissionReportDescription>
                          <CommissionReportDescription type="commission">
                            Commission: ${formatNumberToCommaS(Number(type?.started?.commission))}
                          </CommissionReportDescription>
                          {showRoofsCompletedData[type?.name + index + v + 1] && (
                            <Table
                              columns={roofCompletedColumn}
                              data={type?.started?.opps?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                              fetchData={() => {}}
                              noSearch
                              minWidth=""
                              noBorder
                              onRowClick={(val) => getpageById(val.stage, val._id)}
                            />
                          )}
                        </>
                      )}

                      /~ ============ Completed ============ ~/

                      {
                        <>
                          <CommissionReportDescription
                            type="roof"
                            onClick={() => type?.completed?.num && toggleRoofsCompletedData(type?.name + index + v + 2)}
                            cursor={true}
                          >
                            {type?.name} Completed: {type?.completed?.num || 0} | Volume: $
                            {formatNumberToCommaS(Number(type?.completed?.volume))}
                          </CommissionReportDescription>
                          <CommissionReportDescription type="commission">
                            Commission: ${formatNumberToCommaS(Number(type?.completed?.commission))}
                          </CommissionReportDescription>
                          {showRoofsCompletedData[type?.name + index + v + 2] && (
                            <Table
                              columns={roofCompletedColumn}
                              data={type?.completed?.opps?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                              fetchData={() => {}}
                              noSearch
                              minWidth=""
                              noBorder
                              onRowClick={(val) => getpageById(val.stage, val._id)}
                            />
                          )}
                        </>
                      }
                    </>
                  </div>
                )
              })}
            </TableWrap>

            <CommissionReportDescription>Commission Modifications</CommissionReportDescription>
            <CommissionReportDescription type="commission">
              Commission: ${formatNumberToCommaS(Number(value?.modified?.amount))}
            </CommissionReportDescription>

            {value?.modified?.opp?.length > 0 ? (
              <Table
                columns={modifiedSalesCommissionColumn}
                data={value?.modified?.opp?.sort((a, b) => new Date(a.saleDate) - new Date(b.saleDate))}
                fetchData={() => {}}
                noSearch={true}
                minWidth=""
                noBorder
                onRowClick={(val) => getpageById(val?.oppId?.stage, val?.oppId?._id)}
                // onRowClick={(val) => {
                //   navigate(`/sales/opportunity/${val?._id}`)
                //   // navigate(`/sales/opportunity/${val?._id}`)
                // }}
              />
            ) : null}
            <CommissionReportDescription>
              Salary: ${formatNumberToCommaS(Number(value?.salary)) || '---'}
            </CommissionReportDescription>
            <CommissionReportDescription>
              Commission: ${formatNumberToCommaS(Number(value?.commission)) || '---'}
            </CommissionReportDescription>
            <CommissionReportSubHeading>
              Total Earned This Period: ${formatNumberToCommaS(Number(value?.totalEarned)) || '---'}
            </CommissionReportSubHeading>
            {value?.monthEnd && (
              <CommissionReportDescription>
                Total Earned Entire Month: ${formatNumberToCommaS(Number(value?.monthEarned)) || '---'}
              </CommissionReportDescription>
            )}

            <HorizontalDivider margin="20px 0 0 0" bg="lightGrey" />
          </CommissionReportWrapper>
        ))}*/}
      </CommissionReportMainContainer>
    </div>
  )
}

export default CommissionReport
