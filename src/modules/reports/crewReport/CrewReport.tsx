import { Form, Formik } from 'formik'
import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as SharedStyled from '../../../styles/styled'

import { getCrewReport } from '../../../logic/apis/report'
import { SharedDate } from '../../../shared/date/SharedDate'
import * as Styled from './style'
import Button from '../../../shared/components/button/Button'
import { dayjsFormat, getDataFromLocalStorage } from '../../../shared/helpers/util'
import Timecard from '../../../shared/timecard/Timecard'
import { StorageKey } from '../../../shared/helpers/constants'

const CrewReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [tableName, setTableName] = useState<{ [key: number]: boolean }>({})
  const [crewReport, setCrewReport] = useState<any>([])
  const [buttonCall, setbuttonCall] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate !== '' && paramEndDate !== '' && currentCompany?._id) {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleCrewReport({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search, currentCompany])

  const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const toggleRoofsCompletedData = (type: number) => {
    setTableName((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }
  const handleCrewReport = async (values: any) => {
    try {
      handleDateSelection(values.startDate, values.endDate)
      setIsLoading(true)
      if (values) {
        const response = await getCrewReport({
          endDate: values.endDate,
          startDate: values.startDate,
        })

        setCrewReport(response?.data?.data)
      }
    } catch (e) {
      console.log(e)
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <Styled.CrewReportMainLayout>
      <SharedStyled.SectionTitle>Crew Scoreboard</SharedStyled.SectionTitle>
      <SharedStyled.HorizontalDivider />

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={(values, { setFieldValue }) => {
          handleCrewReport(values)
        }}
      >
        {({ values, setFieldValue, touched, errors }: any) => {
          return (
            <Form>
              <Styled.CrewReportDateFilterContainer>
                <div>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </div>
                <div>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </div>

                <Styled.KPIButtonContainer>
                  <Button
                    isLoading={isLoading}
                    width="max-content"
                    type="submit"
                    height="52px"
                    style={{ alignSelf: 'flex-end' }}
                    onClick={() => setbuttonCall(true)}
                  >
                    Run Report
                  </Button>
                </Styled.KPIButtonContainer>
              </Styled.CrewReportDateFilterContainer>
              {values?.startDate && values?.endDate && (
                <Styled.CrewReportDate>
                  From {dayjsFormat(values?.startDate, 'M/D/YY')} to {dayjsFormat(values?.endDate, 'M/D/YY')}
                </Styled.CrewReportDate>
              )}

              {crewReport &&
                crewReport?.activeCrews?.map((report: any) => (
                  <Fragment key={report?.crewId}>
                    <Styled.CrewReportWrapper>
                      <Styled.CrewReportLabel>{report?.name}'s Crew</Styled.CrewReportLabel>
                      <Styled.CrewReportDescription>
                        <b>Crew Points: {Number(report?.cPoints)}</b>
                      </Styled.CrewReportDescription>
                      <Styled.CrewReportDescription>
                        Crew Hours: {Number(report?.cHours).toFixed(2)}
                      </Styled.CrewReportDescription>
                      <Styled.CrewReportDescription>
                        Crew Pts/Hr: {Number(report?.cPtsPerHour).toFixed(2)}
                      </Styled.CrewReportDescription>
                    </Styled.CrewReportWrapper>
                    <Styled.CrewReportTableContainer>
                      <Styled.CrewReportTableHeading>
                        <Styled.CrewReportTableTitle>Name</Styled.CrewReportTableTitle>
                        <Styled.CrewReportTableTitle>Points</Styled.CrewReportTableTitle>
                        <Styled.CrewReportTableTitle>Hours</Styled.CrewReportTableTitle>
                        <Styled.CrewReportTableTitle>Pts/Hr</Styled.CrewReportTableTitle>
                      </Styled.CrewReportTableHeading>

                      {report?.crewMembers?.map((member: any, index: number) => (
                        <Fragment key={index}>
                          <Styled.CrewReportTableContent onClick={() => toggleRoofsCompletedData(member?.name + index)}>
                            <Styled.CrewReportTableContentLabel>
                              {member?.name}
                              {member?.mVersion?.versionName ? <> ({member?.mVersion?.versionName})</> : null}
                            </Styled.CrewReportTableContentLabel>
                            <Styled.CrewReportTableContentLabel>
                              {Number(member?.mPoints)}
                            </Styled.CrewReportTableContentLabel>
                            <Styled.CrewReportTableContentLabel>
                              {Number(member?.mHours).toFixed(2)}
                            </Styled.CrewReportTableContentLabel>
                            <Styled.CrewReportTableContentLabel>
                              {Number(member?.mPtsPerHour).toFixed(2)}
                            </Styled.CrewReportTableContentLabel>
                          </Styled.CrewReportTableContent>

                          {tableName[member?.name + index] && (
                            <Styled.CrewReportTableContent column="1fr">
                              {member?.days.map((day: any) => {
                                return (
                                  <Styled.CrewReportTableContentLabel key={day?.date}>
                                    <span style={{ fontSize: '18px' }}>{dayjsFormat(day?.date, 'dddd')}</span> -{' '}
                                    {dayjsFormat(day?.date, 'M/D/YY')}
                                    <br />
                                    Points: {day?.points?.toFixed(1)} | Hours: {day?.hours?.toFixed(2)} | Pts/Hr:{' '}
                                    {day?.ptsPerHour?.toFixed(2)}
                                    <br />
                                    <Styled.CrewReportTableContent
                                      borderNone="none"
                                      padding="0"
                                      column="repeat(3,1fr)"
                                      gap="10px"
                                    >
                                      {day?.cards.map((card: any) => (
                                        <Fragment key={card?._id}>
                                          <Timecard timeCard={card} isReport />
                                        </Fragment>
                                      ))}
                                    </Styled.CrewReportTableContent>
                                    <br />
                                  </Styled.CrewReportTableContentLabel>
                                )
                              })}
                            </Styled.CrewReportTableContent>
                          )}
                        </Fragment>
                      ))}
                    </Styled.CrewReportTableContainer>
                  </Fragment>
                ))}
            </Form>
          )
        }}
      </Formik>
    </Styled.CrewReportMainLayout>
  )
}

export default CrewReport
