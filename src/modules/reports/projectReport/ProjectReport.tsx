import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { getProjectReport, getSearchProjectReport, projectUpdateOrder } from '../../../logic/apis/projects'
import { useSelector } from 'react-redux'
import { Fragment, useEffect, useRef, useState } from 'react'
import {
  Button,
  ButtonWrapper,
  DropDownContainer,
  DropDownMainContainer,
  InputField,
  MainWrapper,
  ModalHeader,
  NewTableContent,
  NewTableHeading,
  ProjectReportContentHeading,
  ProjectReportContentSubHeading,
  ProjectReportHeading,
  ProjectReportInputSearchContainer,
  ProjectReportMainContainer,
  TableContainer,
  TableContent,
  TableContentLabel,
  TableFlexContainer,
  TableHeading,
  TableInputField,
  TableInputWrapper,
  TableTitle,
} from './style'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import { Form, Formik } from 'formik'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import MaterialInputModal from './component/MaterialInputModal'
import {
  camelCaseToSentenceCase,
  dayjsFormat,
  dedupeArray,
  formatNumber,
  formatNumberToCommaS,
  getDataFromLocalStorage,
  getEnumValue,
  formatDollarAmountWithIncrement,
  isSuccess,
  isTextContainsWord,
  notify,
  simplifyBackendError,
  toPascalCase,
  truncateParagraph,
} from '../../../shared/helpers/util'
import * as SharedStyled from '../../../styles/styled'
import { Table } from '../../../shared/table/Table'
import * as Styled from './style'
import { SLoader } from '../../../shared/components/loader/Loader'
import DailyLog from '../../timeCard/components/dailyLog/DailyLog'
import useDebounce from '../../../shared/hooks/useDebounce'
import Timecard from '../../../shared/timecard/Timecard'
import CrewProjectDetails from '../../../shared/crewProjectDetails/CrewProjectDetails'
import { Nue, StorageKey, SubscriptionPlanType } from '../../../shared/helpers/constants'
import { getOpportunityMedia } from '../../../logic/apis/media'
import useFetch from '../../../logic/apis/useFetch'
import SubContracctorModal from './component/SubContracctorModal'

const sortData = (data: any[]) => {
  return data.sort((a, b) => {
    const aHasTaskName = !!a.taskName
    const bHasTaskName = !!b.taskName
    const aHasCost = a.workTask && a.workTask.toLowerCase().includes('cost')
    const bHasCost = b.workTask && b.workTask.toLowerCase().includes('cost')

    if (aHasTaskName && !bHasTaskName) {
      return -1 // a comes first if it has taskName
    } else if (!aHasTaskName && bHasTaskName) {
      return 1 // b comes first if it has taskName
    } else if (aHasCost && !bHasCost) {
      return -1 // a comes first if it has cost
    } else if (!aHasCost && bHasCost) {
      return 1 // b comes first if it has cost
    } else {
      return 0 // maintain the original order
    }
  })
}

const ProjectReport = () => {
  const navigate = useNavigate()

  const [data, setData] = useSearchParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [searchValue, setSearchValue] = useState<string>('')
  const [poName, setPoName] = useState<string>('')
  const [isSearchOpen, setSearchOpen] = useState<boolean>(false)
  const [projectSearch, setProjectSearch] = useState<boolean>(false)
  const [dropDownProjectValue, setDropDownProjectValue] = useState<any>([])
  const [dropDownProjectValueFlag, setDropDownProjectValueFlag] = useState(true)
  const [projectReportLoading, setProjectReportLoading] = useState(false)
  const [projectReportData, setProjectReportData] = useState<any>([])
  const [daysValue, setDaysValue] = useState<number>(0)
  const [subContractor, setSubContractor] = useState<number>(0)
  const [labourCost, setLabourCost] = useState<number>(0)
  const [salesPriceValue, setSalesPriceValue] = useState<number>(0)
  const [inventoryCost, setInventoryCost] = useState<number>(0)
  const [qbCOGS, setQbCOGS] = useState<number>(0)
  const [receipts, setReceipts] = useState<number>(0)
  const [other, setOther] = useState<number>(0)
  const [actualMatCost, setActualMatCost] = useState<number>(0)
  const [inventoryMat, setInventoryMat] = useState([])
  const [costMat, setCostMat] = useState(0)
  const [taxMat, setTaxMat] = useState(0)
  const [totalCostMat, setTotalCostMat] = useState(0)
  const [modifiedBudget, setModifiedBudget] = useState<{
    subContractorTotal: number
    isSubcontractorOnly: boolean
    rawLaborBudget: number
  }>({
    subContractorTotal: 0,
    isSubcontractorOnly: true,
    rawLaborBudget: 0,
  })

  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [subContractorInput, setSubContractorInput] = useState(false)
  const [materialInput, setMaterialInput] = useState(false)
  const [materialTable, setMaterialTable] = useState(false)
  const [buttonCall, setbuttonCall] = useState(false)
  const [selectedCrew, setSelectedCrew] = useState('')
  const [logId, setLogId] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [reportD, setReport] = useState<string>('')
  const [poValue, setPOValue] = useState<string>('')
  const [mediaBool, setMediaBool] = useState(false)
  const [newPieceworkData, setNewPieceworkData] = useState({
    // costData: [],
    hoursData: [],
    pieceWorkData: [],
  })

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  const [laborReportData, setLaborReportData] = useState([])

  const [showCrewDailyLog, setShowCrewDailyLog] = useState(false)
  const [project, setProject] = useState('')
  const columns = [
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'Amt',
      accessor: (row: any) => row?.amount?.toFixed(2),
    },
    {
      Header: 'Cost',
      accessor: (row: any) => row?.cost?.toFixed(2),
    },
  ]

  const handleSearch = async () => {
    try {
      setProjectSearch(true)
      const dataParams = {
        search: searchValue,
      }
      const data = await getSearchProjectReport(dataParams)
      if (isSuccess(data)) {
        setDropDownProjectValue(data?.data?.data?.opps)
        setDropDownProjectValueFlag(false)
      } else {
        setDropDownProjectValueFlag(false)
      }
    } catch (error) {
      console.log({ error })
      setDropDownProjectValue(false)
    } finally {
      setProjectSearch(false)
    }
  }

  const params = new URLSearchParams(location.search)
  const projectIdURL = params.get('pId')
  const oppIdURL = params.get('oppId')
  // const projectIdURL = data.get('pId')
  // const oppIdURL = data.get('oppId')

  const { data: mediaRes } = useFetch({
    fetchFn: () =>
      oppIdURL
        ? getOpportunityMedia(oppIdURL!, {
            tags: 'Job Cost',
          })
        : () => {},
    refetchTrigger: mediaBool,
  })

  useEffect(() => {
    if (oppIdURL) {
      setMediaBool((p) => !p)
    }
  }, [oppIdURL])

  const handleDateSelection = (selectedProjectId: string, selectedOppId: string) => {
    const params = new URLSearchParams()
    const newURL = `${window.location.pathname}?pId=${selectedProjectId}&oppId=${selectedOppId}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  useEffect(() => {
    if (projectIdURL && oppIdURL) {
      const dataParams = {
        oppId: oppIdURL,
        projectId: projectIdURL,
      }
      if (!buttonCall) {
        fetchReportByPo(dataParams)
      }
    }
  }, [projectIdURL, oppIdURL])

  const fetchReportByPo = async (dataParams: any) => {
    const result = await getProjectReport(dataParams)
    if (isSuccess(result)) {
      const jobReport = result?.data?.data
      setProjectReportData(jobReport)

      const hoursData = jobReport?.pieceWorkData?.filter((item: { workTask: string }) =>
        isTextContainsWord({ text: item?.workTask, word: 'Hour' })
      )
      const pieceWorkData = jobReport?.pieceWorkData?.filter((item: { taskName: string }) =>
        item?.hasOwnProperty('taskName')
      )

      setNewPieceworkData({ hoursData, pieceWorkData })

      setLaborReportData(jobReport?.laborReport?.map((itm: any) => ({ ...itm, pieceWork: sortData(itm?.pieceWork) })))

      setPoName(`${jobReport?.opportunity?.opp?.PO}-${jobReport?.opportunity?.opp?.num}`)
      const { materials, cost, tax, totalCost } = estimatedMats(jobReport)
      setInventoryMat(materials)
      setCostMat(cost)
      setTaxMat(tax)
      setTotalCostMat(totalCost)
      setModifiedBudget(jobReport?.opportunity?.order?.modifiedBudget)

      setInventoryCost(jobReport?.opportunity?.order?.actualTotals?.inventoryCost)
      setQbCOGS(jobReport?.opportunity?.order?.actualTotals?.qbCOGS)
      setReceipts(jobReport?.opportunity?.order?.actualTotals?.receipts)
      setOther(jobReport?.opportunity?.order?.actualTotals?.other)
      setActualMatCost(jobReport?.opportunity?.order?.actualTotals?.actualMatCost)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }
  console.log({ modifiedBudget })

  const debouncedSearch = useDebounce(searchValue, 500)

  useEffect(() => {
    handleSearch()
  }, [debouncedSearch])

  const handleProject = async (POID: any) => {
    setIsLoading(true)
    const value = dropDownProjectValue?.find((value: any) => `${value?.opp?.PO}-${value?.opp?.num}` === POID)
    const dataParams = {
      oppId: value?.oppId,
      projectId: value?.projectId,
    }

    const result = await getProjectReport(dataParams)

    if (isSuccess(result)) {
      const projectReport = result?.data?.data
      setProjectReportData(projectReport)
      // const costData = result?.data?.pieceWorkData?.filter((item: { workTask: string }) =>
      //   isTextContainsWord({ text: item?.workTask, word: 'Cost' })
      // )
      const hoursData = projectReport?.pieceWorkData?.filter((item: { workTask: string }) =>
        isTextContainsWord({ text: item?.workTask, word: 'Hour' })
      )
      const pieceWorkData = projectReport?.pieceWorkData
        ?.filter((item: { taskName: string }) => item?.hasOwnProperty('taskName'))
        ?.sort((a: { workTask: string }, b: { workTask: string }) => a?.workTask?.localeCompare(b?.workTask))

      setNewPieceworkData({ hoursData, pieceWorkData })

      setLaborReportData(
        projectReport?.laborReport?.map((itm: any) => ({ ...itm, pieceWork: sortData(itm?.pieceWork) }))
      )
      setPoName(`${projectReport?.opportunity?.opp?.PO}-${projectReport?.opportunity?.opp?.num}`)
      const { materials, cost, tax, totalCost } = estimatedMats(projectReport)
      setInventoryMat(materials)
      setCostMat(cost)
      setTaxMat(tax)
      setTotalCostMat(totalCost)
      setModifiedBudget(projectReport?.opportunity?.order?.modifiedBudget)

      setInventoryCost(projectReport?.opportunity?.order?.actualTotals?.inventoryCost)
      setQbCOGS(projectReport?.opportunity?.order?.actualTotals?.qbCOGS)
      setReceipts(projectReport?.opportunity?.order?.actualTotals?.receipts)
      setOther(projectReport?.opportunity?.order?.actualTotals?.other)
      setActualMatCost(projectReport?.opportunity?.order?.actualTotals?.actualMatCost)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }

  function convertDateToCustomFormat(dateString: any) {
    const dateObj = new Date(dateString)
    const month = dateObj.getMonth() + 1 // Adding 1 because getMonth() returns zero-based index (0 for January)
    const day = dateObj.getDate()
    const year = dateObj.getFullYear()
    return `${month}/${day}/${year}`
  }

  function diff(num1: number, num2: number): string {
    const difference = num1 - num2
    const formattedDifference = difference.toFixed(2)
    return formattedDifference
  }

  function totalCal(array: any, prop: any) {
    return totalsCal(array, prop)
  }

  function totalsCal(array: any, property: any) {
    let sum = function (items: any, prop: any) {
      return items?.reduce(function (a: any, b: any) {
        return a + b[prop]
      }, 0)
    }
    let d = sum(array, property)
    return d?.toFixed(2)
  }

  function quotient(num1: number, num2: number) {
    return Number(num1 / num2)?.toFixed(2)
  }

  const refetchData = async () => {
    try {
      setProjectReportLoading(true)
      const dataParams = {
        oppId: projectReportData?.opportunity?.opp?._id,
        projectId: projectReportData?.opportunity?.order?.projectId,
      }

      const result = await getProjectReport(dataParams)
      if (isSuccess(result)) {
        const projectReport = result?.data?.data
        setProjectReportData(projectReport)
        setModifiedBudget(projectReport?.opportunity?.order?.modifiedBudget)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setProjectReportLoading(false)
    }
  }

  const tearOffSquares = (array: any) => {
    const allPitches = array?.map((work: any) => work.pitch)
    const pitchArray = dedupeArray(allPitches)
    const allLayers = array?.map((work: any) => work.layers)
    const layerArray = dedupeArray(allLayers)
    const totals: any = []
    pitchArray?.map((pitch: any) => {
      layerArray?.map((layers: any) => {
        let sq = 0
        let newVal = false
        array?.map((work: any) => {
          if (work.pitch === pitch && work.layers === layers) {
            sq += work.sqs
            newVal = true
          }
        })
        if (newVal) totals.push({ pitch, squares: sq, layers })
      })
    })

    if (totals.length === 0) return 0
    const string = totals?.map((total: any) => {
      return `${total.pitch}/12-${total.layers} LYR: ${Number(
        isNaN(Number(total.squares)) ? 0 : Number(total.squares)?.toFixed(2)
      )} SQ`
    })
    return string?.join(' ')
  }

  const roofingSquares = (array: any) => {
    const allPitches = array?.map((work: any) => work.pitch)
    const pitchArray = dedupeArray(allPitches)
    const totals = pitchArray?.map((pitch: any) => {
      let sq = 0
      array?.map((work: any) => {
        if (work.pitch === pitch) sq += work.sqs
      })
      return { pitch, squares: sq }
    })

    if (totals.length === 0) return 0
    const string = totals?.map((total: any) => {
      return `${total.pitch}/12: ${isNaN(Number(total.squares)) ? 0 : Number(total.squares)?.toFixed(2)} SQ`
    })
    return string?.join(' ')
  }

  const hasValueChanged = (initialValue: string | number, finalValue: string | number) => {
    return initialValue !== finalValue
  }

  const updateData = async () => {
    try {
      setLoading(true)
      const actualTotals = {
        actualPrice: salesPriceValue,
        actualLaborCost: labourCost,
        actualDays: daysValue,
        subcontractorCost: subContractor,
        inventoryCost: inventoryCost,
        qbCOGS: qbCOGS,
        other: other,
        receipts: receipts,
        actualMatCost: actualMatCost,
      }

      const dataParams = {
        orderId: projectReportData?.opportunity?.order?._id,
        actualTotals: actualTotals,
      }
      const result = await projectUpdateOrder(dataParams)
      if (result?.data?.statusCode === 200) {
        notify(simplifyBackendError(result?.data?.data?.message), 'success')
        refetchData()
        setMaterialInput(false)
        setLoading(false)
      }
    } catch (error) {
      console.log(error)
      setLoading(false)
    }
  }

  const handleUpdateData = async (data: any, onClose: () => void) => {
    try {
      setLoading(true)
      const dataParams = {
        orderId: projectReportData?.opportunity?.order?._id,
        ...data,
      }

      const result = await projectUpdateOrder(dataParams)
      if (result?.data?.statusCode === 200) {
        notify(simplifyBackendError(result?.data?.data?.message), 'success')
        refetchData()
      }
    } catch (error) {
      console.log(error)
    } finally {
      onClose()
      setLoading(false)
    }
  }

  const total = (array: any, prop: any) => {
    return totals(array, prop)
  }

  const totals = (array: any, property: any) => {
    var sum = function (items: any, prop: any) {
      return items?.reduce(function (a: any, b: any) {
        return a + b[prop]
      }, 0)
    }
    var d = sum(array, property)
    return d?.toFixed(2)
  }
  // useEffect(() => {
  //   if (currentCompany && currentCompany._id && projectReportData?.opportunity?.order?._id) {
  //     updateData()
  //   }
  // }, [currentCompany, salesPriceValue, labourCost, daysValue, subContractor])

  const roofEstimate = (reroofAreas: any) => {
    const totals = reroofAreas?.map((area: any) => {
      const obj = {
        pitch: area.pitch,
        squares: area.remove,
      }
      return obj
    })
    const string = totals?.map((total: any) => {
      return `${total.pitch}/12: ${total.squares} SQ`
    })
    return string?.join(' ')
  }

  const tearEstimate = (reroofAreas: any) => {
    const totals = reroofAreas?.map((area: any) => {
      const obj = {
        pitch: area.pitch,
        squares: area.remove,
        layers: area.layers,
      }
      return obj
    })
    const string = totals?.map((total: any) => {
      return `${total.pitch}/12-${total.layers} LYR: ${total.squares} SQ`
    })
    return string?.join(' ')
  }
  const inputRef = useRef(null)

  const handleFocus = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Select the text when the field is focused
    event.target.select()
  }

  useEffect(() => {
    setDaysValue(
      Number(
        isNaN(projectReportData?.opportunity?.order?.actualTotals?.actualDays)
          ? null
          : projectReportData?.opportunity?.order?.actualTotals?.actualDays
      )
    )
    setSubContractor(
      Number(
        isNaN(projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost)
          ? null
          : projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost
      )
    )
    setLabourCost(
      Number(
        isNaN(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost)
          ? null
          : projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost
      )
    )
    setSalesPriceValue(
      Number(
        isNaN(projectReportData?.opportunity?.order?.actualTotals?.actualPrice)
          ? null
          : projectReportData?.opportunity?.order?.actualTotals?.actualPrice
      )
    )
  }, [projectReportData])

  const initialValue = {
    projectReport: '',
  }

  const calcHours = (cards: any) => {
    if (!cards) return 0
    const hours = cards.reduce((total: any, card: any) => {
      return card.hrs ? total + card.hrs : total
    }, 0)
    return hours?.toFixed(2)
  }

  const roundTo2 = (num: any) => {
    if (!num) return 0
    return Math.round(num * 100) / 100
  }

  const estimatedMats = (data: any) => {
    const order = data?.opportunity?.order
    const opp = data?.opportunity?.opp
    const materials = order?.matList || []

    if (opp?.state === 'ID') {
      materials?.map((mat: any) => {
        mat.tax = roundTo2(mat.cost * 0.06)
      })
    }
    const permit = {
      amount: order?.priceTotals?.permit ? 1 : 0,
      cost: order?.priceTotals?.permit || 0,
      name: 'Permit',
    }
    const asbTest = {
      amount: order?.priceTotals?.asbTest ? 1 : 0,
      cost: order?.priceTotals?.asbTest || 0,
      name: 'Asbestos Test',
    }
    materials?.push(permit, asbTest)
    let cost = 0
    let tax = 0
    materials?.map((mat: any) => {
      cost += mat.cost
      tax += mat.tax ?? 0
      // tax += roundTo2(mat.tax)
    })
    cost = roundTo2(cost)
    tax = roundTo2(tax)
    const totalCost = roundTo2(cost + tax)
    return {
      materials,
      cost,
      tax,
      totalCost,
    }
  }

  const actualTotalLabor =
    Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) +
    Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) *
      Number(projectReportData?.ttlBurden) +
    Number(projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost)

  const gpEstimatedRatio = projectReportData?.opportunity?.laborBudget
    ? Number(projectReportData?.opportunity?.gross) / Number(projectReportData?.opportunity?.laborBudget)
    : 0
  const gpActualRatio = actualTotalLabor ? Number(projectReportData?.grossProfit) / actualTotalLabor : 0

  const volEstimatedRatio = projectReportData?.opportunity?.laborBudget
    ? Number(projectReportData?.salePrice) / Number(projectReportData?.opportunity?.laborBudget)
    : 0
  const volActualRatio = actualTotalLabor ? Number(salesPriceValue) / Number(actualTotalLabor) : 0

  const actualGpPercent = Number(
    Number(projectReportData?.grossProfit) / Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice)
  )

  return (
    <ProjectReportMainContainer>
      <ProjectReportHeading>Job Cost Report</ProjectReportHeading>
      <div>
        <Formik initialValues={initialValue} onSubmit={() => {}}>
          {({ touched, errors, values, setFieldValue }) => {
            const handleSubmitForm = () => {
              if (values.projectReport.split(':')[0]) {
                setProject(values?.projectReport?.split(':')[0])
                handleProject(values?.projectReport?.split(':')[0])
              }
            }

            useEffect(() => {
              handleSubmitForm()
              setPoName(values.projectReport)
              if (values.projectReport !== '') {
                const value = dropDownProjectValue?.find(
                  (value: any) => `${value?.opp?.PO}-${value?.opp?.num}` === values?.projectReport?.split(':')[0]
                )
                handleDateSelection(value?.projectId, value?.oppId)
              }
            }, [poValue])

            useEffect(() => {
              // if (values.projectReport) {
              if (values.projectReport) {
                setbuttonCall(true)
              }
              setSearchValue(values.projectReport)
              // }
            }, [values.projectReport])
            return (
              <Form>
                {/* {loading && (
                  <Styled.CenteredLoaderWrapper>
                    <SharedStyled.Loader color="grey" />
                  </Styled.CenteredLoaderWrapper>
                )} */}

                {!dropDownProjectValueFlag && (
                  <AutoComplete
                    borderRadius="0px"
                    labelName="Search name, PO#, phone, address"
                    stateName="projectReport"
                    error={touched.projectReport && errors.projectReport ? true : false}
                    setFieldValue={setFieldValue}
                    options={dropDownProjectValue?.map(
                      (value: any) =>
                        `${value?.opp?.PO}-${value?.opp?.num}: ${value?.opp?.client?.firstName ?? ''} ${
                          value?.opp?.client?.lastName ?? ''
                        } ${value?.opp?.startDate ? `| ${value?.opp?.startDate?.split('T')[0]}` : ''}`
                    )}
                    value={values.projectReport}
                    autoFillData={''}
                    onAddClick={() => {
                      setShowAddNewClientModal(true)
                    }}
                    setValueOnClick={setPOValue}
                    apiSearch={true}
                    searchLoader={projectSearch}
                  />
                )}
              </Form>
            )
          }}
        </Formik>
      </div>
      <ProjectReportContentHeading>PO#: {poName.split(':')[0]}</ProjectReportContentHeading>
      <ProjectReportContentSubHeading fontSize="14px">
        <span>
          {projectReportData?.opportunity?.opp?.contactId?.fullName ? (
            <>{projectReportData?.opportunity?.opp?.contactId?.fullName}</>
          ) : null}
          <br />
          {projectReportData?.opportunity?.opp?.jobCompletedDate ? (
            <>Completed: {dayjsFormat(projectReportData?.opportunity?.opp?.jobCompletedDate, 'M/D/YY')}</>
          ) : null}
        </span>{' '}
      </ProjectReportContentSubHeading>
      {isLoading && <>{renderLoadingTable()}</>}
      {projectReportData?.length !== 0 && projectReportData?.data?.statusCode !== 500 && (
        <MainWrapper>
          <SharedStyled.FlexBox>
            <div style={{ width: materialTable ? '70%' : '100%' }}>
              <TableContainer className="project-report">
                <NewTableHeading>
                  <TableTitle>Item</TableTitle>
                  <TableTitle>Budget</TableTitle>
                  <TableTitle>Actual</TableTitle>
                  <TableTitle>Difference</TableTitle>
                </NewTableHeading>
                <NewTableContent fontWeight={600}>
                  <TableContentLabel>Total Volume </TableContentLabel>

                  <TableContentLabel>
                    <SharedStyled.TooltipContainer
                      width="230px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="30px"
                      positionBottomDecs="20px"
                    >
                      <span className="tooltip-content">
                        {Object.entries(projectReportData?.salePriceBreakdown || {})?.map(([k, v]: [string, any]) => {
                          return (
                            <div
                              style={{
                                display: 'grid',
                                gridTemplateColumns: '120px max-content',
                                gap: '4px',
                                justifyContent: 'space-between',
                              }}
                              key={k}
                            >
                              <p style={{ fontWeight: 500, textAlign: 'right', whiteSpace: 'nowrap' }}>
                                {k === 'rrPlywood' ? 'R&R Plywood' : camelCaseToSentenceCase(k)}:
                              </p>

                              <p style={{ fontWeight: 500, textAlign: 'right' }}>
                                {'$'}
                                {v?.toFixed(2)}
                              </p>
                            </div>
                          )
                        })}
                      </span>
                      ${formatNumberToCommaS(projectReportData?.salePrice)}
                    </SharedStyled.TooltipContainer>
                  </TableContentLabel>

                  <TableContentLabel>
                    <TableInputWrapper>
                      <div>$</div>
                      <TableInputField
                        ref={inputRef}
                        type="number"
                        step="0.01"
                        disabled={projectReportLoading}
                        cursor={projectReportLoading ? 'not-allowed' : undefined}
                        value={salesPriceValue}
                        onFocus={handleFocus}
                        onChange={(e) => setSalesPriceValue(Number(e.target.value))}
                        onBlur={() => {
                          if (
                            hasValueChanged(
                              Number(
                                isNaN(projectReportData?.opportunity?.order?.actualTotals?.actualPrice)
                                  ? null
                                  : projectReportData?.opportunity?.order?.actualTotals?.actualPrice
                              ),
                              salesPriceValue
                            )
                          ) {
                            updateData()
                          }
                        }}
                      />
                    </TableInputWrapper>
                  </TableContentLabel>

                  <TableContentLabel>
                    ${formatNumberToCommaS(Number(diff(salesPriceValue, Number(projectReportData?.salePrice))))}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent fontWeight={600}>
                  <TableContentLabel>Materials</TableContentLabel>

                  <TableContentLabel cursorType={true} onClick={() => setMaterialTable(!materialTable)}>
                    <SharedStyled.TooltipContainer
                      width="200px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="30px"
                      positionBottomDecs="20px"
                    >
                      <span className="tooltip-content">
                        {Object.entries(projectReportData?.opportunity?.matBreakdown || {})?.map(
                          ([k, v]: [string, any]) => {
                            return (
                              <div
                                style={{
                                  display: 'grid',
                                  gridTemplateColumns: '100px 1fr',
                                  gap: '4px',
                                }}
                                key={k}
                              >
                                <p style={{ fontWeight: 500, textAlign: 'right', whiteSpace: 'nowrap' }}>
                                  {camelCaseToSentenceCase(k)}:
                                </p>

                                <p style={{ fontWeight: 500, textAlign: 'right' }}>
                                  {'$'}
                                  {v?.toFixed(2)}
                                </p>
                              </div>
                            )
                          }
                        )}
                      </span>
                      ${formatNumberToCommaS(Number(projectReportData?.opportunity?.materialBudget))} &emsp;
                    </SharedStyled.TooltipContainer>
                    {formatNumberToCommaS(Number(projectReportData?.opportunity?.materialBudgetPercent) * 100)}%
                  </TableContentLabel>

                  {loading ? (
                    <SharedStyled.SkeletonLoader height={40} width={200}>
                      <div className="skeleton"></div>
                    </SharedStyled.SkeletonLoader>
                  ) : (
                    <TableContentLabel cursorType={true} onClick={() => setMaterialInput(!materialInput)}>
                      ${formatNumberToCommaS(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)} &emsp;
                      {isFinite(
                        Number(
                          quotient(
                            projectReportData?.opportunity?.order?.actualTotals?.actualMatCost,
                            projectReportData?.opportunity?.order?.actualTotals?.actualPrice
                          )
                        ) * 100
                      )
                        ? formatNumberToCommaS(
                            Number(
                              quotient(
                                projectReportData?.opportunity?.order?.actualTotals?.actualMatCost,
                                projectReportData?.opportunity?.order?.actualTotals?.actualPrice
                              )
                            ) * 100
                          )
                        : '--'}
                      %
                    </TableContentLabel>
                  )}

                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          projectReportData?.opportunity?.order?.actualTotals?.actualMatCost,
                          projectReportData?.opportunity?.materialBudget
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent>

                {/* <NewTableContent fontWeight={600}>
                  <TableContentLabel>RR</TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(Number(projectReportData?.realRevBudget))}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          projectReportData?.opportunity?.order?.actualTotals?.actualPrice,
                          projectReportData?.opportunity?.order?.actualTotals?.actualMatCost
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(
                            diff(
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                            )
                          ),
                          Number(projectReportData?.realRevBudget)
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent> */}
                <NewTableContent>
                  <TableContentLabel>Commission</TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(projectReportData?.opportunity?.commission)}
                  </TableContentLabel>
                  <TableContentLabel>
                    <SharedStyled.TooltipContainer
                      width={
                        projectReportData?.opportunity?.modifiedCommissionBreakDown?.modified?.length
                          ? '200px'
                          : '150px'
                      }
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="30px"
                      positionBottomDecs="20px"
                    >
                      {projectReportData?.opportunity?.modifiedCommissionBreakDown && (
                        <span className="tooltip-content">
                          <SharedStyled.FlexCol>
                            <SharedStyled.FlexRow>
                              <p>Actual:</p>{' '}
                              <p>${projectReportData?.opportunity?.modifiedCommissionBreakDown?.actual?.toFixed(2)}</p>
                            </SharedStyled.FlexRow>

                            {projectReportData?.opportunity?.modifiedCommissionBreakDown?.modified?.length ? (
                              <>
                                <SharedStyled.FlexCol>
                                  {projectReportData?.opportunity?.modifiedCommissionBreakDown?.modified?.map(
                                    (itm: any) => {
                                      return Object.entries(itm).map(([k, v]: [string, any], idx) => {
                                        return (
                                          <SharedStyled.FlexRow key={k}>
                                            <p>Modification:</p>
                                            <p>{formatDollarAmountWithIncrement(v)}</p>
                                          </SharedStyled.FlexRow>
                                        )
                                      })
                                    }
                                  )}
                                </SharedStyled.FlexCol>
                              </>
                            ) : null}
                          </SharedStyled.FlexCol>
                        </span>
                      )}
                      ${formatNumberToCommaS(projectReportData?.opportunity?.modifiedCommission)}
                    </SharedStyled.TooltipContainer>
                  </TableContentLabel>

                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      projectReportData?.opportunity?.commission - projectReportData?.opportunity?.modifiedCommission
                    )}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent>
                  <TableContentLabel>Labor Cost</TableContentLabel>
                  <TableContentLabel>
                    <SharedStyled.TooltipContainer
                      width="230px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="30px"
                      positionBottomDecs="20px"
                    >
                      <span className="tooltip-content">
                        {Object.entries(projectReportData?.opportunity?.laborBreakdown || {})?.map(
                          ([k, v]: [string, any]) => {
                            return (
                              <div style={{ display: 'grid', gridTemplateColumns: '120px 1fr', gap: '4px' }} key={k}>
                                <p style={{ textAlign: 'right', whiteSpace: 'nowrap' }}>
                                  {k === 'rrPlyLaborCost' ? 'R&R Plywood' : camelCaseToSentenceCase(k)}:
                                </p>

                                <p style={{ textAlign: 'right' }}>
                                  {'$'}
                                  {v?.toFixed(2)}
                                </p>
                              </div>
                            )
                          }
                        )}
                        {/* Labor Cost = Labor amount / (1 + Burden %)
                        <br />${formatNumberToCommaS(projectReportData?.opportunity?.rawLaborBudget)} = / (1+{' '}
                        {projectReportData?.opportunity?.laborBreakdown?.burden}% ) */}
                      </span>
                      ${formatNumberToCommaS(projectReportData?.opportunity?.rawLaborBudget)}
                    </SharedStyled.TooltipContainer>
                  </TableContentLabel>
                  <TableContentLabel>
                    <TableInputWrapper>
                      <div>$</div>
                      <TableInputField
                        ref={inputRef}
                        type="number"
                        value={labourCost}
                        disabled={projectReportLoading}
                        cursor={projectReportLoading ? 'not-allowed' : undefined}
                        onFocus={handleFocus}
                        onChange={(e) => setLabourCost(Number(e.target.value))}
                        onBlur={() => {
                          if (
                            hasValueChanged(
                              Number(
                                isNaN(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost)
                                  ? null
                                  : projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost
                              ),
                              labourCost
                            )
                          ) {
                            updateData()
                          }
                        }}
                      />
                    </TableInputWrapper>
                    Auto: ${formatNumberToCommaS(projectReportData.cost)}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost),
                          Number(projectReportData?.opportunity?.rawLaborBudget)
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent>
                  <TableContentLabel>Labor Burden</TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(projectReportData?.opportunity?.laborBurdenBudget)}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) *
                        Number(projectReportData?.ttlBurden)
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) *
                            Number(projectReportData?.ttlBurden),
                          projectReportData?.opportunity?.laborBurdenBudget
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent>
                  <TableContentLabel>Subcontractor</TableContentLabel>
                  <TableContentLabel
                    cursorType={true}
                    onClick={() => (projectReportLoading ? null : setSubContractorInput(true))}
                  >
                    {projectReportLoading ? (
                      <SLoader height={35} width={100} isPercent />
                    ) : (
                      <>${formatNumberToCommaS(projectReportData?.opportunity?.subcontractorTotal)}</>
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    <TableInputWrapper>
                      <div>$</div>
                      <TableInputField
                        ref={inputRef}
                        type="number"
                        value={subContractor}
                        disabled={projectReportLoading}
                        cursor={projectReportLoading ? 'not-allowed' : undefined}
                        onFocus={handleFocus}
                        onChange={(e) => setSubContractor(Number(e.target.value))}
                        onBlur={() => {
                          if (
                            hasValueChanged(
                              Number(
                                isNaN(projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost)
                                  ? null
                                  : projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost
                              ),
                              subContractor
                            )
                          ) {
                            updateData()
                          }
                        }}
                      />
                    </TableInputWrapper>
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(diff(projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost, 0))
                    )}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent fontWeight={600}>
                  <TableContentLabel>Total Labor</TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(Number(projectReportData?.opportunity?.laborBudget))} &emsp;
                    {formatNumberToCommaS(Number(projectReportData?.opportunity?.laborBudgetPercent) * 100)}%
                  </TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(actualTotalLabor)}
                    &emsp;
                    {isFinite(
                      Number(
                        actualTotalLabor / Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice)
                      ) * 100
                    )
                      ? formatNumber(
                          Number(
                            actualTotalLabor / Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice)
                          ) * 100
                        )
                      : '--'}
                    %
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) +
                            Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost) *
                              Number(projectReportData?.ttlBurden) +
                            Number(projectReportData?.opportunity?.order?.actualTotals?.subcontractorCost),
                          Number(projectReportData?.opportunity?.laborBudget)
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent>

                {/* <NewTableContent>
                  <TableContentLabel>RR/$Labor</TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          Number(projectReportData?.realRevBudget),
                          Number(projectReportData?.opportunity?.rawLaborBudget)
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          Number(
                            diff(
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                            )
                          ),
                          Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost)
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(
                            quotient(
                              Number(
                                diff(
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                                )
                              ),
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualLaborCost)
                            )
                          ),
                          Number(
                            quotient(projectReportData?.realRevBudget, projectReportData?.opportunity?.rawLaborBudget)
                          )
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent> */}
                <NewTableContent>
                  <TableContentLabel>Hours</TableContentLabel>
                  <TableContentLabel>
                    {/* {formatNumberToCommaS(
                      Math.round(total(projectReportData?.opportunity?.order?.workOrder, 'ttlHours') * 10) / 10 +
                        projectReportData?.opportunity?.order?.priceTotals?.travelFee /
                          projectReportData?.opportunity?.order?.priceTotals?.travelHrlyRate
                    )} */}
                    {projectReportData?.opportunity?.ttlHours?.toFixed(2)}
                  </TableContentLabel>
                  <TableContentLabel>{Math.round(projectReportData?.hrs * 10) / 10}</TableContentLabel>
                  <TableContentLabel>
                    {Math.round(
                      Number(
                        diff(
                          projectReportData?.hrs,
                          total(projectReportData?.opportunity?.order?.workOrder, 'ttlHours')
                        )
                      ) * 10
                    ) / 10}
                  </TableContentLabel>
                </NewTableContent>

                {/* <NewTableContent>
                  <TableContentLabel>Days</TableContentLabel>
                  <TableContentLabel>{projectReportData?.opportunity?.daysBudget}</TableContentLabel>
                  <TableContentLabel>
                    <TableInputWrapper>
                      auto-calc: {Number(projectReportData?.days).toFixed(1)} &nbsp;
                      <div>$</div>
                      <TableInputField
                        type="text"
                        value={daysValue}
                        onChange={(e) => setDaysValue(Number(e.target.value))}
                        onBlur={updateData}
                      />
                    </TableInputWrapper>
                  </TableContentLabel>
                  <TableContentLabel>
                    {Math.round(Number(diff(daysValue, Number(projectReportData?.opportunity?.daysBudget))) * 10) / 10}
                  </TableContentLabel>
                </NewTableContent> */}

                {/* <NewTableContent>
                  <TableContentLabel>RR/Day</TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          Number(projectReportData?.realRevBudget),
                          Number(projectReportData?.opportunity?.daysBudget)
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          Number(
                            diff(
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                            )
                          ),
                          Number(projectReportData?.opportunity?.order?.actualDays)
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    {' '}
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(
                            quotient(
                              Number(
                                diff(
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                                )
                              ),
                              Number(projectReportData?.opportunity?.order?.actualDays)
                            )
                          ),
                          Number(
                            quotient(projectReportData?.realRevBudget, projectReportData?.opportunity?.rawLaborBudget)
                          )
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent> */}

                {/* totalCal */}
                {/* <NewTableContent>
                  <TableContentLabel>RR/Man-Hour</TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          projectReportData?.realRevBudget,
                          totalCal(projectReportData?.opportunity?.order?.workOrder, 'ttlHours')
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        quotient(
                          Number(
                            diff(
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                              Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                            )
                          ),
                          Number(projectReportData?.hrs)
                        )
                      )
                    )}
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(
                          Number(
                            quotient(
                              Number(
                                diff(
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualPrice),
                                  Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost)
                                )
                              ),
                              Number(projectReportData?.hrs)
                            )
                          ),
                          Number(
                            quotient(
                              projectReportData?.realRevBudget,
                              totalCal(projectReportData?.opportunity?.order?.workOrder, 'ttlHours')
                            )
                          )
                        )
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent> */}

                <NewTableContent fontWeight={600}>
                  <TableContentLabel>Gross Profit</TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(Number(projectReportData?.opportunity?.gross))} &emsp;
                    {(Number(projectReportData?.opportunity?.grossPercent) * 100)?.toFixed(2)}%
                  </TableContentLabel>
                  <TableContentLabel>
                    ${formatNumberToCommaS(Number(projectReportData?.grossProfit))} &emsp;
                    {actualGpPercent ? (actualGpPercent * 100)?.toFixed(2) : '--'}%
                  </TableContentLabel>
                  <TableContentLabel>
                    $
                    {formatNumberToCommaS(
                      Number(
                        diff(Number(projectReportData?.grossProfit), Number(projectReportData?.opportunity?.gross))
                      )
                    )}
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent>
                  <TableContentLabel>Gross Profit/Labor</TableContentLabel>
                  <TableContentLabel>${gpEstimatedRatio?.toFixed(2)}</TableContentLabel>
                  <TableContentLabel>${gpActualRatio?.toFixed(2)}</TableContentLabel>
                  <TableContentLabel>
                    {gpEstimatedRatio ? (((gpActualRatio - gpEstimatedRatio) / gpEstimatedRatio) * 100)?.toFixed(2) : 0}
                    %
                  </TableContentLabel>
                </NewTableContent>
                <NewTableContent>
                  <TableContentLabel>Vol/Labor</TableContentLabel>
                  <TableContentLabel>${volEstimatedRatio?.toFixed(2)}</TableContentLabel>
                  <TableContentLabel>${volActualRatio?.toFixed(2)}</TableContentLabel>
                  <TableContentLabel>
                    {volEstimatedRatio
                      ? (((volActualRatio - volEstimatedRatio) / volEstimatedRatio) * 100)?.toFixed(2)
                      : 0}
                    %
                  </TableContentLabel>
                </NewTableContent>
              </TableContainer>
            </div>
            <div
              style={{
                width: materialTable ? '30%' : '0%',
                display: materialTable ? 'block' : 'none',
                marginLeft: '20px',
              }}
            >
              <ModalHeader>Estimated Inventory</ModalHeader>
              <SharedStyled.FlexBox justifyContent="space-between">
                <SharedStyled.FlexBox flexDirection={'column'} alignItems={'flex-end'}>
                  <p>Cost:</p>
                  <p>Tax:</p>
                  <p>
                    <b>Total</b>
                  </p>
                </SharedStyled.FlexBox>
                &emsp;
                <SharedStyled.FlexBox flexDirection={'column'} alignItems={'flex-start'}>
                  <p>{costMat?.toFixed(2)}</p>
                  <p>{taxMat?.toFixed(2)}</p>
                  <p>
                    <b>{totalCostMat?.toFixed(2)}</b>
                  </p>
                </SharedStyled.FlexBox>
              </SharedStyled.FlexBox>

              <TableContainer>
                <TableHeading column="2fr repeat(2,1fr)">
                  <TableTitle>Name</TableTitle>
                  <TableTitle className="right-align">Amt</TableTitle>
                  <TableTitle className="right-align">Cost</TableTitle>
                </TableHeading>
                <>
                  {inventoryMat?.map((value: any, i: number) => {
                    return (
                      <TableContent column="2fr repeat(2,1fr)" pointer="pointer" key={i}>
                        <TableContentLabel breakAll="break-all">{value?.name}</TableContentLabel>
                        <TableContentLabel breakAll="break-all" className="right-align">
                          {value?.amount?.toFixed(2)}
                        </TableContentLabel>
                        <TableContentLabel breakAll="break-all" className="right-align">
                          ${formatNumberToCommaS(value?.cost)}
                        </TableContentLabel>
                      </TableContent>
                    )
                  })}
                </>
              </TableContainer>
              {/* <Table columns={columns} data={inventoryMat} fetchData={() => {}} noSearch={true} noPagination={true} /> */}
            </div>
          </SharedStyled.FlexBox>
        </MainWrapper>
      )}
      {projectReportData?.opportunity?.opp?._id ? (
        <span
          className="bold"
          style={{ color: 'blue', cursor: 'pointer' }}
          onClick={() => {
            navigate(
              `/${getEnumValue(projectReportData?.opportunity?.opp?.stage?.stageGroup)}/opportunity/${
                projectReportData?.opportunity?.opp?._id
              }`
            )
          }}
        >
          {'Go to opp >>'}
        </span>
      ) : null}
      <ProjectReportContentSubHeading>
        {/* Total Hours: {projectReportData?.hrs?.toFixed(2)} | Total Cost: ${formatNumberToCommaS(projectReportData?.cost)} */}
      </ProjectReportContentSubHeading>
      {/* <ProjectReportContentSubHeading>No Time Cards Found! Or active time card</ProjectReportContentSubHeading> */}
      <CrewProjectDetails
        projectReportData={projectReportData}
        newPieceworkData={newPieceworkData}
        laborReportData={laborReportData}
        isProPlusPlan={isProPlusPlan}
      />
      {/* ======================= Summary ======================= */}
      {/* {projectReportData?.cost ? (
        <>
          <ProjectReportContentHeading style={{ textDecoration: 'underline' }}>Crew Stats</ProjectReportContentHeading>
          <ProjectReportContentSubHeading fontSize={'17px'}>
            Gross Profit/Labor: $
            {(Number(projectReportData?.opportunity?.gross) / Number(projectReportData?.cost))?.toFixed(2)}
          </ProjectReportContentSubHeading>
          <ProjectReportContentSubHeading fontSize={'17px'}>
            Vol/Labor: ${(Number(projectReportData?.salePrice) / Number(projectReportData?.cost))?.toFixed(2)}
          </ProjectReportContentSubHeading>
        </>
      ) : null} */}
      {/* ======================= Summary ======================= */}
      {projectReportData?.logReport?.length ? (
        <TableContainer className="project-report-border">
          <TableHeading>
            <TableTitle>Date</TableTitle>
            <TableTitle>Crew</TableTitle>
            <TableTitle>Tear SQ </TableTitle>
            <TableTitle>Roof SQ</TableTitle>
            <TableTitle>Ply</TableTitle>
            <TableTitle>Hrs</TableTitle>
            <TableTitle>Mats</TableTitle>
            <TableTitle>Temp</TableTitle>
            <TableTitle>Wind</TableTitle>
            <TableTitle>Prec</TableTitle>
            <TableTitle>Worked</TableTitle>
            <TableTitle>#Projects</TableTitle>
            <TableTitle>Notes</TableTitle>
          </TableHeading>

          {projectReportData?.logReport?.map((val: any, index: number) => (
            <TableContent
              key={index}
              onClick={() => {
                // navigate(`/time-cards/edit-daily-log/${val.crewId}/${val.id}`)
                setSelectedCrew(val?.crewId)
                setLogId(val?.id)
                setShowCrewDailyLog(true)
                setReport(dayjsFormat(val?.date, 'M/D/YY'))
              }}
            >
              <TableContentLabel>{convertDateToCustomFormat(val?.date)}</TableContentLabel>
              <TableContentLabel>{val?.crewName}</TableContentLabel>
              <TableContentLabel>{val?.opp?.tearOffDone || val?.opp?.tearOffSQ || 0}</TableContentLabel>
              <TableContentLabel>{val?.opp?.roofingDone || val?.opp?.roofingSQ || 0}</TableContentLabel>
              <TableContentLabel>{val?.opp?.plywoodReplaced || val?.opp?.instSheet || 0}</TableContentLabel>
              <TableContentLabel>{val?.opp?.manHours || val?.opp?.addManHours || 0}</TableContentLabel>
              <TableContentLabel>{val?.opp?.materialCosts || val?.opp?.addMaterials || 0}</TableContentLabel>
              <TableContentLabel>
                {val?.lowTemp || '---'}&#176;/{val?.highTemp || '---'}&#176;
              </TableContentLabel>
              <TableContentLabel>{val?.maxwind_mph || '---'} mph</TableContentLabel>
              <TableContentLabel>{val?.totalprecip_in || '---'} IN</TableContentLabel>
              <TableContentLabel>{val?.worked || '---'}</TableContentLabel>
              <TableContentLabel>{val?.numProjects || '---'}</TableContentLabel>
              <TableContentLabel>
                <SharedStyled.TooltipContainer
                  positionLeft="0"
                  positionBottom="0"
                  positionLeftDecs="40px"
                  positionBottomDecs="25px"
                >
                  <span className="tooltip-content">{val?.opp?.notes}</span>
                  {truncateParagraph(val?.opp?.notes, 15) || '---'}
                </SharedStyled.TooltipContainer>
              </TableContentLabel>
            </TableContent>
          ))}
        </TableContainer>
      ) : null}
      <ButtonWrapper>
        <Button onClick={() => navigate(`/reports`)}>Back To Reports</Button>
      </ButtonWrapper>

      <CustomModal show={subContractorInput}>
        <SubContracctorModal
          onClose={() => {
            setSubContractorInput(false)
          }}
          handleUpdateData={handleUpdateData}
          modifiedBudget={modifiedBudget}
          loading={loading}
        />
      </CustomModal>

      <CustomModal show={materialInput}>
        <MaterialInputModal
          onClose={() => {
            setMaterialInput(false)
          }}
          onSuccess={() => {
            setMediaBool((p) => !p)
          }}
          updateData={updateData}
          setInventoryCost={setInventoryCost}
          setQbCOGS={setQbCOGS}
          setReceipts={setReceipts}
          setOther={setOther}
          setActualMatCost={setActualMatCost}
          loading={loading}
          inventoryCost={inventoryCost}
          qbCOGS={qbCOGS}
          receipts={receipts}
          mediaData={mediaRes?.images}
          other={other}
          oppIdURL={oppIdURL!}
          actualMatCost={actualMatCost}
          projectReportData={projectReportData}
        />
      </CustomModal>
      <CustomModal show={showCrewDailyLog} className="top">
        {showCrewDailyLog && (
          <DailyLog
            isEdit={true}
            onClose={() => {
              setShowCrewDailyLog(false)
              // handleProject(project)
              // getApproveTimeCardsDetail(currentDateupdated)
            }}
            selectedCrewId={selectedCrew}
            logId={logId}
            reportD={reportD}
            isDisableEdit
          />
        )}
      </CustomModal>
    </ProjectReportMainContainer>
  )
}

export default ProjectReport

const renderLoadingTable = () => {
  return (
    <TableContainer className="project-report">
      <NewTableHeading>
        <TableTitle>Item</TableTitle>
        <TableTitle>Budget</TableTitle>
        <TableTitle>Actual</TableTitle>
        <TableTitle>Difference</TableTitle>
      </NewTableHeading>
      <NewTableContent>
        <SharedStyled.FlexCol gap="20px">
          {Array(6)
            .fill(0)
            ?.map((_, idx: number) => (
              <SLoader height={30} width={200} key={idx} />
            ))}
        </SharedStyled.FlexCol>
        <SharedStyled.FlexCol gap="20px">
          {Array(6)
            .fill(0)
            ?.map((_, idx: number) => (
              <SLoader height={30} width={200} key={idx} />
            ))}
        </SharedStyled.FlexCol>
        <SharedStyled.FlexCol gap="20px">
          {Array(6)
            .fill(0)
            ?.map((_, idx: number) => (
              <SLoader height={30} width={200} key={idx} />
            ))}
        </SharedStyled.FlexCol>
        <SharedStyled.FlexCol gap="20px">
          {Array(6)
            .fill(0)
            ?.map((_, idx: number) => (
              <SLoader height={30} width={200} key={idx} />
            ))}
        </SharedStyled.FlexCol>
      </NewTableContent>
    </TableContainer>
  )
}
