import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import * as Yup from 'yup'

import { CrossIcon } from '../../assets/icons/CrossIcon'
import { AvatarSvg } from '../../shared/helpers/images'
import EditIcon from '../../assets/newIcons/edit.svg'
import { getCompanySettings, updateCompany, upsertCompanySettings } from '../../logic/apis/company'
import { getOppClockIn } from '../../logic/apis/projects'
import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import {
  allowedImageTypes,
  FilePathTypeEnum,
  MAX_IMAGE_SIZE,
  SubscriptionPlanType,
  TIME_ZONES,
  WEEK_DAYS,
} from '../../shared/helpers/constants'
import { dayjsFormat, isSuccess, isValidURL, notify } from '../../shared/helpers/util'
import { usStatesShortNames } from '../../shared/helpers/constants'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import Button from '../../shared/components/button/Button'
import { Divider } from '../subscription/components/currentPlan/style'
import { setTriggerRetch } from '../../logic/redux/actions/ui'
import useImageUpload from '../../shared/hooks/useImageUpload'
import { SharedPhone } from '../../shared/sharedPhone/SharedPhone'
import { deleteImageFromS3 } from '../../logic/apis/media'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  defaultPO: string
  weekStartDay: string
  email: string
  phone: string
  weekEndDays: string[]
  address: string
  // city: string
  // state: string
  // zipCode: string
  addDay: string
  workingStates: string[]
  addState: string
  timeZone: string
  dailyOH: number
  dailyLabor: number
  dailyProfit: number
  // salesComm: number
  commission: number
  financeMod: number
  actRevGoal: number
  matMarkup: number
  // repairMarkup: number
  // repairMinimum: number
  travelFee: number
  travelHrlyRate: number
  laborWaste: number
  manHourRate: number
  plywoodRate: number
  weekendBonus: number
  ttlBurden: number
  insWorkersComp: number
  insUnemployment: number
  ssMedicare: number
  // salesTaxWA: number
  gpsEnable: string
  gpsTimeInterval: string
  // salesTaxID: number
}

enum gpsEnableEnum {
  On = 'Mandatory',
  // Optional = 'Optional',
  Off = 'Off',
}

// const gpsDropdownValues = ['Mandatory', 'Optional', 'Off']
const gpsDropdownValues: string[] = ['On', 'Off']

export const removeField = (cd: any, idx: number, fields: Array<any>) => {
  let newField = [...fields.slice(0, idx), ...fields.slice(idx + 1)]
  cd([...newField])
}

const CompanySettings = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    defaultPO: '',
    weekStartDay: '',
    weekEndDays: [],
    address: '',
    email: '',
    phone: '',
    // city: '',
    // state: '',
    // zipCode: '',
    addDay: '',
    workingStates: [],
    addState: '',
    timeZone: '',
    dailyOH: 0,
    dailyLabor: 0,
    dailyProfit: 0,
    // salesComm: 0,
    commission: 0,
    financeMod: 0,
    actRevGoal: 0,
    matMarkup: 0,
    // repairMarkup: 0,
    // repairMinimum: 0,
    travelFee: 0,
    travelHrlyRate: 0,
    laborWaste: 0,
    manHourRate: 0,
    plywoodRate: 0,
    weekendBonus: 0,
    ttlBurden: 0,
    insWorkersComp: 0,
    insUnemployment: 0,
    ssMedicare: 0,
    // salesTaxWA: 0,
    // salesTaxID: 0,
    gpsEnable: '',
    gpsTimeInterval: '',
  })

  const [long, setLong] = useState<string>('')
  const [lat, setLat] = useState<string>('')
  const [address, setAddress] = useState<string>('')
  const [project, setProject] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [addressFlag, setAddressFlag] = useState<boolean>(false)
  const [allDetails, setAllDetails] = useState<any>({})
  const [clockInData, setClockInData] = useState<any>([])
  const [companyData, setCompanyData] = useState<any>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companies, triggerRefetch } = globalSelector.company
  const [selectedSetStages, setSelectedSetStages] = useState<string[]>([])
  const [selectedSetStates, setSelectedSetStates] = useState<string[]>([])
  const [isCompanyEdit, setIsCompanyEdit] = useState(false)
  const dispatch = useDispatch()
  const [avatar, setAvatar] = useState<any>()
  const uploadRef = useRef<any>(null)
  const [imgLoading, setImgLoading] = useState(false)
  const { uploadImage, imageUrl } = useImageUpload()
  const [updateLoading, setUpdateLoading] = useState(false)

  useEffect(() => {
    if (companies?.[0]?.company?.imageUrl) {
      setAvatar(companies?.[0]?.company?.imageUrl)
    }
  }, [companies?.[0]?.company?.imageUrl])
  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  /**
   * CompanySettingsFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CompanySettingsFormSchema = Yup.object().shape({})

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)
      let dataObj = {
        ...submittedValues,
        gpsTimeInterval: Number(submittedValues?.gpsTimeInterval) * 60 * 1000,
        workingStates: selectedSetStates,
        weekEndDays: selectedSetStages,
        // weekStartDay: selectedSetStages,
        // salesComm: submittedValues.salesComm / 100,
        commission: submittedValues.commission / 100,
        financeMod: submittedValues.financeMod / 100,
        matMarkup: submittedValues.matMarkup / 100,
        // repairMarkup: submittedValues.repairMarkup / 100,
        weekendBonus: submittedValues.weekendBonus / 100,
        insWorkersComp: submittedValues.insWorkersComp / 100,
        insUnemployment: submittedValues.insUnemployment / 100,
        ssMedicare: submittedValues.ssMedicare / 100,
        // salesTaxWA: submittedValues.salesTaxWA / 100,
        // salesTaxID: submittedValues.salesTaxID / 100,
        createdBy: currentMember._id,
        latitude: lat,
        longitude: long,
        // @ts-ignore
        gpsEnable: gpsEnableEnum[submittedValues.gpsEnable],
      }

      delete dataObj.ttlBurden

      let response = await upsertCompanySettings(dataObj)
      if (isSuccess(response)) {
        notify('Company Settings Updated Successfully', 'success')
        getCompanySettingDetail()
        setLoading(false)
        setAddressFlag(false)
      } else {
        setLoading(false)
        setAddressFlag(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.log('onSave error', error)
      setAddressFlag(false)
    }
  }

  const getCompanySettingDetail = async () => {
    // if (Object.keys(currentCompany).length > 0) {
    let response = await getCompanySettings()
    if (isSuccess(response)) {
      let data = response?.data?.data?.companySetting

      let dataObj = {
        ...data,
        // salesComm: Number((data.salesComm * 100).toFixed(2)),
        commission: Number((data.commission * 100).toFixed(2)),
        financeMod: Number((data.financeMod * 100).toFixed(2)),
        matMarkup: Number((data.matMarkup * 100).toFixed(2)),
        // repairMarkup: Number((data.repairMarkup * 100).toFixed(2)),
        weekendBonus: Number((data.weekendBonus * 100).toFixed(2)),
        insWorkersComp: Number((data.insWorkersComp * 100).toFixed(2)),
        insUnemployment: Number((data.insUnemployment * 100).toFixed(2)),
        ssMedicare: Number((data.ssMedicare * 100).toFixed(2)),
        gpsEnable: data?.gpsEnable === gpsEnableEnum.On ? 'On' : data?.gpsEnable,
        gpsTimeInterval: Number(Number(data?.gpsTimeInterval) / 1000) / 60,
        // salesTaxWA: Number((data.salesTaxWA * 100).toFixed(2)),
        // salesTaxID: Number((data.salesTaxID * 100).toFixed(2)),
      }
      setCompanyData(data)
      setInitialValues({ ...initialValues, ...dataObj })
      setLong(data?.longitude)
      setLat(data?.latitude)
      setSelectedSetStages(data?.weekEndDays)
      setSelectedSetStates(data?.workingStates)
    } else {
      setLoading(false)
      notify(response?.data?.message, 'error')
    }
    // }
  }

  const getClockIn = async () => {
    try {
      let clockObj: any = []
      const result = await getOppClockIn()
      if (result?.data?.data?.readyOpps?.length) {
        result?.data?.data?.readyOpps.forEach((clockIn: any) => {
          clockObj.push(clockIn?.PO)
        })
        setClockInData(clockObj)
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  useEffect(() => {
    getCompanySettingDetail()
    getClockIn()
  }, [])

  const companyUpdate = async (data: { imageUrl?: string }) => {
    try {
      setUpdateLoading(true)
      const res = await updateCompany(data)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        localStorage.removeItem('currentCompany')

        companies?.[0]?.company?.imageUrl && deleteImageFromS3(companies?.[0]?.company?.imageUrl)

        setIsCompanyEdit(false)

        dispatch(setTriggerRetch(!triggerRefetch))
        setImgLoading(false)
      }
    } catch (error) {
      console.error('updateCompany error', error)
    } finally {
      setUpdateLoading(false)
    }
  }

  useEffect(() => {
    if (imageUrl) {
      companyUpdate({ imageUrl: imageUrl })
    }
  }, [imageUrl])

  const handleAvatarChange = (e: any) => {
    const file = e.target.files[0]
    if (file?.size > MAX_IMAGE_SIZE) {
      notify('File size exceeds the 5MB limit', 'error')
      return
    }

    if (!allowedImageTypes.includes(file?.type)) {
      notify('Only JPEG and PNG file types are allowed', 'error')
      return
    }

    if (file) {
      setAvatar(URL.createObjectURL(file))
      setImgLoading(true)
      uploadImage(file, FilePathTypeEnum.Company)
    }
  }

  const handleCompanySubmit = (values: any) => {
    console.log({ values })
    companyUpdate({ ...values })
  }

  return (
    <Styled.CompanySettingsContainer gap="24px">
      {/* <Styled.CompanySettingsContainer> */}
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Company Settings</SharedStyled.SectionTitle>
      </SharedStyled.FlexRow>

      {/* =============== Company info ===============*/}

      {companies?.map((data: any) => (
        <SharedStyled.FlexCol gap="8px" className="company-info" key={data?.company?._id}>
          <SharedStyled.FlexRow alignItems="flex-start">
            {avatar ? (
              <SharedStyled.FlexRow alignItems="flex-start" width="max-content" gap="0px">
                <input
                  type="file"
                  ref={uploadRef}
                  onChange={handleAvatarChange}
                  accept="image/*"
                  style={{ visibility: 'hidden', width: '0px' }}
                />

                <div className={`avatar-wrapper ${imgLoading ? 'loading' : ''}`}>
                  <img
                    src={avatar ?? AvatarSvg}
                    alt="profile picture"
                    className="logo profile"
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      uploadRef.current.click()
                    }}
                  />
                </div>
              </SharedStyled.FlexRow>
            ) : null}
            <SharedStyled.FlexCol gap="8px">
              <SharedStyled.FlexRow>
                <h1 className="capitalize" style={{ whiteSpace: 'nowrap' }}>
                  {data.company.companyName}
                </h1>

                <SharedStyled.FlexRow className="edit" gap="2px" onClick={() => setIsCompanyEdit(!isCompanyEdit)}>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.5 4.99995L15 7.49995M10.8333 16.6666H17.5M4.16665 13.3333L3.33331 16.6666L6.66665 15.8333L16.3216 6.17829C16.6341 5.86574 16.8096 5.44189 16.8096 4.99995C16.8096 4.55801 16.6341 4.13416 16.3216 3.82162L16.1783 3.67829C15.8658 3.36583 15.4419 3.19031 15 3.19031C14.558 3.19031 14.1342 3.36583 13.8216 3.67829L4.16665 13.3333Z"
                      stroke="black"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>

                  <p>{isCompanyEdit ? 'Cancel' : 'Edit'}</p>
                </SharedStyled.FlexRow>
              </SharedStyled.FlexRow>
              <p>
                <span>Joined on:</span> {dayjsFormat(data.company.createdAt, 'M/D/YY')}
              </p>

              {isCompanyEdit ? (
                <Formik initialValues={{}} onSubmit={handleCompanySubmit}>
                  {({ values }) => {
                    return (
                      <Form style={{ width: '100%' }}>
                        <SharedStyled.FlexCol gap="10px">
                          <InputWithValidation
                            labelName="Company Name"
                            defaultValue={data.company.companyName}
                            stateName="companyName"
                          />

                          <InputWithValidation
                            labelName="Year Company was Founded?"
                            defaultValue={data.company.foundingYear}
                            stateName="foundingYear"
                          />
                          <InputWithValidation
                            labelName="Type Of Work"
                            defaultValue={data.company.workType}
                            stateName="workType"
                            // error={touched.typeOfWork && errors.typeOfWork ? true : false}
                          />

                          <Button width="max-content" type="submit" isLoading={updateLoading}>
                            Save
                          </Button>
                        </SharedStyled.FlexCol>
                      </Form>
                    )
                  }}
                </Formik>
              ) : null}

              {data.company.foundingYear && !isCompanyEdit ? (
                <p>
                  <span>Founding Year:</span> {data.company.foundingYear}
                </p>
              ) : null}
              {data.company.workType && !isCompanyEdit ? (
                <p>
                  <span>Work Type:</span> {data.company.workType}
                </p>
              ) : null}

              {data.company.activeTeamMembers ? (
                <p>
                  <span>Total Team Members:</span> {data.company.activeTeamMembers}
                </p>
              ) : null}
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>

          {/* <p className="capitalize">
                <span>Owner:</span> {camelCaseToSentenceCase(data.member.name)}
              </p> */}
        </SharedStyled.FlexCol>
      ))}
      {/* =============== Company info ===============*/}

      <Divider />

      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={CompanySettingsFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {({ values, handleChange, setFieldValue, touched, errors }) => {
          return (
            <Form className="form">
              <SharedStyled.FlexRow gap="16px" alignItems="flex-start">
                <Styled.SingleFieldNameContainer>
                  <SharedStyled.FlexCol gap="6px">
                    <Styled.NameText>Default PO# to clock in on company time</Styled.NameText>
                    <Styled.NameValueUnitContainer width="100%">
                      <InputWithValidation
                        labelName=""
                        stateName="defaultPO"
                        padding="20px 16px"
                        error={touched.address && errors.address ? true : false}
                      />
                      {/* <Styled.ValueInput borderRadius="4px" name="defaultPO" component="select" className="po">
                        {clockInData?.map((value: string) => (
                          <option value={value}>{value}</option>
                        ))}
                      </Styled.ValueInput> */}
                    </Styled.NameValueUnitContainer>
                  </SharedStyled.FlexCol>
                </Styled.SingleFieldNameContainer>
              </SharedStyled.FlexRow>

              <SharedStyled.FlexRow gap="16px" margin="12px 0 0 0">
                <Styled.SingleFieldNameContainer>
                  <SharedStyled.FlexCol gap="6px">
                    <Styled.NameText>Company Contact Information</Styled.NameText>
                    <SharedStyled.TwoInputDiv>
                      <SharedPhone
                        labelName="Phone"
                        stateName="phone"
                        value={values.phone || ''}
                        onChange={handleChange('phone')}
                      />

                      <InputWithValidation labelName="Email" stateName="email" value={values.email} />
                    </SharedStyled.TwoInputDiv>
                  </SharedStyled.FlexCol>
                </Styled.SingleFieldNameContainer>
              </SharedStyled.FlexRow>

              {isProPlusPlan ? (
                <SharedStyled.FlexCol gap="20px" margin="12px 0 0 0">
                  <SharedStyled.FlexCol gap="6px">
                    <Styled.NameText>What are Working States?</Styled.NameText>
                    <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
                      <CustomSelect
                        dropDownData={usStatesShortNames}
                        setValue={() => {}}
                        stateName="addState"
                        value={values.addState}
                        // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Select State"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="7px"
                        mediaHeight="52px"
                        type="button"
                        disabled={selectedSetStates.includes(values.addState)}
                        onClick={() => {
                          let val = values.addState
                          setSelectedSetStates((prev) => [...prev, val])
                        }}
                      >
                        <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                      </SharedStyled.Button>
                    </SharedStyled.FlexBox>
                  </SharedStyled.FlexCol>
                  <SharedStyled.FlexCol>
                    <SharedStyled.Text fontWeight="500" fontSize="14px" width="100%" textAlign="flex-start">
                      {selectedSetStates.length > 0 && 'Selected states :'}
                    </SharedStyled.Text>
                    <Styled.OptionsWrapper>
                      {selectedSetStates.map((field, idx) => (
                        <div
                          key={idx}
                          className="option"
                          onClick={() =>
                            removeField((values: string[]) => setSelectedSetStates(values), idx, selectedSetStates)
                          }
                        >
                          {field} <CrossIcon />
                        </div>
                      ))}
                    </Styled.OptionsWrapper>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexCol>
              ) : null}

              <SharedStyled.FlexCol gap="20px" margin="12px 0 0 0">
                <SharedStyled.FlexCol gap="6px">
                  <Styled.NameText>What day does your week start for payroll? (to calculate overtime)</Styled.NameText>
                  <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
                    <CustomSelect
                      dropDownData={WEEK_DAYS}
                      setValue={() => {}}
                      stateName="weekStartDay"
                      value={values.weekStartDay}
                      // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                      setFieldValue={setFieldValue}
                      labelName="Select day"
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />
                  </SharedStyled.FlexBox>
                </SharedStyled.FlexCol>
              </SharedStyled.FlexCol>

              <SharedStyled.FlexCol gap="20px" margin="12px 0 0 0">
                {isProPlusPlan ? (
                  <SharedStyled.FlexCol gap="6px">
                    <Styled.NameText>What are the default weekend days for your company?</Styled.NameText>
                    <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
                      <CustomSelect
                        dropDownData={WEEK_DAYS}
                        setValue={() => {}}
                        stateName="addDay"
                        value={values.addDay}
                        // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Select days"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="7px"
                        mediaHeight="52px"
                        type="button"
                        disabled={selectedSetStages.includes(values.addDay)}
                        onClick={() => {
                          let val = values.addDay
                          setSelectedSetStages((prev) => [...prev, val])
                        }}
                      >
                        <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                      </SharedStyled.Button>
                    </SharedStyled.FlexBox>
                  </SharedStyled.FlexCol>
                ) : null}
                <SharedStyled.FlexCol>
                  {isProPlusPlan ? (
                    <>
                      <SharedStyled.Text fontWeight="500" fontSize="14px" width="100%" textAlign="flex-start">
                        {selectedSetStages.length > 0 && 'Selected days :'}
                      </SharedStyled.Text>
                      <Styled.OptionsWrapper>
                        {selectedSetStages.map((field, idx) => (
                          <div
                            key={idx}
                            className="option"
                            onClick={() =>
                              removeField((values: string[]) => setSelectedSetStages(values), idx, selectedSetStages)
                            }
                          >
                            {field} <CrossIcon />
                          </div>
                        ))}
                      </Styled.OptionsWrapper>
                    </>
                  ) : null}
                  {!addressFlag && (
                    <SharedStyled.FlexBox width="290px">
                      {values.address !== '' ? (
                        <Styled.NameText>
                          <b>Company Address: </b> <br />
                          {values.address}
                        </Styled.NameText>
                      ) : (
                        <Styled.NameText>
                          <b>Company Address: </b> <br />
                          N/A
                        </Styled.NameText>
                      )}

                      <SharedStyled.FlexRow className="edit" gap="2px" onClick={() => setAddressFlag(true)}>
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M12.5 4.99995L15 7.49995M10.8333 16.6666H17.5M4.16665 13.3333L3.33331 16.6666L6.66665 15.8333L16.3216 6.17829C16.6341 5.86574 16.8096 5.44189 16.8096 4.99995C16.8096 4.55801 16.6341 4.13416 16.3216 3.82162L16.1783 3.67829C15.8658 3.36583 15.4419 3.19031 15 3.19031C14.558 3.19031 14.1342 3.36583 13.8216 3.67829L4.16665 13.3333Z"
                            stroke="black"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>

                        <p>edit</p>
                      </SharedStyled.FlexRow>
                      {/* <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="7px"
                        mediaHeight="52px"
                        type="button"
                        onClick={() => setAddressFlag(true)}
                      >
                        <svg
                          stroke="currentColor"
                          fill="currentColor"
                          stroke-width="0"
                          viewBox="0 0 24 24"
                          height="1em"
                          width="1em"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M9.24264 18.9964H21V20.9964H3V16.7538L12.8995 6.85431L17.1421 11.0969L9.24264 18.9964ZM14.3137 5.44009L16.435 3.31877C16.8256 2.92825 17.4587 2.92825 17.8492 3.31877L20.6777 6.1472C21.0682 6.53772 21.0682 7.17089 20.6777 7.56141L18.5563 9.68273L14.3137 5.44009Z"></path>
                        </svg>
                      </SharedStyled.Button> */}
                      {/* <InputWithValidation
                      labelName="Street Address"
                      stateName="address"
                      error={touched.address && errors.address ? true : false}
                    />
                    <InputWithValidation
                      labelName="City"
                      stateName="city"
                      error={touched.city && errors.city ? true : false}
                      twoInput={true}
                    />
                    <InputWithValidation
                      labelName="State"
                      stateName="state"
                      error={touched.state && errors.state ? true : false}
                      twoInput={true}
                    />
                    <InputWithValidation
                      labelName="Zip"
                      stateName="zipCode"
                      error={touched.zipCode && errors.zipCode ? true : false}
                    /> */}
                    </SharedStyled.FlexBox>
                  )}
                </SharedStyled.FlexCol>
              </SharedStyled.FlexCol>

              {addressFlag && (
                <SharedStyled.FlexBox width="380px">
                  <div style={{ width: '100%' }}>
                    <AutoCompleteAddress
                      complemteAddress={'address'}
                      setFieldValue={setFieldValue}
                      setLat={setLat}
                      setLong={setLong}
                    />
                  </div>
                  &nbsp;
                  <SharedStyled.Button
                    maxWidth="50px"
                    marginTop="7px"
                    mediaHeight="52px"
                    type="button"
                    onClick={() => {
                      setAddressFlag(false)
                      setFieldValue('address', companyData?.address)
                    }}
                  >
                    <SharedStyled.IconCode className="cross">&#x2B;</SharedStyled.IconCode>
                  </SharedStyled.Button>
                </SharedStyled.FlexBox>
              )}
              {/* <Styled.SingleFieldNameContainer marginTop="10px">
                <Styled.NameValueUnitContainer>
                  <Styled.ValueInput borderRadius="4px" name="weekStartDay" minWidth="230px" component="select">
                    {WEEK_DAYS.map((value: string) => (
                      <option value={value}>{value}</option>
                    ))}
                  </Styled.ValueInput>
                </Styled.NameValueUnitContainer>
                <Styled.NameText>What day does your week start? (For calculating OT)</Styled.NameText>
              </Styled.SingleFieldNameContainer> */}

              {/* <Styled.SingleFieldNameContainer marginTop="16px">
                <Styled.NameText>What time zone are you in?</Styled.NameText>

                <Styled.NameValueUnitContainer width="50%">
                  <Styled.ValueInput $borderRadius="4px" name="timeZone" component="select" className="po">
                    {TIME_ZONES.map((value: string) => (
                      <option key={value} value={value}>
                        {value}
                      </option>
                    ))}
                  </Styled.ValueInput>
                </Styled.NameValueUnitContainer>
              </Styled.SingleFieldNameContainer> */}

              <Styled.SingleFieldNameContainer marginTop="16px" style={{ width: '50%' }}>
                <Styled.NameText className="gps">Mobile App GPS:</Styled.NameText>

                <CustomSelect
                  dropDownData={gpsDropdownValues}
                  setValue={() => {}}
                  stateName="gpsEnable"
                  value={values.gpsEnable ?? ''}
                  setFieldValue={setFieldValue}
                  labelName="Select one"
                  innerHeight="52px"
                  margin="10px 0 0 0"
                  showInitialValue
                />
              </Styled.SingleFieldNameContainer>

              {isProPlusPlan ? (
                <Styled.SingleFieldNameContainer marginTop="16px" style={{ width: '50%' }}>
                  {/* <Styled.NameText>GPS Time Interval:</Styled.NameText>

                  <Styled.NameValueUnitContainer>
                    <Styled.ValueInput
                      name="gpsTimeInterval"
                      type="number"
                      $borderRadius="4px 0px 0px 4px"
                      onWheel={(e: any) => {
                        e.target.blur()
                      }}
                      defaultValue={values?.gpsTimeInterval}
                    />
                    <Styled.UnitDiv>min</Styled.UnitDiv>
                  </Styled.NameValueUnitContainer> */}
                </Styled.SingleFieldNameContainer>
              ) : null}

              {isProPlusPlan ? (
                <>
                  <Styled.FieldHeader marginTop="16px">Sales variables</Styled.FieldHeader>
                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>What is your Overhead expense per day?</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          type="number"
                          name="dailyOH"
                          $borderRadius="0px 4px 4px 0px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>

                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>What is your COGS labor expense per day?</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="dailyLabor"
                          type="number"
                          $borderRadius="0px 4px 4px 0px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>

                  <SharedStyled.FlexCol width="50%">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Desired Profit per day</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="dailyProfit"
                          type="number"
                          $borderRadius="0px 4px 4px 0px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    {/* <Styled.SingleFieldNameContainer marginTop="16px">
                  <Styled.NameText>Sales Commission (of ACTUAL REVENUE)</Styled.NameText>
                  <Styled.NameValueUnitContainer>
                    <Styled.ValueInput
                      name="salesComm"
                      type="number"
                      borderRadius="4px 0px 0px 4px"
                      onWheel={(e: any) => {
                        e.target.blur()
                      }}
                    />
                    <Styled.UnitDiv>%</Styled.UnitDiv>
                  </Styled.NameValueUnitContainer>
                </Styled.SingleFieldNameContainer> */}
                  </SharedStyled.FlexCol>
                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Sales Commission (of TOTAL SALE PRICE)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="commission"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Finance Modifier (added on top)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="financeMod"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>
                  <Styled.SingleFieldNameContainer marginTop="16px">
                    <SharedStyled.FlexRow justifyContent="space-between" className="heading">
                      <Styled.NameText>Actual Revenue Goal per day</Styled.NameText>
                      <Styled.FieldText>{values.actRevGoal}</Styled.FieldText>
                    </SharedStyled.FlexRow>
                  </Styled.SingleFieldNameContainer>
                  <SharedStyled.FlexCol width="50%">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Material Markup</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="matMarkup"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>

                    {/* <Styled.SingleFieldNameContainer marginTop="16px">
                  <Styled.NameText>Repair Markup</Styled.NameText>
                  <Styled.NameValueUnitContainer>
                    <Styled.ValueInput
                      name="repairMarkup"
                      type="number"
                      borderRadius="4px 0px 0px 4px"
                      onWheel={(e: any) => {
                        e.target.blur()
                      }}
                    />
                    <Styled.UnitDiv>%</Styled.UnitDiv>
                  </Styled.NameValueUnitContainer>
                </Styled.SingleFieldNameContainer> */}
                  </SharedStyled.FlexCol>
                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Travel Fee (per mile per man from shop)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="travelFee"
                          type="number"
                          $borderRadius="0px 4px 4px 0px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>

                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Travel Hourly Rate (per mile per man from shop)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="travelHrlyRate"
                          type="number"
                          $borderRadius="0px 4px 4px 0px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>

                  {/* <Styled.SingleFieldNameContainer marginTop="16px">
                <Styled.NameText>Repair Minimum Charge</Styled.NameText>
                <Styled.NameValueUnitContainer>
                  <Styled.NameDiv>$</Styled.NameDiv>
                  <Styled.ValueInput
                    name="repairMinimum"
                    type="number"
                    borderRadius="0px 4px 4px 0px"
                    onWheel={(e: any) => {
                      e.target.blur()
                    }}
                  />
                </Styled.NameValueUnitContainer>
              </Styled.SingleFieldNameContainer> */}

                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Labor Waste (per 8 hour work day)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="laborWaste"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>min</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Hourly Labor Charge</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="manHourRate"
                          type="number"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>/HR</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>R&R Plywood per Square Foot</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="plywoodRate"
                          type="number"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>/SF</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Weekend Piece Work Bonus</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="weekendBonus"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexRow>
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>R&R Plywood Sheet (Labor Only)</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.NameDiv>$</Styled.NameDiv>
                        <Styled.ValueInput
                          name="plywoodLaborRate"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>/SHT</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    <Styled.SingleFieldNameContainer marginTop="16px">&nbsp;</Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexRow className="heading">
                    <Styled.FieldHeader>Labor Burden - Total:</Styled.FieldHeader>
                    <Styled.FieldHeader>{(values.ttlBurden * 100)?.toFixed(2)}%</Styled.FieldHeader>
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexRow gap="16px">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Worker's Comp on Labor</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="insWorkersComp"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Unemployment Insurance</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="insUnemployment"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexCol width="50%">
                    <Styled.SingleFieldNameContainer marginTop="16px">
                      <Styled.NameText>Social Security & Medicare</Styled.NameText>
                      <Styled.NameValueUnitContainer>
                        <Styled.ValueInput
                          name="ssMedicare"
                          type="number"
                          $borderRadius="4px 0px 0px 4px"
                          onWheel={(e: any) => {
                            e.target.blur()
                          }}
                        />
                        <Styled.UnitDiv>%</Styled.UnitDiv>
                      </Styled.NameValueUnitContainer>
                    </Styled.SingleFieldNameContainer>
                  </SharedStyled.FlexCol>
                </>
              ) : null}

              <SharedStyled.FlexRow margin="20px 0 0 0">
                <Button type="submit" isLoading={loading} width="200px">
                  Save
                </Button>
              </SharedStyled.FlexRow>
            </Form>
          )
        }}
      </Formik>
      {/* </Styled.CompanySettingsContainer> */}
    </Styled.CompanySettingsContainer>
  )
}

export default CompanySettings
