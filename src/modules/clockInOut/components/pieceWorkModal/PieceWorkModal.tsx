import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import * as Yup from 'yup'
import { Fragment, useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { Field, FieldArray, Form, Formik } from 'formik'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'

import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import {
  onlyNumber,
  onlyPositiveNumberRegex,
  onlyPositiveNumberWithTwoDecimalsRegex,
  twoDecimal,
} from '../../../../shared/helpers/regex'
import {
  dayjsFormat,
  getDataFromLocalStorage,
  handleWheel,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { createPieceWork, getPieceWorkByTaskId } from '../../../../logic/apis/pieceWork'
import { useSelector } from 'react-redux'
import { getMemberTasks } from '../../../../logic/apis/task'
import { getOppClockInMobile } from '../../../../logic/apis/projects'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { SwitchFromCont, SwitchToCont, TimecardCont } from '../../../timeCard/components/addTimeCardPopUp/style'
import useWindowDimensions from '../../../../shared/hooks/useWindowDimensions'
import { LAYERS, Nue, PITCHES, StorageKey } from '../../../../shared/helpers/constants'
import { removeDuplicates } from '../../../timeCard/components/addTimeCardPopUp/AddTimeCardPopUp'
import { useAppSelector } from '../../../../logic/redux/reduxHook'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'

export const mergeArrays = (a: any[], b: any[], field: string) => {
  const merged = []

  // Iterate through array 'b'
  for (const itemB of b) {
    let found = false
    // Iterate through array 'a'
    for (const itemA of a) {
      // If 'name' matches, merge properties from 'a' and 'b', prioritizing values from 'b'
      if (itemA[field] === itemB[field]) {
        merged.push({ ...itemA, ...itemB })
        found = true
        break
      }
    }
    // If 'name' not found in 'a', add 'b' item as it is
    if (!found) {
      merged.push(itemB)
    }
  }

  return merged
}

interface I_ExtraTime {
  hours: string
  minutes: string
}

interface InitialValues {
  project: string
  task: string
  switchedProject?: string
  switchedTask?: string
  allHourlyPayCheckbox: boolean
  workDone: Array<any>
  extraTime: any
  notes: string
  pieceWork?: any[]
  poIdData?: any
  clockOutTime?: string
}

interface I_PieceWorkModal {
  setShowPieceWorkModal: React.Dispatch<React.SetStateAction<boolean>>
  actionName: string
  setIsClockedIn: React.Dispatch<React.SetStateAction<boolean>>
  projectInitial: string
  taskInitial: string
  clockedOutTime: string
  clockedInTimeDate: Date
  // clockedOutTimeDate: Date
  recentTimeCardId: string
  loading: boolean
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
  onFinalSwitch: (pieceWorkTask?: string, allHourly?: boolean, dataObj?: Record<string, any>) => void
  onFinalClockOut: (pieceWorkTask?: string, allHourly?: boolean, dataObj?: Record<string, any>) => void
  setTask: React.Dispatch<React.SetStateAction<string>>
  workTaskData?: any
  setProject: React.Dispatch<React.SetStateAction<string>>
  setNewProject: React.Dispatch<React.SetStateAction<string>>
  isCrewLead: boolean
  setClockedOutTimeDate?: any
}

export const PieceWorkModal = (props: I_PieceWorkModal) => {
  const {
    setShowPieceWorkModal,
    actionName,
    setIsClockedIn,
    projectInitial,
    taskInitial,
    clockedOutTime,
    clockedInTimeDate,
    // clockedOutTimeDate,
    onFinalSwitch,
    onFinalClockOut,
    recentTimeCardId,
    loading,
    setLoading,
    setTask,
    workTaskData,
    setNewProject,
    isCrewLead,
    setClockedOutTimeDate,
    setProject,
  } = props
  const [pieceworkByTaskId, setPieceworkByTaskId] = useState([])
  const { positionDetails } = useAppSelector((state) => state.company)
  const isPieceworkPosition = positionDetails?.isPieceworkPosition
  const [selectedPoName, setSelectedPoName] = useState('')
  const [selectedTask, setSelectedTask] = useState<any>('')

  const isSwitch = actionName === 'Switch'

  const { isMobile } = useWindowDimensions()
  const [initialValues, setInitialValues] = useState<InitialValues>({
    project: '',
    task: '',
    switchedTask: '',
    switchedProject: '',
    allHourlyPayCheckbox: false,
    clockOutTime: clockedOutTime,
    workDone: [{ pitch: 0, sqs: 0, layers: 0 }],

    pieceWork: pieceworkByTaskId?.length
      ? [
          {
            name: '',
            value: '',
            pitch: '',
            layers: '',
          },
        ]
      : [],
    extraTime: {
      hours: 0,
      minutes: 0,
    },
    notes: '',
    poIdData: '',
  })

  const [allTaskData, setAllTaskData] = useState([])

  const [showExtra, setShowExtra] = useState<boolean>(false)
  const [taskData, setTaskData] = useState<any>([])
  const [taskIdData, setTaskIdData] = useState<any>([])

  const [IdTaskData, setIdTaskData] = useState<any>([])
  const [pickPoData, setPickPoData] = useState<any>([])
  const [pitch, setPitch] = useState([])
  const [layer, setLayer] = useState([])
  const [isHourly, setIsHourly] = useState(false)

  const [pieceworkLoading, setPieceworkLoading] = useState(true)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const workDoneValidationSchema = Yup.object().shape({
    name: Yup.string()
      .optional()
      .when([], {
        is: (_val: string) => isPieceworkPosition && !isHourly,
        then: Yup.string().required('Required'),
      }),
    value: Yup.string()
      .optional()
      .matches(twoDecimal, 'Only numbers with upto two decimals are allowed')
      .matches(onlyPositiveNumberWithTwoDecimalsRegex, 'Enter number greater than zero')
      .when('name', {
        is: (val: string) => val !== 'Extra hours' && isPieceworkPosition && !isHourly,
        then: Yup.string().required('Required'),
      }),
    layers: Yup.string()
      .nullable()
      .when('name', {
        is: (val: string) =>
          !!(pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.hasLayer && !!layer?.length),
        then: Yup.string().required('Required'),
      }),
    pitch: Yup.string()
      .nullable()
      .when('name', {
        is: (val: string) =>
          !!(pieceworkByTaskId?.find((itm: { name: string }) => itm?.name === val)?.usesPitch && !!pitch?.length),
        then: Yup.string().required('Required'),
      }),
  })

  const extraTimeValidationSchema = Yup.object().shape({
    hours: Yup.string().matches(onlyNumber, 'Enter Valid value'),
    minutes: Yup.string().matches(onlyNumber, 'Enter Valid value'),
  })

  /**
   * PieceWorkModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const PieceWorkModalSchema = Yup.object().shape({
    project: Yup.string(),
    task: Yup.string(),
    switchedTask: Yup.string()
      .optional()
      .when([], {
        is: () => isSwitch,
        then: Yup.string().required('Required'),
      }),
    switchedProject: Yup.string()
      .optional()
      .when([], {
        is: () => isSwitch,
        then: Yup.string().required('Required'),
      }),
    pieceWork: Yup.array()
      .nullable()
      .of(workDoneValidationSchema)
      .when('project', {
        is: () => !(pieceworkByTaskId && pieceworkByTaskId.length),
        then: Yup.array().notRequired(),
      }),
    extraTime: extraTimeValidationSchema,
  })

  useEffect(() => {
    if (projectInitial && taskInitial)
      setInitialValues({ ...initialValues, project: projectInitial, task: taskInitial })
  }, [projectInitial, taskInitial])

  const pickPO = async () => {
    try {
      let clockObj: any = []
      const result = await getOppClockInMobile()
      if (result?.data?.data?.readyOpps?.length) {
        result?.data?.data?.readyOpps.forEach((clockIn: any) => {
          clockObj.push(clockIn)
        })
        setPickPoData(clockObj)
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  useEffect(() => {
    pickPO()
  }, [currentMember])

  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      if (
        currentCompany &&
        Object.keys(currentCompany)?.length > 0 &&
        currentMember &&
        Object.keys(currentMember)?.length > 0
      ) {
        setTask(submittedValues.task)

        let currentDate = new Date()
        let submittedHours = Number(submittedValues?.clockOutTime!.split(':')[0])
        let submittedMinutes = Number(submittedValues?.clockOutTime!.split(':')[1])
        let submittedSeconds = currentDate.getSeconds()
        let submittedMilliseconds = currentDate.getMilliseconds()
        let submittedTime = currentDate.setHours(
          submittedHours,
          submittedMinutes,
          submittedSeconds,
          submittedMilliseconds
        )
        let clockedInTime = clockedInTimeDate.getTime()
        let currentTime = new Date().getTime()
        if (submittedTime < clockedInTime) {
          notify('Cannot clock-out before clock-in time', 'error')
          return
        } else if (submittedTime > currentTime) {
          notify('Cannot clockout in future', 'error')
          return
        }

        const modifiedPieceWork = submittedValues?.pieceWork
          ?.filter((itm) => itm?.name !== 'Extra hours')
          ?.map((p) => {
            const selectedPiecework = pieceworkByTaskId?.find((pw: { name: string }) => pw?.name === p?.name)

            return {
              pitch:
                typeof p?.pitch === 'string' && !!pitch?.length && selectedPiecework?.usesPitch
                  ? Number(p?.pitch?.split('/')[0]?.trim())
                  : !!pitch?.length && selectedPiecework?.usesPitch
                  ? p?.pitch
                  : undefined,

              layers: p?.layers && !!layer?.length && selectedPiecework?.hasLayer ? Number(p?.layers) : undefined,
              amount: p?.value,
              id: selectedPiecework?._id,
            }
          })

        setLoading(true)
        let clockInTimeInSeconds = clockedInTimeDate.getTime()
        let currentTimeInSeconds = new Date().getTime()
        let timeClockedIn = currentTimeInSeconds - clockInTimeInSeconds
        let hoursInSeconds = Number(submittedValues.extraTime.hours) * 60 * 60
        let minutesInSeconds = Number(submittedValues.extraTime.minutes) * 60
        let totalExtraTimeInMilliseconds = (hoursInSeconds + minutesInSeconds) * 1000
        let diffBetweenClockedInAndExtraTime = timeClockedIn - totalExtraTimeInMilliseconds

        const hrs = submittedValues.extraTime.hours ? Number(submittedValues.extraTime.hours) : 0
        const min = submittedValues.extraTime.minutes ? Number(submittedValues.extraTime.minutes) : 0

        if (diffBetweenClockedInAndExtraTime < 0) {
          notify('Cannot put extra time more than actual worked time!', 'error')
          setLoading(false)
          return
        }

        // if (!submittedValues.allHourlyPayCheckbox) {
        //   if (submittedValues.task === 'Repairs' || submittedValues.task === 'Office') {
        //     submittedValues.allHourlyPayCheckbox = true
        //   }
        // }
        setProject(submittedValues.project)
        clockedInTimeDate.setSeconds(0)
        clockedInTimeDate.setMilliseconds(0)
        const clockedOutTimeDate = new Date(submittedTime)
        clockedOutTimeDate.setSeconds(0)
        clockedOutTimeDate.setMilliseconds(0)

        setClockedOutTimeDate?.(clockedOutTimeDate)

        const finalTimeIn = new Date(clockedInTimeDate).toISOString()
        const finalTimeOut = new Date(clockedOutTimeDate).toISOString()

        let dataObj = {
          timeCardId: recentTimeCardId,
          memberId: currentMember._id,
          task: submittedValues?.task,
          notes: submittedValues.notes,
          timeIn: finalTimeIn,
          timeOut: finalTimeOut,
          allHourly: submittedValues.allHourlyPayCheckbox,
          projectId: submittedValues.project,
          foreman: isCrewLead,

          work: isPieceworkPosition
            ? {
                workDone: submittedValues.allHourlyPayCheckbox ? [] : removeDuplicates(modifiedPieceWork),
                // extra: extras,
                extraTime: { extraHrs: submittedValues.extraTime.hours, extraMin: submittedValues.extraTime.minutes },
              }
            : undefined,
          createdBy: currentMember._id,
          date: new Date(),
        }

        let response = await createPieceWork(dataObj)

        if (isSuccess(response)) {
          if (actionName === 'Switch') {
            onFinalSwitch(submittedValues.task, submittedValues.allHourlyPayCheckbox, {
              notes: dataObj?.notes,
              clockOutDateTime: dataObj?.timeOut,
              project: selectedPoName,
              task: submittedValues?.switchedTask,
              switchText: `Switched to PO#: ${submittedValues?.switchedProject}, Task: ${
                submittedValues?.switchedTask
              } @ ${dayjsFormat(dataObj?.timeOut, 'H:mm')}`,
            })
            // setLoading(false)
          } else {
            onFinalClockOut(submittedValues.task, submittedValues.allHourlyPayCheckbox, {
              notes: dataObj?.notes,
              clockOutDateTime: dataObj?.timeOut,
            })
            // setLoading(false)
          }
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      }
    } catch (error) {
      console.error('PieceWorkModal handleSubmit', error)
      setLoading(false)
    }
  }

  const getTasks = async () => {
    try {
      if (
        currentCompany &&
        Object.keys(currentCompany)?.length > 0 &&
        currentMember &&
        Object.keys(currentMember)?.length > 0
      ) {
        let taskResponse = await getMemberTasks({}, currentMember._id)

        if (isSuccess(taskResponse)) {
          let workTasks = taskResponse?.data?.data?.workTask
          let taskObj: any = []
          let taskIdObj: any = {}
          let idTaskObj: any = {}

          workTasks.forEach((workTask: any) => {
            taskObj.push(workTask.name)
            taskIdObj = { ...taskIdObj, [`${workTask.name}`]: workTask._id }
            idTaskObj = { ...idTaskObj, [`${workTask._id}`]: workTask.name }
          })
          setTaskData(taskObj)
          setAllTaskData(workTasks)
          setTaskIdData(taskIdObj)
          setIdTaskData(idTaskObj)
        } else {
          notify(taskResponse?.data?.message, 'error')
        }
      }
    } catch (error) {
      console.error('getTasks error', error)
    }
  }

  useEffect(() => {
    getTasks()
  }, [currentCompany, currentMember])

  const getValueByKey = (poValue: string) => {
    const result = pickPoData.find((item: any) => item?._id === poValue)
    return result ? result._id : ''
  }

  const fetchPitchLayer = async (Id: string) => {
    try {
      const res = pickPoData?.find((itm: any) => itm?._id === Id)

      setPitch(res?.pitches?.filter(Boolean)?.length ? res?.pitches?.filter(Boolean) : PITCHES)
      setLayer(res?.layers?.filter(Boolean)?.length ? res?.layers?.filter(Boolean) : LAYERS)
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Styled.PieceWorkModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={PieceWorkModalSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          useEffect(() => {
            setIsHourly(values?.allHourlyPayCheckbox)
          }, [values?.allHourlyPayCheckbox])

          useEffect(() => {
            if (pickPoData?.length !== 0) {
              if (values.project !== '' || initialValues.project !== '') {
                const Id = getValueByKey(values.project !== '' ? values.project : initialValues.project)
                fetchPitchLayer(Id)
              }
            }
          }, [values.project, initialValues.project, pickPoData])

          useEffect(() => {
            if (taskIdData && values?.task && Object.values(taskIdData)?.length) {
              values.pieceWork = pieceworkByTaskId?.length ? [{ name: '', value: '' }] : []
              Object.values(taskIdData)?.includes(values?.task) &&
                (async () => {
                  try {
                    const res = await getPieceWorkByTaskId({
                      taskId: values?.task,
                      date: startOfDate(new Date()),
                      memberId: currentMember._id,
                    })

                    const customPiecework = res?.data?.data?.pieceWorkSettings?.map((item: any) => ({
                      name: item?.name,
                      unit: item?.unit?.split('(')?.[0]?.trim(),
                      usesPitch: item?.usesPitch,
                      pitch: item?.pitch?.map((p: any) => ({ amount: p?.amount, pitch: p?.pitchOrder })),
                      _id: item?._id,
                      hasLayer: item?.hasLayer,
                      isExtra: item?.isExtra,
                      description: item?.description,
                    }))
                    setPieceworkByTaskId(customPiecework)
                  } catch (error) {
                    console.error('error=====>', error)
                  } finally {
                    setPieceworkLoading(false)
                  }
                })()
            }
          }, [taskIdData, values?.task, pieceworkByTaskId?.length])

          const dropdownValues = pieceworkByTaskId?.map((item: any) => ({ ...item, value: '' }))
          dropdownValues?.push({ name: 'Extra hours' })

          const selectedPws = values?.pieceWork?.map((v) => v.name)

          const selectedPo = pickPoData?.find((v: any) => v?._id === values?.project)

          setNewProject(values.project)

          return (
            <TimecardCont>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Piece Work</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setShowPieceWorkModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer className="top-padding">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <SwitchFromCont>
                      {isSwitch ? (
                        <SharedStyled.Text
                          as={'h1'}
                          style={{ fontFamily: Nue.bold, fontSize: '24px', paddingTop: '16px' }}
                        >
                          SWITCH FROM
                        </SharedStyled.Text>
                      ) : null}
                      {pieceworkLoading ? (
                        <SharedStyled.FlexCol>
                          <SLoader height={52} />
                          <SLoader height={52} />
                        </SharedStyled.FlexCol>
                      ) : (
                        <>
                          <SharedStyled.FlexCol alignItems="center" gap="16px">
                            <SharedStyled.FlexRow className="info">
                              <SharedStyled.FlexCol alignItems="center">
                                <SharedStyled.Text>
                                  {selectedPo?.num ? `${selectedPo?.PO}-${selectedPo?.num}` : selectedPo?.PO ?? ''}
                                </SharedStyled.Text>
                                <p>PO #</p>
                              </SharedStyled.FlexCol>
                              <SharedStyled.FlexCol alignItems="center">
                                <SharedStyled.Text>{IdTaskData[values.task]}</SharedStyled.Text>
                                <p>Task</p>
                              </SharedStyled.FlexCol>
                            </SharedStyled.FlexRow>
                          </SharedStyled.FlexCol>
                          <InputWithValidation labelName="Clock Out Time" stateName="clockOutTime" type="time" />
                          {/* <CustomSelect
                          value={selectedPo?.num ? `${selectedPo?.PO}-${selectedPo?.num}` : selectedPo?.PO ?? ''}
                          labelName="PO"
                          stateName="project"
                          setValue={(val: string) => {
                            const value = pickPoData?.find(
                              (item: any) => item?.PO === val?.split('-')?.[0]?.trim()
                            )?._id

                            setFieldValue('project', value)
                          }}
                          margin="10px 0 0 0"
                          dropDownData={pickPoData.map((item: any) => (item.num ? `${item.PO}-${item.num}` : item.PO))}
                          setFieldValue={() => {}}
                          showInitialValue
                          error={touched.project && errors.project ? true : false}
                        />
                        <CustomSelect
                          value={IdTaskData[values.task]}
                          labelName="Task"
                          stateName="task"
                          setValue={(val: string) => {
                            setFieldValue('task', taskIdData[val])
                          }}
                          margin="10px 0 0 0"
                          dropDownData={taskData}
                          setFieldValue={() => {}}
                          error={touched.task && errors.task ? true : false}
                        /> */}

                          {/* {values.task !== 'Repairs' && ( */}

                          {/* )} */}
                        </>
                      )}

                      {/* ============= Piecework ============= */}
                      {isPieceworkPosition ? (
                        <>
                          {!pieceworkLoading &&
                            allTaskData?.find((itm: any) => itm?._id === values?.task)?.pieceWork && (
                              <SharedStyled.FlexBox
                                width="100%"
                                alignItems="center"
                                gap="5px"
                                marginTop="6px"
                                justifyContent="flex-start"
                              >
                                <Styled.CheckBox
                                  width="15px"
                                  height="20px"
                                  type="checkbox"
                                  name="allHourlyPayCheckbox"
                                />
                                <Styled.CheckBoxDescription>No Piece Work - all hourly pay</Styled.CheckBoxDescription>
                              </SharedStyled.FlexBox>
                            )}

                          {pieceworkLoading ? <SLoader height={100} /> : null}

                          {!pieceworkLoading && pieceworkByTaskId?.length && values.allHourlyPayCheckbox === false ? (
                            <div className="piecework">
                              <Styled.WorkDoneContainer name="pieceWork">
                                {(fieldArrayProps: any) => {
                                  const { push, remove } = fieldArrayProps
                                  return (
                                    <>
                                      {values.pieceWork?.map((work: any, index: number) => (
                                        <Fragment key={index}>
                                          <SharedStyled.FlexRow alignItems="flex-start">
                                            <CustomSelect
                                              value={
                                                work.name
                                                  ? work?.name
                                                  : dropdownValues?.filter((item) => {
                                                      return !selectedPws?.includes(item?.name)
                                                    })?.length
                                                  ? dropdownValues
                                                      ?.filter((item) => {
                                                        return !selectedPws?.includes(item?.name)
                                                      })
                                                      ?.map((t) => t.name)[0]
                                                  : ''
                                              }
                                              isPieceWork
                                              labelName="Piecework"
                                              setValue={() => {}}
                                              margin="10px 0 0 0"
                                              showInitialValue
                                              toolTipText={
                                                dropdownValues?.find((item) => item?.name === work?.name)?.description
                                              }
                                              maxWidth="160px"
                                              stateName={`pieceWork.${index}.name`}
                                              dropDownData={dropdownValues
                                                ?.filter(
                                                  (item) =>
                                                    !selectedPws?.includes(item?.name) ||
                                                    item?.usesPitch ||
                                                    item?.hasLayer
                                                )
                                                ?.map((t) => t.name)}
                                              setFieldValue={setFieldValue}
                                              error={errors?.pieceWork?.[index]?.name ? true : false}
                                            />
                                            <SharedStyled.FlexRow>
                                              <SharedStyled.FlexRow alignItems="flex-start">
                                                {work?.name !== 'Extra hours' ? (
                                                  <InputWithValidation
                                                    labelName={
                                                      dropdownValues?.find(
                                                        (itm) => itm?.name === values?.pieceWork?.[index].name
                                                      )?.unit ?? 'Select piecework'
                                                    }
                                                    minWidth="80px"
                                                    forceType="number"
                                                    stateName={`pieceWork.${index}.value`}
                                                    error={errors?.pieceWork?.[index]?.value ? true : false}
                                                    onWheel={handleWheel}
                                                  />
                                                ) : (
                                                  <Styled.NameValueUnitContainer
                                                    justifyContent="flex-start"
                                                    width="100%"
                                                    marginTop="8px"
                                                    flexWrap="wrap"
                                                    gap="0px"
                                                    style={{ justifyContent: 'flex-start' }}
                                                    className="extra-container"
                                                  >
                                                    {/* <Styled.NameDiv style={{ marginRight: '16px' }}>
                                                  Extra Time:
                                                </Styled.NameDiv> */}
                                                    <SharedStyled.FlexBox alignItems="center">
                                                      <Styled.ValueInput type="number" name={`extraTime.hours`} />
                                                      <Styled.UnitDiv>HR</Styled.UnitDiv>
                                                    </SharedStyled.FlexBox>
                                                    <SharedStyled.FlexBox alignItems="center">
                                                      <Styled.ValueInput
                                                        type="number"
                                                        marginLeft="8px"
                                                        name={`extraTime.minutes`}
                                                      />
                                                      <Styled.UnitDiv>MIN</Styled.UnitDiv>
                                                    </SharedStyled.FlexBox>
                                                  </Styled.NameValueUnitContainer>
                                                )}
                                                {dropdownValues?.find(
                                                  (itm) => itm?.name === values?.pieceWork?.[index].name
                                                )?.usesPitch && pitch?.length ? (
                                                  <CustomSelect
                                                    value={
                                                      !isNaN(work?.pitch)
                                                        ? `${work.pitch}/12`
                                                        : work.pitch
                                                        ? work?.pitch
                                                        : pitch?.map((item: any) => `${item}/12`)[0]
                                                    }
                                                    labelName="Pitch"
                                                    setValue={() => {}}
                                                    margin="10px 0 0 0"
                                                    showInitialValue
                                                    isPieceWork
                                                    isPitch
                                                    stateName={`pieceWork.${index}.pitch`}
                                                    dropDownData={pitch.map((item: any) => `${item}/12`)}
                                                    setFieldValue={setFieldValue}
                                                    error={errors?.pieceWork?.[index]?.pitch ? true : false}
                                                  />
                                                ) : null}

                                                {
                                                  // values.task === 'Tear Off' &&
                                                  dropdownValues?.find(
                                                    (itm) => itm?.name === values?.pieceWork?.[index].name
                                                  )?.hasLayer && layer?.length ? (
                                                    <CustomSelect
                                                      value={work.layers ?? layer[0]}
                                                      labelName="Layers"
                                                      setValue={() => {}}
                                                      margin="10px 0 0 0"
                                                      showInitialValue
                                                      isPieceWork
                                                      stateName={`pieceWork.${index}.layers`}
                                                      dropDownData={layer}
                                                      setFieldValue={setFieldValue}
                                                      error={errors?.pieceWork?.[index]?.layers ? true : false}
                                                    />
                                                  ) : null
                                                }
                                              </SharedStyled.FlexRow>
                                              <SharedStyled.FlexRow margin="10px 0 0 0" width="max-content">
                                                <Button
                                                  type="button"
                                                  width="max-content"
                                                  className="delete"
                                                  padding="7px 14px"
                                                  onClick={() => remove(index)}
                                                  // style={{ visibility: index > 0 ? 'visible' : 'hidden' }}
                                                >
                                                  -
                                                </Button>
                                              </SharedStyled.FlexRow>
                                            </SharedStyled.FlexRow>
                                          </SharedStyled.FlexRow>

                                          {isMobile ? (
                                            <SharedStyled.HorizontalDivider
                                              margin="14px 0 0 0"
                                              bg="rgba(0, 0, 0, 0.6)"
                                            />
                                          ) : null}
                                        </Fragment>
                                      ))}
                                      {values?.pieceWork?.length! + 1 <= dropdownValues?.length ? (
                                        <SharedStyled.FlexRow>
                                          <Button
                                            type="button"
                                            className="success"
                                            onClick={() => push({ name: '', value: '' })}
                                          >
                                            + Add Work
                                          </Button>
                                        </SharedStyled.FlexRow>
                                      ) : null}
                                    </>
                                  )
                                }}
                              </Styled.WorkDoneContainer>
                            </div>
                          ) : null}
                        </>
                      ) : null}

                      {/* ============= Piecework ============= */}

                      {pieceworkLoading ? (
                        <SharedStyled.FlexCol>
                          <SLoader height={52} />
                          <SLoader height={52} />
                        </SharedStyled.FlexCol>
                      ) : (
                        <>
                          <SharedStyled.FlexCol gap="-10px" padding="0 0 16px 0">
                            <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                              Notes
                            </Styled.LabelDiv>
                            <Styled.TextArea
                              component="textarea"
                              as={Field}
                              name="notes"
                              marginTop="8px"
                              height="100px"
                            />
                          </SharedStyled.FlexCol>
                          {/* <SharedStyled.FlexRow width="max-content" justifyContent="flex-start" margin="0 auto 0 0">
                          <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                            Clock Out Time:
                          </Styled.LabelDiv>
                          <span>{clockedOutTime}</span>
                        </SharedStyled.FlexRow> */}
                        </>
                      )}
                    </SwitchFromCont>

                    {isSwitch ? (
                      <SwitchToCont
                        onTouchMove={(e) => {
                          e.stopPropagation()
                        }}
                      >
                        <SharedStyled.Text
                          as={'h1'}
                          style={{ fontFamily: Nue.bold, fontSize: '24px', paddingTop: '16px' }}
                        >
                          SWITCH TO
                        </SharedStyled.Text>

                        <AutoComplete
                          value={values?.switchedProject}
                          labelName="Switch to PO"
                          stateName="switchedProject"
                          options={pickPoData.map((item: any) => (item.num ? `${item.PO}-${item.num}` : item.PO))}
                          setValueOnClick={(val: string) => {
                            const value = pickPoData?.find(
                              (item: any) => item?.PO === val?.split('-')?.[0]?.trim()
                            )?._id
                            setSelectedPoName(value)
                            setFieldValue('switchedProject', val)
                          }}
                          className="reverse"
                          dropdownHeight="220px"
                          borderRadius="0"
                          validate
                          setFieldValue={setFieldValue}
                          selectedValue={selectedPoName}
                          error={touched.switchedProject && errors.switchedProject ? true : false}
                        />

                        <AutoComplete
                          value={values?.switchedTask}
                          labelName="Switch to Task"
                          stateName="switchedTask"
                          options={taskData!}
                          dropdownHeight="160px"
                          borderRadius="0"
                          validate
                          className="reverse"
                          setFieldValue={setFieldValue}
                          selectedValue={selectedTask}
                          setValueOnClick={(val: string) => {
                            setSelectedTask(val)
                          }}
                          error={touched.switchedTask && errors.switchedTask ? true : false}
                        />
                      </SwitchToCont>
                    ) : null}
                    <SharedStyled.ButtonContainer padding="20px 0 0 0">
                      <Button type="submit" isLoading={loading} disabled={pieceworkLoading}>
                        {actionName}
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </TimecardCont>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Styled.PieceWorkModalContainer>
  )
}
