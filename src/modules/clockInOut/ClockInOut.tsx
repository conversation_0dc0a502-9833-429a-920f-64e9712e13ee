import { Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

import {
  createTimeCardToClockIn,
  getMemberRecentTimeCards,
  updateTimeCardToClockOut,
} from '../../logic/apis/clockInOut'
import { getMemberPosition } from '../../logic/apis/position'
import { getOppClockInMobile } from '../../logic/apis/projects'
import { getMemberTasks } from '../../logic/apis/task'
import Button from '../../shared/components/button/Button'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify, simplifyBackendError } from '../../shared/helpers/util'
import useWindowDimensions from '../../shared/hooks/useWindowDimensions'
import { NormalDropdown } from '../../shared/normalDropdown/NormalDropdown'
import * as SharedStyled from '../../styles/styled'
import { ButtonCont } from '../units/style'
import ClockInModal from './components/clockINModal/ClockInModal'
import { ClockOutModal } from './components/clockOutModal/ClockOutModal'
import { PieceWorkModal } from './components/pieceWorkModal/PieceWorkModal'
import { SwitchModal } from './components/switchModal/SwitchModal'
import * as Styled from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import DashboardTimecard from '../dashboard/components/DashboardTimecard'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import ErrorModal from './components/errorModal/ErrorModal'
import { StorageKey } from '../../shared/helpers/constants'
import { updateActivityTimeCard } from '../../logic/apis/approveTimeCard'

interface I_SwitchTimeDetails {
  switchProject: string
  switchTask: string
  switchTime: string
  switchTimeDate: Date
}
let intervalId: any

const initialValue = {
  manager: '',
  task: '',
}

const ClockInOut = () => {
  const [project, setProject] = useState<string>('')
  const [projectName, setProjectName] = useState('')
  const [task, setTask] = useState<string>('')
  const [showSwitchModal, setShowSwitchModal] = useState(false)
  const [showClockOutModal, setShowClockOutModal] = useState(false)
  const [showPieceWorkModal, setShowPieceWorkModal] = useState<boolean>(false)
  const [actionName, setActionName] = useState<string>('')
  const [isClockedIn, setIsClockedIn] = useState<boolean>(false)
  const [clockedInTime, setClockedInTime] = useState<string>('')
  const [clockedInTimeDate, setClockedInTimeDate] = useState<Date>(new Date())
  const [clockedOutTime, setClockedOutTime] = useState<string>('')
  const [clockedOutTimeDate, setClockedOutTimeDate] = useState<any>()
  const [switchTimeDetails, setSwitchTimeDetails] = useState<I_SwitchTimeDetails>({
    switchProject: '',
    switchTask: '',
    switchTime: '',
    switchTimeDate: new Date(),
  })

  const [initialValues, setInitialValues] = useState(initialValue)
  const [clockedInHours, setClockedInHours] = useState<any>('0')
  const [clockedInMinutes, setClockedInMinutes] = useState<any>('0')
  const [clockInLoading, setClockInLoading] = useState<boolean>(false)
  const [recentTimeCardId, setRecentTimeCardId] = useState<string>('')
  const [recentTimeCardDetails, setRecentTimeCardDetails] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [taskData, setTaskData] = useState<any>([])
  const [clockInData, setClockInData] = useState<any>([])
  const [taskIdData, setTaskIdData] = useState<any>([])
  const [IdTaskData, setIdTaskData] = useState<any>([])
  const [pickPOIdForSwitch, setPickPoIdForSwitch] = useState<string>('')
  const [projectForSwitch, setProjectForSwitch] = useState<string>('')
  const [taskForSwitch, setTaskForSwitch] = useState<string>('')
  const [showClockInModal, setShowClockInModal] = useState(false)
  const [newProject, setNewProject] = useState('')
  const [bool, setBool] = useState(false)
  const formRef = useRef<any>()
  const [showErrorModal, setShowErrorModal] = useState(false)
  const [isCrewLead, setIsCrewLead] = useState(false)

  const { width } = useWindowDimensions()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, positionDetails } = globalSelector.company
  const isPositionNotAvailable = globalSelector.ui?.isPositionNotAvailable

  useEffect(() => {
    if (clockedOutTime) {
      let currentDate = new Date()
      const [formattedHours, minutes] = clockedOutTime?.split(':')
      currentDate?.setHours(Number(formattedHours))
      currentDate?.setMinutes(Number(minutes))
      let time = `${formattedHours}:${minutes}`
      setClockedOutTime(time)
      setClockedOutTimeDate(currentDate)
    }
  }, [clockedOutTime])

  const onClockIn = async (switchedTimeDate?: Date, switchPayload?: any) => {
    try {
      let switchedTime = switchedTimeDate ? new Date(switchedTimeDate) : undefined

      if (project === '') {
        notify('Please pick the PO', 'error')
      } else if (task === '') {
        notify('Please pick the Task', 'error')
      } else if (Object.keys(currentMember).length > 0) {
        setClockInLoading(true)
        let currentDate
        if (switchedTime) {
          clearInterval(intervalId)
          currentDate = switchedTime
        } else {
          currentDate = new Date()
        }
        let hours = currentDate.getHours()
        let minutes: any = currentDate.getMinutes()
        if (minutes.toString().length === 1) {
          minutes = '0' + minutes
        }
        let time = `${hours}:${minutes}`
        currentDate.setHours(hours)
        currentDate.setMinutes(minutes)

        let dataObj = {
          memberId: currentMember._id,
          projectId: switchPayload?.project
            ? switchPayload?.project
            : clockInData?.find((item: any) => item?._id === projectForSwitch)?._id, //id po
          projectPO: switchPayload?.project
            ? clockInData?.find((item: any) => item?._id === switchPayload?.project)?.PO
            : clockInData?.find((item: any) => item?._id === projectForSwitch)?.PO, //name po
          task: switchPayload?.task
            ? taskIdData[switchPayload?.task]
            : taskForSwitch?.length === 36
            ? taskForSwitch
            : taskIdData[taskForSwitch],
          timeIn: new Date(currentDate?.setSeconds(0, 0))?.toISOString(),
          createdBy: currentMember._id,
        }
        let response = await createTimeCardToClockIn(dataObj)

        if (isSuccess(response)) {
          setClockedInTime(time)
          setClockedInTimeDate(currentDate)
          setIsClockedIn(true)
          setClockInLoading(false)
          notify('ClockedIn Successfully', 'success')
          // setShowClockInModal(false)
          setBool((prev) => !prev)
        } else {
          notify(simplifyBackendError(response?.data?.message), 'error')
          setClockInLoading(false)
        }
      }
    } catch (error: any) {
      console.error('onClockIn error', error)
      setClockInLoading(false)
    }
  }

  const onClockOut = () => {
    try {
      let currentDate = new Date()
      let hours = currentDate.getHours()
      let formattedHours = hours < 10 ? `0${hours}` : hours
      let minutes: any = currentDate.getMinutes()
      let minutesString = minutes.toString()
      if (minutes.toString().length === 1) {
        minutesString = `0${minutesString}`
        minutes = minutesString
      }
      let time = `${formattedHours}:${minutes}`
      setClockedOutTime(time)
      // setClockedOutTimeDate(currentDate)
      // setShowClockOutModal(true)
      setActionName('Clock Out')
      setShowPieceWorkModal(true)
    } catch (error: any) {
      console.error('onClockOut error', error)
    }
  }

  const onSwitch = () => {
    try {
      let currentDate = new Date()
      let hours = currentDate.getHours()
      let formattedHours = hours < 10 ? `0${hours}` : hours
      let minutes: any = currentDate.getMinutes()
      let minutesString = minutes.toString()
      if (minutes.toString().length === 1) {
        minutesString = `0${minutesString}`
        minutes = minutesString
      }
      let time = `${formattedHours}:${minutes}`
      currentDate.setHours(hours)
      currentDate.setMinutes(minutes)
      setClockedOutTime(time)
      setClockedOutTimeDate(currentDate)
      setActionName('Switch')
      // setShowSwitchModal(true)
      setShowPieceWorkModal(true)
    } catch (error) {
      console.error('onClockOut error', error)
    }
  }

  const onFinalSwitch = async (pieceWorkTask?: string, allHourly?: boolean, payloadData?: Record<string, any>) => {
    try {
      await onFinalClockOut(pieceWorkTask, allHourly, payloadData)
      clearInterval(intervalId)
      setProject(payloadData?.project)
      setTask(payloadData?.task!)
      // setTaskForSwitch(payloadData?.task!)
      // setProjectForSwitch(payloadData?.project!)

      onClockIn(payloadData?.clockOutDateTime, {
        project: payloadData?.project,
        task: payloadData?.task,
      })
      setClockedInHours('0')
      setClockedInMinutes('0')
      setLoading(false)
      setShowPieceWorkModal(false)
    } catch (error) {
      console.error('onFinalSwitch error', error)
      setLoading(false)
    }
  }

  const onFinalClockOut = async (pieceWorkTask?: string, allHourly?: boolean, payloadData?: Record<string, any>) => {
    try {
      let isoTimeIn = Number(clockedInTimeDate)
      let isoTimeOut = Number(new Date(payloadData?.clockOutDateTime))

      let hrsInMillisecs = isoTimeOut - isoTimeIn

      let hours = Math.round((hrsInMillisecs / (1000 * 60 * 60)) * 100) / 100

      let dataObj = {
        _id: recentTimeCardId,
        allHourly: allHourly,
        projectId: clockInData
          ?.filter((value: any) => value?._id === newProject)
          .map((value: any) => String(value?._id))
          .join(','),
        projectPO: clockInData?.find((item: any) => item?._id === newProject)?.PO,
        notes: payloadData?.notes,
        task: pieceWorkTask!,
        workTaskId: pieceWorkTask ? taskIdData[pieceWorkTask] : taskIdData[task],
        hrs: hours,
        timeOut: payloadData?.clockOutDateTime,
      }

      let response = await updateTimeCardToClockOut(dataObj)

      if (isSuccess(response)) {
        if (actionName === 'Switch') {
          await updateActivityTimeCard(recentTimeCardId, {
            status: payloadData?.switchText,
          })
        }
        setIsClockedIn(false)
        setProject('')
        setTask('')
        setClockedInTime('')
        setClockedInHours('0')
        setClockedInMinutes('0')
        setLoading(false)
        setShowPieceWorkModal(false)
        notify('ClockedOut Successfully', 'success')
        setNewProject('')
        setBool((prev) => !prev)
        setPickPoIdForSwitch('')
        setProjectForSwitch('')
        setTaskForSwitch('')

        formRef?.current()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('onFinalClockOut error', error)
      setLoading(false)
    }
  }

  const calculateTime = () => {
    try {
      let delay = 0
      intervalId = setInterval(() => {
        let currentTime = new Date()
        let activeTime = Number(currentTime) - Number(clockedInTimeDate)

        let hours = Math.floor(activeTime / (1000 * 60 * 60))

        let minutes = Math.floor((activeTime % (1000 * 60 * 60)) / (1000 * 60))

        let seconds = Math.floor((activeTime % (1000 * 60)) / 1000)

        delay = (61 - seconds) * 1000

        let hoursString = hours.toString()
        if (hours.toString().length === 1) {
          hoursString = '0' + hours
        }

        setClockedInHours(hoursString)
        let minutesString = minutes.toString()
        if (minutes.toString().length === 1) {
          minutesString = '0' + minutes
        }
        setClockedInMinutes(minutesString)
      }, 5000)
    } catch (error) {
      console.error('calculateTime error', error)
    }
  }

  const recentTimeCardsDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      let dataObj = {
        memberId: currentMember._id,
      }

      let response = await getMemberRecentTimeCards(dataObj)

      if (isSuccess(response)) {
        let id = response?.data?.data?.activeCard?._id
        let timeCard = response?.data?.data?.activeCard

        // if (timeCard?.active) {
        //   let activeDate = new Date(timeCard?.timeIn)
        //   let hours = activeDate.getHours()
        //   let minutes: any = activeDate.getMinutes()
        //   if (minutes.toString().length === 1) {
        //     minutes = '0' + minutes
        //   }
        //   let time = `${hours}:${minutes}`
        //   setClockedInTime(time)
        //   setClockedInTimeDate(activeDate)
        //   setIsClockedIn(true)
        // }
        setRecentTimeCardId(id)
        setRecentTimeCardDetails(timeCard)
      } else {
        notify(response?.data?.message, 'error')
        setClockInLoading(false)
      }
      // }
    } catch (error) {
      console.error('recentTimeCardsDetails error', error)
    }
  }

  const onRefresh = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      let dataObj = {
        memberId: currentMember._id,
      }

      let response = await getMemberRecentTimeCards(dataObj)
      if (isSuccess(response)) {
        let recentTimeCard = response?.data?.data?.activeCard
        if (recentTimeCard?.active) {
          let activeDate = new Date(recentTimeCard.timeIn)
          let hours = activeDate.getHours()
          let minutes: any = activeDate.getMinutes()
          if (minutes.toString().length === 1) {
            minutes = '0' + minutes
          }
          let time = `${hours}:${minutes}`
          setClockedInTime(time)
          setTask(recentTimeCard.task)
          setProject(recentTimeCard.projectId)
          setClockedInTimeDate(activeDate)

          setIsClockedIn(true)
        }
      } else {
        notify(response?.data?.message, 'error')
        setClockInLoading(false)
      }
      // }
    } catch (error) {
      console.error('onRefresh error', error)
    }
  }

  const getTasks = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      let taskResponse = positionDetails?._id && (await getMemberTasks({}, currentMember._id))

      if (isSuccess(taskResponse) && positionDetails?._id) {
        let workTasks = taskResponse?.data?.data?.workTask
        let taskObj: any = []
        let taskIdObj: any = {}
        let idTaskObj: any = {}

        workTasks.forEach((workTask: any) => {
          if (workTask?.position?.includes(positionDetails?._id)) {
            taskObj.push(workTask)
            taskIdObj = { ...taskIdObj, [`${workTask.name}`]: workTask._id }
            idTaskObj = { ...idTaskObj, [`${workTask._id}`]: workTask.name }
          }
        })
        setTaskData(taskObj)
        setTaskIdData(taskIdObj)
        setIdTaskData(idTaskObj)
      } else {
        notify(taskResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      // setShowErrorModal(true)
      console.error('getTasks error', error)
    }
  }

  const getClockIn = async () => {
    try {
      let clockObj: any = []

      const result = await getOppClockInMobile()
      if (result?.data?.data?.readyOpps?.length) {
        result?.data?.data?.readyOpps.forEach((clockIn: any) => {
          clockObj.push(clockIn)
        })
        setClockInData(clockObj)
      }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  useEffect(() => {
    if (isClockedIn && clockedInTime !== '') {
      calculateTime()
    } else {
      clearInterval(intervalId)
      setClockedInHours('0')
      setClockedInMinutes('0')
      return () => {}
    }
  }, [isClockedIn])

  useEffect(() => {
    if (currentMember._id) {
      recentTimeCardsDetails()
    }
  }, [isClockedIn, currentMember])
  useEffect(() => {
    // recentTimeCardsDetails()

    if (recentTimeCardDetails && Object?.values(recentTimeCardDetails)?.length && recentTimeCardDetails?.active) {
      let activeDate = new Date(recentTimeCardDetails?.timeIn)

      let hours = activeDate.getHours()
      let minutes: any = activeDate.getMinutes()
      if (minutes.toString().length === 1) {
        minutes = '0' + minutes
      }
      let time = `${hours}:${minutes}`
      setClockedInTime(time)
      setClockedInTimeDate(activeDate)
      setIsClockedIn(true)
      setRecentTimeCardId(recentTimeCardDetails?._id)
      setTask(recentTimeCardDetails?.task)
      setProject(recentTimeCardDetails?.projectId)
    }
  }, [recentTimeCardDetails])

  useEffect(() => {
    if (currentMember._id !== '') {
      getTasks()
    }
    // getClockIn()
  }, [currentMember, Object.values(IdTaskData)?.length, showPieceWorkModal, positionDetails])

  useEffect(() => {
    getClockIn()
  }, [])

  useEffect(() => {
    if (project) setProjectForSwitch(project)
    if (project) {
      clockInData.filter((data: any) => data?._id === project).forEach((item: any) => setPickPoIdForSwitch(item._id))
    }
    if (task) setTaskForSwitch(task)
  }, [project, task])

  useEffect(() => {
    if (isPositionNotAvailable) {
      setShowErrorModal(true)
    }
  }, [isPositionNotAvailable])

  const selectedPo = clockInData?.find((v: any) => v?._id === project)

  return (
    <>
      {isPositionNotAvailable ? null : (
        <>
          <Styled.ClockInCont gap="24px">
            <Formik initialValues={initialValues} onSubmit={() => {}}>
              {({ values, setFieldValue, resetForm }) => {
                formRef.current = resetForm
                useEffect(() => {
                  if (project && !values?.manager) {
                    setFieldValue(
                      'manager',
                      selectedPo?.num ? `${selectedPo?.PO}-${selectedPo?.num}` : selectedPo?.PO ?? ''
                    )
                  }

                  if (task && !values?.task) setFieldValue('task', task?.length === 36 ? IdTaskData[task] : task)
                }, [task, project, Object.values(IdTaskData)?.length])
                return (
                  <>
                    <SharedStyled.FlexRow
                      justifyContent="space-between"
                      alignItems="center"
                      flexWrap={width < 480 ? 'wrap' : 'nowrap'}
                      as={'form'}
                    >
                      <SharedStyled.FlexCol gap="10px" width="100%">
                        {/* <CustomSelect
                      value={selectedPo?.num ? `${selectedPo?.PO}-${selectedPo?.num}` : selectedPo?.PO ?? ''}
                      labelName="PO#"
                      stateName="manager"
                      dropDownData={clockInData.map((data: any) => (data.num ? `${data.PO}-${data.num}` : data.PO))}
                      setValue={(val: string) => {
                        const value = clockInData?.find((item: any) => item?.PO === val?.split('-')?.[0]?.trim())?._id
                        setProject(value)
                      }}
                      className="top"
                      showInitialValue
                    /> */}

                        <AutoComplete
                          value={
                            values?.manager
                              ? values?.manager
                              : selectedPo?.num
                              ? `${selectedPo?.PO}-${selectedPo?.num}`
                              : selectedPo?.PO ?? ''
                          }
                          labelName="PO#"
                          stateName="manager"
                          options={clockInData.map((data: any) => (data.num ? `${data.PO}-${data.num}` : data.PO))}
                          setValueOnClick={(val: string) => {
                            setProjectName(val)
                            const value = clockInData?.find(
                              (item: any) => item?.PO === val?.split('-')?.[0]?.trim()
                            )?._id
                            setProject(value)
                          }}
                          disabled={isClockedIn}
                          dropdownHeight="300px"
                          borderRadius="0px"
                          selectedValue={projectName}
                          setFieldValue={setFieldValue}
                          validate
                        />

                        <AutoComplete
                          value={values?.task}
                          labelName="Task"
                          stateName="task"
                          options={taskData.map((d: any) => d?.name)}
                          setValueOnClick={(val: string) => {
                            setTask(val)
                          }}
                          dropdownHeight="300px"
                          borderRadius="0px"
                          selectedValue={task}
                          validate
                          disabled={isClockedIn}
                          setFieldValue={setFieldValue}
                        />

                        {/* <CustomSelect
                      value={task?.length === 36 ? IdTaskData[task] : task ?? ''}
                      labelName="Task"
                      stateName="task"
                      dropDownData={taskData?.map((d: any) => d?.name)}
                      setValue={setTask}
                      className="top"
                      showInitialValue
                    /> */}
                      </SharedStyled.FlexCol>
                    </SharedStyled.FlexRow>

                    <SharedStyled.FlexCol width="100%">
                      <ButtonCont className="clock large">
                        {isClockedIn && (
                          <>
                            <Button onClick={() => onSwitch()}>Switch</Button>
                            <Button className="delete" onClick={() => onClockOut()}>
                              Clock Out!
                            </Button>
                          </>
                        )}

                        {!isClockedIn && (
                          <Button
                            onClick={() => {
                              onClockIn()
                              // setShowClockInModal(true)
                            }}
                            isLoading={clockInLoading}
                            disabled={!project || !task}
                          >
                            Clock In
                          </Button>
                        )}
                      </ButtonCont>

                      {recentTimeCardDetails?._id ? (
                        <Styled.InfoCont className="mob-passed-time desktop">
                          <SharedStyled.FlexCol alignItems="center" gap="10px">
                            <p>You've been clocked in for:</p>
                            <Styled.ClockInTime>
                              {clockedInHours} H
                              <Styled.LoaderContainer isClockedIn={isClockedIn}>
                                <Styled.LoaderContent className="loading"></Styled.LoaderContent>
                              </Styled.LoaderContainer>
                              {clockedInMinutes} M
                            </Styled.ClockInTime>
                          </SharedStyled.FlexCol>
                        </Styled.InfoCont>
                      ) : null}
                    </SharedStyled.FlexCol>
                  </>
                )
              }}
            </Formik>

            <SharedStyled.HorizontalDivider height="2px" />

            <DashboardTimecard
              bool={bool}
              setIsCrewLead={setIsCrewLead}
              updateClockOut={() => {
                window.location.reload()
              }}
            />
          </Styled.ClockInCont>
        </>
      )}

      <CustomModal show={showSwitchModal} className="overflow">
        {showSwitchModal && (
          <SwitchModal
            setShowSwitchModal={setShowSwitchModal}
            setShowPieceWorkModal={setShowPieceWorkModal}
            setActionName={setActionName}
            projectInitial={project}
            taskInitial={task}
            taskData={taskData.map((d: any) => d?.name)}
            IdTaskData={IdTaskData}
            clockedOutTime={clockedOutTime}
            switchTimeDetails={switchTimeDetails}
            setSwitchTimeDetails={setSwitchTimeDetails}
            setClockedOutTime={setClockedOutTime}
            clockedInTimeDate={clockedInTimeDate}
            clockInData={clockInData}
            setPickPoIdForSwitch={setPickPoIdForSwitch}
            setProjectForSwitch={setProjectForSwitch}
            setTaskForSwitch={setTaskForSwitch}
          />
        )}
      </CustomModal>
      <CustomModal show={showClockOutModal}>
        <ClockOutModal
          setShowClockOutModal={setShowClockOutModal}
          setShowPieceWorkModal={setShowPieceWorkModal}
          setActionName={setActionName}
          clockedOutTime={clockedOutTime}
          clockedInTimeDate={clockedInTimeDate}
          clockedOutTimeDate={clockedOutTimeDate}
          setClockedOutTime={setClockedOutTime}
        />
      </CustomModal>
      <CustomModal show={showPieceWorkModal} className="top">
        <PieceWorkModal
          setIsClockedIn={setIsClockedIn}
          setShowPieceWorkModal={setShowPieceWorkModal}
          actionName={actionName}
          projectInitial={project}
          taskInitial={task}
          clockedOutTime={clockedOutTime}
          clockedInTimeDate={clockedInTimeDate}
          // clockedOutTimeDate={clockedOutTimeDate}
          setClockedOutTimeDate={setClockedOutTimeDate}
          recentTimeCardId={recentTimeCardId}
          onFinalSwitch={onFinalSwitch}
          onFinalClockOut={onFinalClockOut}
          loading={loading}
          setLoading={setLoading}
          setTask={setTask}
          setProject={setProject}
          setNewProject={setNewProject}
          isCrewLead={isCrewLead}
        />
      </CustomModal>

      <CustomModal show={showErrorModal}>
        <ErrorModal />
      </CustomModal>
      <CustomModal show={showClockInModal} toggleModal={() => setShowClockInModal(false)} className="overflow">
        <ClockInModal
          onClose={() => {
            setShowClockInModal(false)
          }}
        >
          <Styled.UpperPartContainer>
            <SharedStyled.FlexCol gap="24px" margin="0 0 34px 0">
              <NormalDropdown
                value={project}
                labelName="Pick PO#"
                stateName="manager"
                dropDownData={clockInData}
                setValue={setProject}
                pickPo={true}
              />
              <NormalDropdown
                value={task}
                labelName="Pick a Task"
                stateName="task"
                dropDownData={taskData}
                setValue={setTask}
              />
            </SharedStyled.FlexCol>

            {!isClockedIn && (
              <Button width="max-content" onClick={() => onClockIn()} isLoading={clockInLoading}>
                Clock In
              </Button>
            )}
          </Styled.UpperPartContainer>
        </ClockInModal>
      </CustomModal>
    </>
  )
}

export default ClockInOut
