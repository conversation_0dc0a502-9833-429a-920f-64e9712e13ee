import styled from 'styled-components'
import { Field } from 'formik'
import { colors, screenSizes } from '../../styles/theme'
import { FlexRow, SectionTitle, Text } from '../../styles/styled'
import { Nue } from '../../shared/helpers/constants'

interface I_Text {
  display?: string
  margin?: string
  padding?: string
  width?: any
  border?: boolean
  textAlign?: string
  alignItems?: string
  isDraft?: boolean
}

export const PrintContainer = styled.section`
  .margin-text {
    margin: 10px 0 0 0;
  }

  .material-input {
    width: 100%;
    span {
      display: inline-block;
      width: 100%;
    }
    input {
      border: none;
      outline: 'none';
    }
    input:focus {
      border: none;
      outline: none;
    }
    /* For Chrome, Safari, Edge, and other WebKit browsers */
    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* For Firefox */
    input[type='number'] {
      -moz-appearance: textfield;
    }
  }

  @media print {
    /* margin: 10mm 0; 
    border: 1px solid black;
    padding: 20px; */
    width: 100%;
    button {
      display: none;
    }
    p {
      width: 100% !important;
      white-space: normal !important;
      word-wrap: break-word !important;
      overflow-wrap: break-word !important;
      word-break: break-word !important;
      max-width: 100% !important;
    }
    hr {
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    .print-hide {
      display: none;
    }
  }

  .arrow {
    cursor: pointer;
  }

  .noPrint {
    @media print {
      display: none;
    }
  }

  /* @media print {
    .print-hide {
      display: none;
    }
    
  } */

  ${SectionTitle} {
    font-family: ${Nue.regular};
    font-weight: 500;
    font-size: 28px;
    white-space: nowrap;
  }

  ${Text} {
    font-family: ${Nue.regular};
    /* Allow wrapping by default; individual components can override as needed */
    white-space: normal;
    overflow-wrap: anywhere;
    word-break: break-word;
  }

  /* Ensure Fine Print section matches app font */
  .fine-print {
    font-family: ${Nue.regular};
  }

  .po {
    font-size: 20px;
    font-family: ${Nue.medium};
  }

  .address {
    text-transform: capitalize;
  }

  .grid {
    display: grid;
    grid-template-columns: 200px 186px;
  }

  .medium {
    font-family: ${Nue.medium};
    font-weight: 500;
  }

  .capitalize {
    text-transform: capitalize;
  }
  p {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    word-break: break-word !important;
  }
`
export const PrintNotesContainer = styled.section`
  width: 100%;

  /* Ensure long words/URLs wrap in UI */
  /* Reduce tab width to avoid big visual gaps from pasted content */
  tab-size: 2;
  -moz-tab-size: 2;
  p,
  li,
  span {
    white-space: pre-line !important; /* preserve newlines, collapse extra spaces */
    overflow-wrap: anywhere !important; /* modern */
    word-wrap: break-word !important; /* legacy */
    word-break: break-word !important;
    max-width: 100%;
    text-indent: 0;
    margin-left: 0;
  }

  ul,
  ol {
    padding-left: 16px; /* predictable list indentation */
    list-style-position: outside;
  }

  /* Keep code/pre blocks from overflowing */
  pre,
  code {
    white-space: pre-wrap !important;
    overflow-wrap: anywhere !important;
    word-break: break-word !important;
    font-family: ${Nue.regular} !important;
  }

  @media print {
    width: 100%;
    button {
      display: none;
    }
    p,
    li,
    span {
      white-space: normal !important;
      overflow-wrap: anywhere !important;
      word-wrap: break-word !important;
      word-break: break-word !important;
      max-width: 100% !important;
    }
    hr {
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    .print-hide {
      display: none;
    }
  }
`
export const BoxGap = styled.div<I_Text>`
  display: ${(props) => props.display ?? 'block'};
  margin: ${(props) => props.margin ?? '0 5px 0 0'};
  padding: ${(props) => props.padding ?? '0'};
  width: ${(props) => props.width ?? 'auto'};
  border-top: ${(props) => (props.border ? '1px solid #DEE2E6' : 'none')};
  text-align: ${(props) => props.textAlign};
  align-items: ${(props) => props.alignItems};
  @media (max-width: ${screenSizes.M}px) {
    width: 100%;
  }

  ${(props) =>
    props.isDraft &&
    `
  .delete{
  display: none;
  }
    opacity: 1 !important;
  span{
  color: ${colors.lightBlue};
  }
  `};

  &.btn {
    gap: 30px;
    margin-bottom: 6px;
    span {
      font-family: ${Nue.regular};
    }
  }

  @media print {
    .three-table-div1 {
      width: 80%;
    }
    .three-table-div2 {
      width: 10%;
    }
    .three-table-div3 {
      width: 10%;
    }
  }

  &.table {
    hr {
      margin: 4px 0;
      @media print {
        /* margin: 10mm 0; 
    border: 1px solid black;
    padding: 20px; */
        .print-hide {
          display: none;
        }
      }
    }
  }

  @media print {
    /* margin: 10mm 0; 
    border: 1px solid black;
    padding: 20px; */
    .print-hide {
      display: none;
    }
  }
`

export const EditButton = styled.button`
  margin-left: 10px;
  cursor: pointer;

  display: inline-block;
  padding: 0.25em 0.8em;
  font-size: 75%;
  font-family: ${Nue.medium};
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  background-color: #6c757d;
  color: ${colors.white};
`
export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '30%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  margin: ${(props) => props.margin};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.darkGrey};
  border-radius: 8px;
  padding: 12px 18px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.marginTop};
`
export const CustomStyledDrop = styled.div`
  .colorDropdown {
    select {
      height: 40px;
    }
  }
`

export const NewOrderWrapper = styled.section`
  margin: 10px 0 0 10px;
  b:first-child {
    display: inline-block;
  }
`

export const NewOrderContainer = styled.div`
  width: 500px;

  div {
    display: grid;
    grid-template-columns: 1.5fr 100px 1fr;
  }

  p:not(:first-child) {
    text-align: right;
  }

  .pointer {
    cursor: pointer;
  }
`

export const BreakdownCont = styled.div`
  width: max-content;
  display: grid;
  grid-template-columns: 200px 1fr;
  span:last-child {
    width: 100px;
    text-align: right;
  }
`

export const BreakdownWrap = styled.div`
  margin-top: 20px;

  ul {
    margin: 12px 0;
  }
`
export const BoxSpacing = styled.div<{ margin: string }>`
  margin: ${(props) => props.margin};

  &.material-table {
    #add-btn {
      &:focus {
        outline: 3px solid blue;
        box-shadow: 0 0 0 0;
      }
    }

    .material-input {
      cursor: pointer;

      input {
        &:focus {
          outline: 1px solid blue;
          box-shadow: 0 0 0 0;
        }
      }

      select {
        font-family: ${Nue.regular};
        font-size: 14px;
        text-align: center;

        &:focus-visible {
          border: 1px solid blue;
          box-shadow: 0 0 0 0;
        }
      }
    }

    .three-table-div1 {
      span {
        padding: 0px;
      }

      input {
        width: 100%;
        border-radius: 4px;
        height: 22px !important;
      }
    }

    .drag-row {
      pointer-events: none;
      opacity: 0.5;
    }

    .editing-row {
      padding: 8px 0;
      .three-table-div1 {
        span {
          padding: 0px;
        }

        input {
          height: 24px !important;
          border: none;
          width: 100%;
        }
      }

      .three-table-div3 {
        .material-input {
          margin-left: 6px;
        }
      }

      span {
        display: block;
        border: 1px solid rgba(106, 116, 126, 0.5);

        border-radius: 4px;
        padding: 4px;
      }

      .three-table-div2 {
        input {
          height: 24px;
        }
      }
    }

    .three-table-div2 {
      input {
        border: 1px solid rgba(106, 116, 126, 0.5);

        border-radius: 4px;
        max-width: 94px;
        text-align: right;
        height: 22px;
      }
    }
  }
`
export const HoverBox = styled.div`
  display: flex;
  align-items: center; /* Vertically align SVG and text */
  /* gap: 8px; Space between the SVG and text */

  .hover-svg {
    width: 10px; /* Set a fixed width for the SVG container */
    height: 24px; /* Set a fixed height if needed */
    display: flex; /* Ensures SVG stays centered */
    justify-content: center; /* Center SVG horizontally */
    align-items: center; /* Center SVG vertically */
    visibility: hidden; /* Make the SVG invisible by default */
    transform: rotate(90deg);
  }

  &:hover .hover-svg {
    visibility: visible; /* Make the SVG visible on hover */
  }
`
export const ActionIconContainer = styled.div`
  cursor: pointer;
  svg {
    width: 18px;
    height: 18px;
  }
  &.delete {
    &:focus {
      outline: 1px solid blue;
      border-radius: 4px;
      margin-bottom: 2px;
    }
    svg path {
      stroke: ${colors.errorRed};
    }
  }
  &.restore {
    &:focus {
      outline: 1px solid blue;
      border-radius: 4px;
      margin-bottom: 2px;
    }

    svg path {
      stroke: ${colors.blueLight};
    }
  }
`
