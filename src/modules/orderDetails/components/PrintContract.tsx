import React, { forwardRef, useEffect, useState } from 'react'
import * as Styled from '../styles'
import * as SharedStyled from '../../../styles/styled'
import NHRLogo from '../../../assets/images/nhr.png'
import {
  calculateDiscountPercentage,
  dayjsFormat,
  formatNumberToCommaS,
  formatPhoneNumber,
  roundToNearestTenth,
} from '../../../shared/helpers/util'
import { getSalesTax, roundTo2 } from '../../contract/constant'
import { content, replaceDynamicValues } from '../constant'
import { ContentData } from '../../contractsSetting/ContractsSetting'

interface PdfContentProps {
  order: any
  projectTypes: any
  opportunity: any
  companySettingForAll: any
  priceByProject: any
  contracts: any
  finePrintContent: ContentData
}
// Forwarding the ref to capture this component's content for PDF generation
const PrintContract = forwardRef<HTMLDivElement, PdfContentProps>(
  ({ order, projectTypes, opportunity, companySettingForAll, priceByProject, contracts, finePrintContent }, ref) => {
    let matchedProject = projectTypes.find((project) => project._id === order?.projects?.[0]?.type)
    const ceilToDecimalPlaces = (num: number, decimalPlaces: number) => {
      const factor = Math.pow(10, decimalPlaces)
      return Math.ceil(num * factor) / factor
    }
    function insertLineBreakAfterFirstComma(address: string) {
      const indexOfFirstComma = address.indexOf(',')

      // If no comma found, return the original string
      if (indexOfFirstComma === -1) {
        return address
      }

      // Split the string into two parts and insert a line break after the first comma
      const firstPart = address.substring(0, indexOfFirstComma).trim() // Including the comma
      const secondPart = address.substring(indexOfFirstComma + 1).trim() // Excluding the comma and trimming space

      return (
        <>
          {firstPart}
          <br />
          {secondPart}
        </>
      )
    }
    function getPitchValue(pitchKey: string): number | null {
      // Find the project type that matches the given typeId
      if (!matchedProject) {
        return 0 // Return null if the project type doesn't exist
      }

      // Get the value from the pitchMod using the pitchKey
      const pitchValue = matchedProject.pitchMod[pitchKey]

      // Check if the pitchKey exists
      if (pitchValue === undefined) {
        return 0
      }
      console.log({ pitchKey, projectTypes, pitchValue, matchedProject })

      return pitchValue
    }

    function sortTasks(tasks: any) {
      return tasks?.sort((a, b) => {
        const groupA = a.group ?? ''
        const groupB = b.group ?? ''
        const orderA = a.order ?? 0
        const orderB = b.order ?? 0

        if (groupA === '' && groupB !== '') {
          return 1
        }
        if (groupA !== '' && groupB === '') {
          return -1
        }
        if (groupA === groupB) {
          return orderA - orderB
        }
        return groupA.localeCompare(groupB)
      })
    }

    const renderColors = (colors: any, selectedProjectType: any) => {
      return (
        colors &&
        Object?.entries(colors)?.map(([key, value]: any, index: number) => {
          const [colorId, colorName] = key?.split('@')
          return (
            <div>
              {' '}
              <span style={{ textTransform: 'capitalize' }}>
                {selectedProjectType?.priceColor?.find((itm: { _id: string }) => itm?._id === colorId)?.name ??
                  colorName}
                :
              </span>
              &emsp;
              <span className="color">
                <b>{value}</b>
              </span>
            </div>
          )
        })
      )
    }

    return (
      <div ref={ref}>
        <div className="flex-box">
          <div>
            <img src={NHRLogo} alt="Logo" />
          </div>
          <div className="text-end" style={{ fontSize: '14px' }}>
            <span>
              {insertLineBreakAfterFirstComma(companySettingForAll?.address || '--')}
              <br />
              {formatPhoneNumber(companySettingForAll?.phone || '', '')}
              <br />
              {companySettingForAll?.email || ''}
            </span>
          </div>
        </div>
        <h1 className="text-center">CONSTRUCTION AGREEMENT</h1>
        <div className="grid-box">
          <div>
            <h4 style={{ margin: '5px 0' }} className="text-start">
              PROJECT LOCATION
            </h4>
            <span style={{ fontSize: '16px' }}>
              {opportunity?.contact?.firstName} {opportunity?.contact?.lastName}
              {/* {opportunity?.firstName} {opportunity?.lastName} */}
              <br />
              {opportunity?.street}
              <br />
              {opportunity?.city}, {opportunity?.state} {opportunity?.zip}
              <br />
              {formatPhoneNumber(opportunity?.contact?.phone, '') || ''}
              <br />
              {opportunity?.contact?.email || ''}
            </span>
          </div>
          <div className="text-start">
            <h4 style={{ margin: '5px 0' }}>CONTACT</h4>
            <span style={{ fontSize: '16px' }}>
              {opportunity?.contact?.firstName} {opportunity?.contact?.lastName}
              {/* {opportunity?.firstName} {opportunity?.lastName} */}
              <br />
              {opportunity?.contact?.street || ''}
              <br />
              {opportunity?.contact?.city || ''}, {opportunity?.contact?.state || ''} {opportunity?.contact?.zip || ''}
              <br />
              {formatPhoneNumber(opportunity?.contact?.phone, '') || ''}
              <br />
              {opportunity?.contact?.email || ''}
            </span>
          </div>

          <div className="text-start">
            <h4 style={{ margin: '5px 0' }}>
              DATE:{' '}
              <span style={{ fontWeight: 'normal' }}>
                {dayjsFormat(priceByProject?.[0]?.createdAt, 'M/D/YY') || '--'}
              </span>
            </h4>
            <h4 style={{ margin: '5px 0' }}>
              PO#:{' '}
              <span style={{ fontWeight: 'normal' }}>
                {opportunity?.PO}-{opportunity?.num}
              </span>
            </h4>
            {/* <h4>
              EST. START &emsp; <span>9/8/24</span>
            </h4> */}
          </div>
        </div>
        <div className="text-des">DESCRIPTION OF WORK TO BE COMPLETED</div>
        <>
          {order?.projects?.map((project: any, index: number) => {
            const projectTypeData = projectTypes?.find((itm: { _id: string }) => itm?._id === project?.type)
            const priceTypeData = priceByProject?.find((itm: any) => itm?.projectType?.id === project?.type)
            return (
              <div style={{ margin: '0 0 10px 0' }}>
                <div>
                  <h3 style={{ margin: '0 0 5px 0' }}>
                    <span>{priceTypeData?.packages?.find((v) => v._id === project?.basePackage)?.name || '--'}</span>{' '}
                    &emsp;
                    <span
                      style={{ color: 'grey', textTransform: 'capitalize', fontSize: '14px' }}
                    >{`${projectTypeData?.name}`}</span>
                  </h3>

                  {priceTypeData?.options
                    ?.filter((v: any) => project?.chosenOptions?.includes(v._id))
                    ?.map((option: any, index: number) => (
                      <li key={index}>{option.name}</li>
                    ))}
                </div>

                <div>
                  {contracts?.find((v) => v.projectType === project?.type)?.sections?.length ? (
                    <>
                      {contracts
                        ?.find((v) => v.projectType === project?.type)
                        ?.sections?.sort((a, b) => a.order - b.order)
                        ?.map((section: any, indexS: number) => (
                          <div key={indexS}>
                            <h4 style={{ margin: '10px 0 5px 0', textDecoration: 'underline' }}>{section?.title}</h4>
                            {section?.display
                              ?.sort((a, b) => a.order - b.order)
                              ?.map((d, indexD: number) => (
                                <div key={indexD} style={{ marginLeft: '20px' }}>
                                  {d?.groupId === '' ? (
                                    <>
                                      <div
                                        style={{
                                          display: 'grid',
                                          fontSize: '14px',
                                          padding: '2px 0',
                                          borderBottom: '1px solid lightgrey',
                                          gridTemplateColumns: '1fr',
                                        }}
                                      >
                                        <div>
                                          <span>{d?.text}</span>
                                        </div>
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      {sortTasks(project?.workOrder)
                                        ?.filter((v) => v.group == d?.groupId && !d?.removeTasks.includes(v.task_id))
                                        ?.map(
                                          (
                                            {
                                              taskName,
                                              taskUnit,
                                              rawValue,
                                            }: { taskName: string; taskUnit: string; rawValue: number },
                                            index: number
                                          ) => (
                                            <div
                                              style={{
                                                fontSize: '14px',
                                                padding: '2px 0',
                                                borderBottom: '1px solid lightgrey',
                                              }}
                                            >
                                              <span>
                                                {taskName}: {roundToNearestTenth(rawValue)?.toFixed(1)} {taskUnit}
                                              </span>
                                            </div>
                                          )
                                        )}
                                    </>
                                  )}
                                </div>
                              ))}
                          </div>
                        ))}
                    </>
                  ) : (
                    <div style={{ margin: '10px 0 0 20px', fontSize: '14px' }}>
                      {sortTasks(project?.workOrder)
                        ?.filter((v) => v.title !== 'Travel')
                        ?.map(
                          (
                            { taskName, taskUnit, rawValue }: { taskName: string; taskUnit: string; rawValue: number },
                            index: number
                          ) => {
                            return (
                              <>
                                <div
                                  style={{
                                    display: 'grid',
                                    padding: '2px 0',
                                    borderBottom: '1px solid lightgrey',
                                    gridTemplateColumns: '4fr repeat(2, 1fr)',
                                  }}
                                >
                                  <div>
                                    <span>{taskName}</span>
                                  </div>

                                  <div>
                                    <span>{roundToNearestTenth(rawValue)?.toFixed(1)}</span>
                                  </div>

                                  <div>
                                    <span>{taskUnit}</span>
                                  </div>
                                </div>
                              </>
                            )
                          }
                        )}
                      {/* <p className="no-margin" style={{ fontSize: '14px' }}>
                      Tear off 1 layer: 18.3 SQ
                    </p> */}
                    </div>
                  )}
                </div>

                <div className="grid-box" style={{ gridTemplateColumns: '55% 45%' }}>
                  <div>
                    <p style={{ margin: '10px 0 5px 0', fontSize: '14px', fontWeight: '600' }} className="text-start">
                      Notes
                    </p>
                    <div
                      style={{
                        border: '1px solid lightgrey',
                        padding: '10px',
                        fontSize: '14px',
                        whiteSpace: 'pre-line',
                        wordBreak: 'break-word',
                        overflowWrap: 'anywhere',
                      }}
                    >
                      {order?.projects?.[index]?.notes}
                    </div>
                  </div>

                  <div>
                    <div className="cost-summary">
                      <div>
                        <p
                          style={{ margin: '10px 0 5px 0', fontSize: '14px', fontWeight: '600' }}
                          className="text-center"
                        >
                          COLORS
                        </p>
                        &emsp;
                        <div className="color"></div>
                      </div>
                      {renderColors(order?.projects?.[index]?.colors, projectTypeData)}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </>

        <h4>Bid includes all applicable permits & disposal, all prices guaranteed for 7 days</h4>
        <div style={{ border: '2px solid #F4EEDA', marginBottom: '5px' }}></div>

        <>
          <div className="cost-summary">
            {order?.priceTotals?.discount && order?.priceTotals?.discount > 0 ? (
              <>
                <div>
                  <span>Project Cost:</span>
                  <span className="price">${formatNumberToCommaS(order?.priceTotals?.grandTotal)}</span>
                </div>
                <div id="discount-row">
                  <span>Discount:</span>
                  <span className="price">{`-$${formatNumberToCommaS(order?.priceTotals?.discount)}`} </span>
                </div>
              </>
            ) : null}

            <div>
              <span>Project Subtotal:</span>
              <span className="price">${formatNumberToCommaS(order?.priceTotals?.jobTotal)}</span>
            </div>
            <div>
              <span>State Taxes:</span>
              <span className="price">${formatNumberToCommaS(order?.priceTotals?.salesTax)} </span>
            </div>
            <div>
              <span>
                <b>Investment Total:</b>
              </span>

              <span className="price">
                <b>${formatNumberToCommaS(order?.priceTotals?.jobTotal + order?.priceTotals?.salesTax)}</b>
              </span>
            </div>
          </div>

          <div className="cost-summary">
            <div>
              <span style={{ textDecoration: 'underline' }}>Payment Schedule</span>
              <span className="price"></span>
            </div>
            {
              <div>
                <span>Scheduling Deposit ({(matchedProject?.deposit ?? 0.5) * 100}%):</span>
                <span className="price">
                  $
                  {formatNumberToCommaS(
                    ceilToDecimalPlaces(
                      (matchedProject?.deposit ?? 0.5) *
                        (order?.priceTotals?.jobTotal + (order?.priceTotals?.salesTax || 0)),
                      0
                    )
                  )}
                </span>
              </div>
            }
            {matchedProject?.downPmt > 0 ? (
              <div>
                <span>Job Start Payment ({(matchedProject?.downPmt ?? 0) * 100}%):</span>
                <span className="price">
                  $
                  {formatNumberToCommaS(
                    ceilToDecimalPlaces(
                      (matchedProject?.downPmt ?? 0) *
                        (order?.priceTotals?.jobTotal + (order?.priceTotals?.salesTax || 0)),
                      0
                    )
                  )}
                </span>
              </div>
            ) : null}
            {
              <div>
                <span>
                  Final Payment ({(1 - ((matchedProject?.deposit ?? 0.5) + (matchedProject?.downPmt ?? 0))) * 100}%):
                </span>
                <span className="price">
                  $
                  {formatNumberToCommaS(
                    order?.priceTotals?.jobTotal +
                      (order?.priceTotals?.salesTax || 0) -
                      (ceilToDecimalPlaces(
                        (matchedProject?.deposit ?? 0.5) *
                          (order?.priceTotals?.jobTotal + (order?.priceTotals?.salesTax || 0)),
                        0
                      ) +
                        ceilToDecimalPlaces(
                          (matchedProject?.downPmt ?? 0) *
                            (order?.priceTotals?.jobTotal + (order?.priceTotals?.salesTax || 0)),
                          0
                        ))
                  )}
                </span>
              </div>
            }
          </div>
        </>

        {/* <div>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            PAYMENT - Name 'New Heights Roofing' on payments with insured and all vested parties upon the execution of
            this Agreement by both parties. Scheduling Payment is non-refundable.
          </p>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            TERMS & CONDITIONS - This Proposal is expressly subject to Client’s acceptance of all of the terms above 
            and all of New Heights Roofing, LLC’s Terms and Conditions below, all of which are incorporated herein by 
            this reference. New Heights Roofing has the right to rescind this contract within seven (7) business 
            days after accepted date. Additional or modified terms by Client will be construed as proposals only and 
            will not become part of the Agreement unless expressly agreed by New Heights Roofing, LLC in writing. Colors 
            above shall be selected by Client and may only be changed by written Change Order.  Custom materials are not 
            refundable to Client. This Proposal is valid for seven (7) days, after which prices are subject to change.
          </p>
        </div>
        <div>
          <h4 style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>NEW HEIGHTS ROOFING WARRANTY</h4>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            1. Lifetime Workmanship Guarantee: New Heights Roofing guarantees your new roof system will be absolutely
            free of workmanship defects for life. If a defect is ever found in any aspect of the workmanship or
            installation, New Heights Roofing will repair the defect at no cost to the homeowner. This guarantee will
            become void if a person or firm other than New Heights Roofing performs or re-performs any work within the
            scope of this contract, or performs work that may affect any work within the scope of this contract.
          </p>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            2. Material Warranty: If a defect in the material is found New Heights Roofing will work with the homeowner
            to fix the problem to its fullest extent at the expense of the shingle manufacturer.
          </p>
        </div>
        <div>
          <h5 style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>
            NEW HEIGHTS ROOFING STANDARD OPERATING PROCEDURES & GUIDELINES
          </h5>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            A{')'} SETUP AND LOGISTICS: Owner will allow free access to driveway for delivery of materials and staging
            of containers and scaffolding. Owner is responsible for keeping vehicles, boats, trailers, and equipment
            away from the roof edge and agrees to release and discharge New Heights Roofing {'('}NHR{')'} from liability
            for any property damage resulting from the work being performed. Normal work hours are between 7am—7pm.
            <br />B{')'} TIME & MATERIALS: Any additional work not outlined in this proposal will be billed at $
            {priceByProject?.[0]?.variables?.manHourRate || '--'}/man hour + materials. NHR has Owner approval to
            replace or repair any damage found, and will make an attempt to contact and deliver updates. During the re
            roofing process, especially when installing new flashing, Owner understands that existing siding may be
            damaged. NHR will repair any damaged siding at the request of Owner at the rate above. NHR will leave any
            repair work in a ready-to-paint condition, Owner will be responsible for any painting work done. If any roof
            decking is found to be compromised or damaged, NHR will replace the decking w/ 7/16" OSB at $
            {priceByProject?.[0]?.variables?.plywoodRate?.toFixed(2) || '--'}
            /SF. If a different material is used {'('}either at Owner's request, or to match existing material{')'}, the
            difference in material cost will be added to that amount. If a substantial portion of the roof decking is
            found to be unacceptable by the project manager, NHR has Owner’s approval to replace all the sheathing on
            the home at the price above. {'(________)'}
            <br />C{')'} CHANGE ORDERS: Owner may ask that the scope of the work performed by NHR be changed. NHR shall
            have complete discretion as to whether or not to grant the request. Any request to change the contract shall
            be made directly to an officer of NHR.
            <br />D{')'} ROOF ATTACHMENTS: Any items or structures attached to the roof are Owner’s responsibility to
            remove and re-attach.
            <br />E{')'} INTERIOR PERSONAL PROPERTY: NHR is not responsible for damage that occurs to the interior of
            the home or other personal property during the project. Owner is responsible for covering any items in the
            attic or other exposed areas {'('}such as unfinished garages{')'}that may get dirty or damaged by falling
            debris.
            <br />F{')'} DELAYS: Should Owner directly or indirectly cause delays or interruptions in the performance of
            NHR’s work, Owner agrees to compensate NHR for its loss of time on the project at $
            {priceByProject?.[0]?.variables?.manHourRate || '--'}/man hour.
            <br />G{')'} PREMISES & PETS: Owner understands and agrees that any and all animals will be kept out of all
            work areas for the duration of the project. If at any time access to the work areas is not available to NHR
            crew members due to the presence of Owner's pet{'('}s{')'}, Owner agrees to reimburse NHR for expenses
            incurred for lost time at the rate of ${priceByProject?.[0]?.variables?.manHourRate || '--'}/man hour lost.
            Owner also understands that NHR crew members will not be responsible for any pet(s) leaving the home due to
            doors, windows, gates or other openings on the premises being left open or being hurt or damaged due to work
            in progress. Owner also agrees to clean up pet feces around the job site area.
            <br />H{')'} INTEREST: Any accrued balance owing and unpaid to NHR after 30 days shall bear an interest of
            1.5% per month.
            <br />I{')'} INSUFFICIENT FUNDS: Owner understands and agrees that any checks or other medium of payment
            presented to NHR by the Owner, or Owner's agent, that is returned to NHR for insufficient funds will incur
            an additional charge of $50, plus any and all fees assessed by the bank or other institution handling these
            monies.
            <br />J{')'} MARKETING DISCLOSURE: Owner hereby grants NHR the right to use their name as well as pictures
            of the work done and the home in its marketing materials or other oral, electronic, or written promotions.
            <br />K{')'} PERMITS: NHR shall obtain all necessary building permits and asbestos tests required by the
            local government. One or more inspections will be performed by the city or county with jurisdiction. Due to
            certain unforeseen issues, these sometimes fail, however NHR will work with the city or county to ensure a
            passing inspection is completed. Every project installed by NHR is thoroughly inspected internally and
            surpasses building code requirements. If asbestos is found in existing roofing material there may be
            additional costs to properly abate the hazardous material. {'(________)'}
            <br />L{')'} EXTREME WEATHER & NATURAL DISASTERS: In cases of extreme weather conditions or natural
            disasters like tornadoes, hurricanes, ice dams, winds in excess of product wind warrant, tree damage, hail
            storms, and earthquakes, the manufacturer’s warranty nor the workmanship warranty will cover damage to your
            roof or home. This type of damage is normally covered by homeowners insurance. 
            <br />M{')'} DISPUTE RESOLUTION: If Owner is dissatisfied with the work performed by NHR, Owner will provide
            a letter outlining in detail any defects Owner is alleging in NHR’s work and give NHR the opportunity to
            inspect and cure the alleged defects. If after NHR’s inspection Owner and NHR are unable to agree on how to
            resolve Owner's complaints, the parties agree to arbitrate the matter.
            <br />N{')'} LAW PROVISION: The parties will submit all their disputes arising out of or in connection with
            this Agreement to the exclusive jurisdiction of the Courts where the project took place. Parties will use
            Idaho law to resolve disputes related to Idaho projects and Washington law to resolve disputes related to
            Washington projects.
            <br />O{')'} ATTORNEY’S FEES:  In the event that any suit or action is instituted under or in relation to
            this Contract, including without limitation to enforce any provision in this Contract, the prevailing party
            in such dispute shall be entitled to recover from the losing party all fees, costs and expenses of enforcing
            any right of such prevailing party under or with respect to this Contract, including without limitation,
            such reasonable fees and expenses of attorneys and accountants, which shall include, without limitation, all
            fees, statutory costs, arbitration costs and expenses of appeals.
            <br />P{')'} WAIVER OF CONTRACTUAL RIGHT: The failure of either party to enforce any provision of this
            Agreement shall not be construed as a waiver or limitation of that party’s right to subsequently enforce and
            compel strict compliance with every provision of this Agreement.
            <br />Q{')'} TRANSMISSION OF DOCUMENTS: Facsimile or electronic transmission of any signed original
            document, and retransmission of any signed facsimile or electronic transmission shall be the same as
            delivery of an original. At the request of either Owner or NHR, Owner and NHR will confirm facsimile or
            electronic transmitted signatures by signing an original document.
            <br />R{')'} AUTHORITY OF SIGNATORY: If Owner is a corporation, partnership, trust, estate, or other entity,
            the person executing this agreement on its behalf warrants his or her authority to do so and to bind Owner.
            <br />S{')'} ENTIRE AGREEMENT: The terms hereof constitute the entire agreement and supersede all prior
            agreements, negotiations and discussions between parties. This agreement may be modified only by a written
            agreement signed by each of the parties.
            <br />T{')'} CREDIT CARD PAYMENTS: Payments made by credit card will incur an additional 3.5% charge on the
            total amount being paid. Any American Express cards will be a 4% additional charge on the total amount being
            paid.
          </p>
        </div>
        <div>
          <h5 style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>
            GENERAL CONTRACTOR RESIDENTIAL PROPERTY DISCLOSURE
          </h5>
          <p className="no-margin" style={{ fontSize: '14px' }}>
            New Heights Roofing (“General Contractor”) provides to {opportunity?.client?.firstName || ''}{' '}
            {opportunity?.client?.lastName || ''} (“Property Owner(s) or Purchaser(s)”) this disclosure statement as
            required under Idaho Code § 45-525.
            <br />
            <br />
            In connection with a contract to construct, alter or repair improvements on residential property or a
            contract to sell newly constructed residential property, general contractors are required to advise
            homeowners or residential property purchasers of their rights under Idaho Code § 45-525:
            <br />
            <br />
            1. The right, at the reasonable expense of the homeowner or residential real property purchaser, to require
            that the general contractor obtain lien waivers from any subcontractors providing services or materials to
            the general contractor;
            <br />
            <br />
            2. The right to receive from the general contractor proof that the general contractor has a general
            liability insurance policy including completed operations in effect and proof that the general contractor
            has worker’s compensation insurance for his employees as required by Idaho law;
            <br />
            <br />
            3. The right and opportunity to purchase an extended policy of title insurance from a title insurance
            company which would provide insurance coverage covering certain liens which may be unfiled or unrecorded; 
            <br />
            <br />
            4. The right to require at the homeowner or residential real property purchaser’s expense, a surety bond in
            an amount up to the value of the construction project.
            <br />
            <br />
            Acknowledgement:
            <br />
            <br />
            The homeowner(s) or residential real property purchaser(s), hereby acknowledge(s) receipt of a copy of this
            General Contractor Residential Property Disclosure.
          </p>
        </div> */}
        <div
          className="fine-print"
          dangerouslySetInnerHTML={{
            __html: replaceDynamicValues(finePrintContent?.content || '', {
              manHourRate: formatNumberToCommaS(priceByProject?.[0]?.variables?.manHourRate) || 0,
              plywoodRate: formatNumberToCommaS(priceByProject?.[0]?.variables?.plywoodRate) || 0,
              firstName: opportunity?.contact?.firstName,
              lastName: opportunity?.contact?.lastName,
            }),
          }}
          // style={{
          //   padding: '12px',
          //   fontSize: '16px',
          //   lineHeight: '1.5',
          //   backgroundColor: '#f9f9f9',
          //   borderRadius: '6px',
          // }}
        />

        {/* <div>
          <ReactMarkdown>
            {replaceDynamicValues(content, {
              manHourRate: 50,
              plywoodRate: 2.5,
            })}
          </ReactMarkdown>
        </div> */}
        {/* <div className="grid-box" style={{ gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <div>
            <h4 className="text-start">OWNER</h4>
            <br />
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Signature
            </p>
            <p>
              {opportunity?.client?.firstName || ''} {opportunity?.client?.lastName || ''}
            </p>
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Print Name
            </p>
            <br />
            <br />
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Date
            </p>
          </div>
          <div className="text-start">
            <h4>NEW HEIGHTS ROOFING LLC</h4>
            <br />
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Signature
            </p>
            <p>{opportunity?.salesPersonName || ''}</p>
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Print Name
            </p>
            <br />
            <br />
            <div className="border"></div>
            <p className="no-margin" style={{ fontSize: '14px' }}>
              Date
            </p>
          </div>
        </div> */}

        <div className="grid-box-sign">
          <div className="signature-section">
            <h4>OWNER</h4>
            <div className="line"></div>
            <p className="label">Signature</p>

            <div className="flex-row">
              <div className="line full-width">
                <span className="name">
                  {opportunity?.contact?.firstName || ''} {opportunity?.contact?.lastName || ''}
                </span>
              </div>
            </div>
            <p className="label">Print Name</p>

            <div className="line full-width"></div>
            <p className="label">Date</p>
          </div>

          <div className="signature-section">
            <h4>NEW HEIGHTS ROOFING LLC</h4>
            <div className="line"></div>
            <p className="label">Signature</p>

            <div className="flex-row">
              <div className="line full-width">
                <span className="name">{opportunity?.salesPersonName || ''}</span>
              </div>
            </div>
            <p className="label">Print Name</p>

            <div className="line full-width"></div>
            <p className="label">Date</p>
          </div>
        </div>
      </div>
    )
  }
)

export default PrintContract
