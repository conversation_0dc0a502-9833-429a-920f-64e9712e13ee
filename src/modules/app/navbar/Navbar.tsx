import { memo, useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'

import HamBurgerSvg from '../../../assets/newIcons/hamburger.svg'
import NavSwitchSvg from '../../../assets/newIcons/navSwitch.svg'
import QuestionSvg from '../../../assets/newIcons/question.svg'
import { setCurrentCompany } from '../../../logic/redux/actions/company'
import { setNavCollapsed, setShowCompanyCreationModal, setShowMobileSideNav } from '../../../logic/redux/actions/ui'
import { useAppDispatch } from '../../../logic/redux/reduxHook'
import Dropdown from '../../../shared/components/dropdown/Dropdown'
import Popover from '../../../shared/components/popover/Popover'
import { OptionItem } from '../../../shared/dropDown/style'
import { StageGroupEnum, StorageKey, SubscriptionPlanType } from '../../../shared/helpers/constants'
import { AddSvg, AvatarSvg, nhrLogoSvg } from '../../../shared/helpers/images'
import {
  getDataFromLocalStorage,
  getEnumValue,
  getInitials,
  getWeekRange,
  isSuccess,
  notify,
  uuidToColor,
} from '../../../shared/helpers/util'
import useWindowDimensions from '../../../shared/hooks/useWindowDimensions'
import { FlexRow, RoundButton } from '../../../styles/styled'
import ProfileDrop from './components/ProfileDrop'
import * as Styled from './style'
import { getCrewMemberData } from '../../../logic/apis/dashboard'
import { updateActivityTimeCard, updateForgotClockoutTimeCard } from '../../../logic/apis/approveTimeCard'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import useDebounce from '../../../shared/hooks/useDebounce'
import { searchOpps } from '../../../logic/apis/sales'
import { Form, Formik } from 'formik'
import {
  DropDownContainer,
  DropDownContentContainer,
  DropDownItem,
  DropDownOuterContainer,
  SearchBarContainer,
  SearchInput,
} from '../../sales/style'
import { SmallLoaderCont } from '../../../shared/components/loader/style'
import { useClickOutside } from '../../../shared/hooks/useClickOutside'
import { SearchIcon } from '../../../assets/icons/SearchIcon'
import { TooltipPortal } from '../../../shared/components/tooltip'
import { helpPath } from '../../../logic/paths'
import { Info, NameCont } from '../../../shared/components/profileInfo/style'
import { SLoader } from '../../../shared/components/loader/Loader'
import Button from '../../../shared/components/button/Button'
import { useSelector } from 'react-redux'
import { OppSearchResponse } from './Navbar.test'
// import { getMemberPosition } from '../../../logic/apis/position'

export const addOptions = [
  {
    label: 'Add Company',
    value: 'company',
  },
  {
    label: 'Add Package',
    value: 'package',
  },
  {
    label: 'Add Task',
    value: 'task',
  },
  {
    label: 'Add Client ',
    value: 'client',
  },
]

const Navbar = () => {
  const currentCompany = getDataFromLocalStorage(StorageKey.currentCompany)

  const [searchTerm, setSearchTerm] = useState('')
  // const debouncedSearch = useDebounce(searchTerm, 500)
  const [searchLoading, setSearchLoading] = useState(false)
  const [searchData, setSearchData] = useState<OppSearchResponse[]>([])
  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const dropdownRef = useRef(null)
  const [isSearchActive, setIsSearchActive] = useState(false)
  const [permissions, setPermissions] = useState<{ [key: string]: boolean }>({})
  useClickOutside(dropdownRef, setShowDropdown)
  // useClickOutside(dropdownRef, setIsSearchActive)

  // const [dropDownVal, setDropDownVal] = useState({
  //   label: currentCompany?.companyName,
  // })

  const [dropDownProjectValue, setDropDownProjectValue] = useState<any>([])

  const globalSelector = useSelector((state: any) => state)
  const { navCollapsed } = globalSelector.ui
  const { companies, currentMember, positionDetails, positionPermissions } = globalSelector.company
  const { profileInfo } = globalSelector.auth
  const [searchLoaded, setSearchLoaded] = useState(false)

  const { pathname } = useLocation()

  // const [timeCardData, setTimeCardData] = useState([])

  const dispatch = useAppDispatch()

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  // useEffect(() => {
  //   ;(async () => {
  //     if (debouncedSearch) {
  //       const sanitizedSearch = debouncedSearch.replace(/[\(\)\-]/g, '')
  //       try {
  //         setSearchLoading(true)
  //         const res = await searchOpps({
  //           search: sanitizedSearch,
  //           salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : '',
  //           projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
  //         })

  //         setSearchData(res?.data?.data?.opps)
  //       } catch (error) {
  //         console.error('Search Error', error)
  //       } finally {
  //         setSearchLoading(false)
  //       }
  //     }
  //   })()
  // }, [debouncedSearch])

  const organizationOptions = companies?.map((item: any) => {
    return {
      ...item?.company,
      label: item?.company?.companyName,
      value: item?.company?._id,
      image: AvatarSvg,
    }
  })

  const handleOptionClick = (option: { label: string; value: string }) => {
    switch (option?.value) {
      case 'company':
        dispatch(setShowCompanyCreationModal(true))

        break

      default:
        break
    }
  }

  // const getTimecardStatus = async () => {
  //   const { start } = getWeekRange(0)
  //   const res = await getCrewMemberData(
  //     currentCompany._id,
  //     currentMember?._id,
  //     start ? dayjs(start)?.toISOString() : '',
  //     new Date()?.toISOString()
  //   )

  //   const data = res?.data?.data?.data?.filter((item: any) => item?.cards?.find((card: any) => card?.active))
  //   setTimeCardData(data)
  // }

  useEffect(() => {
    if (currentCompany?.companyName && currentMember?._id) {
      // setDropDownVal({
      //   label: currentCompany?.companyName,
      // })

      checkClockOut()
      // ;(async () => {
      //   const response = await getMemberPosition({ companyId: currentCompany._id }, uID)

      //   if (isSuccess(response)) {
      //     const processedDict: { [key: string]: boolean } = {}
      //     for (const item of response?.data?.data?.memberPosition?.permissions) {
      //       const resource = item.resource
      //       const permissions = item.permissions
      //       if (permissions.some((p: any) => [1, 2, 3].includes(p))) {
      //         processedDict[resource] = true
      //       } else if (permissions.includes(4)) {
      //         processedDict[resource] = false
      //       }
      //     }

      //     setPermissions(processedDict)
      //   }
      // })()

      // getTimecardStatus()
    }
  }, [currentCompany?.companyName, currentMember?._id])

  const navigate = useNavigate()

  const { width } = useWindowDimensions()

  const checkClockOut = async () => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const res = await updateForgotClockoutTimeCard({
      memberId: currentMember?._id,
      startDate: today?.toISOString(),
    })

    const timecardId = res?.data?.data?.timecardId

    if (isSuccess(res) && timecardId) {
      const response = await updateActivityTimeCard(timecardId, {
        status: 'Forgot to Clock out',
        timeOut: res?.data?.data?.timeOut,
      })

      if (isSuccess(response)) {
        window.location.reload()
      }
    }

    notify(res?.data?.data?.message, 'info')
  }

  const handleSearchSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (searchTerm) {
      const isProbablyPhoneNumber = /^\(?\d{3}\)?[\s\-]?\d{3}[\s\-]?\d{4}$/

      const sanitizedSearch = isProbablyPhoneNumber.test(searchTerm) ? searchTerm.replace(/[()\-\s]/g, '') : searchTerm

      try {
        setSearchLoading(true)
        setSearchData([])
        const res = await searchOpps({
          search: sanitizedSearch,
          salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : '',
          projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
        })

        setSearchData(res?.data?.data?.opps)
      } catch (error) {
        console.error('Search Error', error)
        setSearchData([])
      } finally {
        setSearchLoading(false)
        setSearchLoaded(true)
      }
    }
  }

  return (
    <Styled.HeaderContainer>
      <FlexRow justifyContent="space-between">
        <Styled.NavLeftWrap>
          {companies?.length ? (
            <>
              {width <= 1024 ? (
                <Styled.HamIconStyle
                  onClick={() => {
                    dispatch(setNavCollapsed(false))
                    dispatch(setShowMobileSideNav(true))
                    localStorage.setItem(StorageKey.navCollapsed, 'true')
                  }}
                  src={HamBurgerSvg}
                  alt="sideNav-toggle"
                />
              ) : (
                <TooltipPortal
                  customStyle={{ transform: 'translate(-30%,0)', top: '50px' }}
                  content={`${navCollapsed ? 'Expand' : 'Collapse'} Sidebar`}
                  position="bottom"
                >
                  <Styled.HamIconStyle
                    onClick={() => {
                      dispatch(setNavCollapsed(!navCollapsed))
                      const value = getDataFromLocalStorage(StorageKey.navCollapsed)
                      localStorage.setItem(StorageKey.navCollapsed, JSON.stringify(!value))
                    }}
                    src={NavSwitchSvg}
                    alt="sideNav-toggle"
                    className={navCollapsed ? '' : 'rotate'}
                  />
                </TooltipPortal>
              )}
            </>
          ) : null}

          {/* <Styled.Logo src={nhrLogoSvg} alt="logo" /> */}

          {/* <Dropdown
            onSelectValue={(val) => {
              setDropDownVal(val)
              localStorage.setItem(StorageKey.currentCompany, JSON.stringify(val))
              dispatch(setCurrentCompany(val))
              navigate(`/profile`)
              window.location.reload()
            }}
            options={organizationOptions}
            selectedValue={dropDownVal}
            className="org"
            isOrganization
          >
            <Styled.SelectedOrg>
              <p>Your Organization</p>

              <FlexRow>
                <img src={AvatarSvg} alt="organization image" />
                <h5>{!dropDownVal?.label ? 'Select Organization' : dropDownVal?.label}</h5>
              </FlexRow>
            </Styled.SelectedOrg>
          </Dropdown> */}

          {isProPlusPlan ? (
            <>
              {positionPermissions?.sales || positionPermissions?.operations ? (
                <Styled.SearchCont className={'active'} id={showDropdown ? 'show-border' : ''}>
                  <SearchIcon />
                  {/* <AutoComplete
                    value={values?.searchVal}
                    labelName="Search by name, PO#, phone, address"
                    stateName="searchVal"
                    apiSearch
                    className="search"
                    options={searchData?.map(
                      (result: any) =>
                        `${result?.PO}-${result?.num}: ${result?.firstName} ${result?.lastName} - ${result?.stageName}`
                    )}
                    setValueOnClick={(val: string) => {
                      // s(val)
                    }}
                    dropdownHeight="300px"
                    borderRadius="0px"
                    // selectedValue={task}
                    // validate
                    setTypeForAction={() => {
                      setSearchTerm(values?.searchVal)
                    }}
                    // disabled={isClockedIn}
                    setFieldValue={setFieldValue}
                  /> */}

                  <DropDownOuterContainer
                  // onClick={() => {
                  //   setIsSearchActive(true)
                  // }}
                  >
                    <DropDownContainer>
                      <form onSubmit={handleSearchSubmit}>
                        <SearchBarContainer
                          className="search-loader"
                          onClick={() => {
                            setShowDropdown(true)
                          }}
                        >
                          <SearchInput
                            placeholder="Search name, PO#, phone, address"
                            value={searchTerm}
                            onFocus={(e: any) => {
                              e.target.select()
                            }}
                            onChange={(e: any) => setSearchTerm(e.target.value)}
                          />
                          {/* {searchLoading ? <SmallLoaderCont /> : null} */}

                          <Button padding="10px 12px" width="max-content" isLoading={searchLoading}>
                            Find
                          </Button>

                          {/* StageGroupEnum?.stageGroup */}
                          {searchData?.length > 0 && (
                            <DropDownContentContainer
                              ref={dropdownRef}
                              visibility={showDropdown}
                              className={searchLoading ? 'searchLoading height' : 'height'}
                            >
                              {searchData?.map((result: any) => (
                                <DropDownItem
                                  key={result._id}
                                  onClick={() => {
                                    setShowDropdown(false)
                                    setSearchTerm('')
                                    if (result?.stageGroup === StageGroupEnum.Leads) {
                                      window.location.href = `/contact/profile/${result?._id}
                                      `
                                    } else {
                                      window.location.href = `/${getEnumValue(result?.stageGroup)}/opportunity/${
                                        result._id
                                      }`
                                    }
                                  }}
                                >
                                  {result?.stageGroup === StageGroupEnum.Leads ? (
                                    <>
                                      {result?.fullName} {result?.stageName ? ` - ${result?.stageName}` : ''}{' '}
                                      {(result?.status && `(${result?.status})`) || ''}
                                    </>
                                  ) : (
                                    <>
                                      {result?.PO}
                                      {result?.num ? `-${result?.num}` : ''}
                                      {result?.fullName ? `: ${result?.fullName}` : ''}
                                      {result?.stageName ? ` - ${result?.stageName}` : ''}{' '}
                                      {(result?.status && `(${result?.status})`) || ''}
                                    </>
                                  )}
                                </DropDownItem>
                              ))}
                            </DropDownContentContainer>
                          )}

                          {searchTerm && !searchData?.length && !searchLoading && searchLoaded && (
                            <DropDownContentContainer ref={dropdownRef} visibility={showDropdown} className={'height'}>
                              <DropDownItem>No results found</DropDownItem>
                            </DropDownContentContainer>
                          )}
                        </SearchBarContainer>
                      </form>
                    </DropDownContainer>
                  </DropDownOuterContainer>
                </Styled.SearchCont>
              ) : null}
            </>
          ) : null}
        </Styled.NavLeftWrap>

        <Styled.NavRightWrap gap="14px">
          <FlexRow gap="16px">
            {/* <Popover triggerComponent={renderAddIcon()} className="add">
              {addOptions?.map((option) => (
                <OptionItem key={option.value} onClick={() => handleOptionClick(option)}>
                  <p>{option.label}</p>
                </OptionItem>
              ))}
            </Popover> */}
            {/* <RoundButton>setShowCompanyCreationModal
              <img src={BellSvg} alt="bell icon" />
            </RoundButton> */}

            <FlexRow gap="16px">
              {positionDetails?.symbol === 'Owner' || positionDetails?.symbol === 'Admin' ? (
                <Styled.Avatar
                  src={QuestionSvg}
                  alt="help icon"
                  style={{ padding: '8px' }}
                  onClick={() => {
                    navigate(helpPath, {
                      state: {
                        fromRoute: pathname,
                      },
                    })
                  }}
                />
              ) : null}
              <Popover
                className="pop-avatar"
                triggerComponent={
                  <>
                    {profileInfo?.imageUrl ? (
                      <Styled.Avatar src={profileInfo?.imageUrl ?? AvatarSvg} alt="avatar icon" />
                    ) : (
                      <NameCont bg={profileInfo?.id ? uuidToColor(profileInfo?.id!) : '#efefef'} className="drop">
                        {profileInfo?.firstName ? (
                          <Info>{getInitials(`${profileInfo?.firstName} ${profileInfo?.lastName}`)}</Info>
                        ) : null}
                      </NameCont>
                    )}
                  </>
                }
              >
                <ProfileDrop />
              </Popover>
            </FlexRow>
          </FlexRow>
        </Styled.NavRightWrap>
      </FlexRow>
    </Styled.HeaderContainer>
  )
}

export default memo(Navbar)

const renderAddIcon = () => {
  return (
    <FlexRow justifyContent="flex-end">
      <RoundButton className="add">
        <img src={AddSvg} alt="add icon" />
      </RoundButton>
    </FlexRow>
  )
}
