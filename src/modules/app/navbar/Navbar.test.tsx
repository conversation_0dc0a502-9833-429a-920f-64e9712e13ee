import * as Yup from 'yup'
import { searchOpps } from '../../../logic/apis/sales'
import { act } from '@testing-library/react'

const oppSearchSchema = Yup.object({
  _id: Yup.string().uuid(),
  PO: Yup.string(),
  num: Yup.string(),
  street: Yup.string(),
  status: Yup.string(),
  createdAt: Yup.date(),
  phone: Yup.string(),
  email: Yup.string().email(),
  fullName: Yup.string(),
  stageId: Yup.string().uuid(),
  stageName: Yup.string(),
  stageGroup: Yup.string(),
})

export type OppSearchResponse = Yup.InferType<typeof oppSearchSchema>

beforeAll(() => {
  require('dotenv').config()
  const token = process.env.TESTING_API_KEY
  localStorage.setItem('token', JSON.stringify(token))
})

describe('Opp Search API', () => {
  test('renders navbar without crashing', async () => {
    await act(async () => {
      const response = await searchOpps({
        search: 'a',
      })

      const data = response?.data?.data?.opps?.[0]

      await expect(oppSearchSchema.validate(data)).resolves.toBeTruthy()
    })
  })
})
