import { RouterProvider } from 'react-router-dom'
import 'react-toastify/dist/ReactToastify.css'

import createAppRouter from './routes/NewRoute'
import './App.css'

export const App = () => {
  // Create the router
  const router = createAppRouter()

  console.log('Form Merged===[log]===>')

  return (
    <RouterProvider
      future={{
        v7_startTransition: true,
      }}
      router={router}
    />
  )
}
