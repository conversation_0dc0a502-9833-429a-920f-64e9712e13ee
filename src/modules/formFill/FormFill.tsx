import React, { useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { getCompanyFormById } from '../../logic/apis/form'
import { extractLocalTime, generateUUID, isSuccess, notify } from '../../shared/helpers/util'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import './styles.css'
import {
  createOpportunityForm,
  getOpportunityById,
  opportunityFormById,
  updateChecklist,
  updateOpportunityForm,
} from '../../logic/apis/sales'
import { useSelector } from 'react-redux'
import * as SharedStyled from '../../styles/styled'
import { SLoader } from '../../shared/components/loader/Loader'
import Button from '../../shared/components/button/Button'
// import html2pdf from 'html2pdf.js'
import {
  FilePayload,
  createMediaOpportunity,
  deleteMediaOpportunity,
  getAllMedia,
  getOpportunityMedia,
  getPresignedUrlMedia,
} from '../../logic/apis/media'

import useFetch from '../../logic/apis/useFetch'
import ReactDOMServer from 'react-dom/server'
import { compressImage, extractImageData, getThumbnailUrl, validateFiles, validateFormFiles } from '../media/mediaUtils'
import { weatherApi } from '../../logic/apis/wheatherApi'
import FormPDFLayout from './components/FormPDFLayout'
import { getConfig } from '../../config'
import { processFile } from '../media/Media'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { FilePathTypeEnum } from '../../shared/helpers/constants'
import { I_Opportunity } from '../opportunity/Opportunity'
import { generateWeatherString, getCurrentLocation } from './components/constant'
import FileUpload from './components/FileUpload'
import OpportunityMediaModal from './components/OpportunityMediaModal'
import CameraFileUpload from './components/CameraFileUpload'
import useConfirmExit from '../../shared/hooks/useConfirmExit'
import NewFormPDFLayout from './components/NewFormPDFLayout'
import { pdf } from '@react-pdf/renderer'

interface I_Form {
  fields: any
  name: string
  createdBy: string
  description: string
  active: boolean
}

const FormFill = () => {
  const { builderFormId, oppId, formId, imageId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company
  const [loading, setLoading] = useState(true)
  const [progress, setProgress] = useState(0)
  const [oppData, setOppData] = useState<I_Opportunity | undefined>()
  const [weather, setWeather] = useState<{ status: any; data: any }>()
  const [btn, setBTN] = useState(false)
  const [coordinates, setCoordinates] = useState(null)
  const [loadingSubmit, setLoadingSubmit] = useState(false)
  const formRef = useRef<HTMLDivElement>(null)
  const { company } = useAppSelector((state) => state)
  const { companySettingForAll } = company
  const { state } = useLocation()
  const [isDirty, setIsDirty] = useState(false)

  useConfirmExit(isDirty, 'If you exit now, all entered data will be lost. Do you want to leave? ')

  const stepName = state?.stepName

  function extractZipFromAddress(address: string) {
    const zipMatch = address.match(/\b\d{5}\b/)
    return zipMatch ? zipMatch[0] : null
  }

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault()
      e.returnValue = ''
    }

    if (isDirty) {
      window.addEventListener('beforeunload', handleBeforeUnload)
    } else {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [isDirty])

  const navigate = useNavigate()
  const [formDataById, setFormDataById] = useState<I_Form>({
    fields: [],
    name: '',
    createdBy: '',
    description: '',
    active: true,
  })

  const [imageBase64, setImageBase64] = useState<string | null>(null)
  const mapKey = getConfig()?.mapsAPIKey!

  const notIncludeInLabel = ['weather', 'location', 'paragraph', 'header']

  useEffect(() => {
    if (coordinates) {
      fetchMapImage()
    }
  }, [coordinates])

  useEffect(() => {
    if (!formId) {
      getCurrentLocation()
        .then((coords: any) => setCoordinates(coords))
        .catch((error) => console.error('Location Error:', error))
    }
  }, [formId])

  const fetchMapImage = async () => {
    try {
      const { latitude, longitude } = coordinates
      const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=${17}&size=400x300&markers=color:red%7C${latitude},${longitude}&key=${mapKey}`
      const response = await fetch(staticMapUrl)
      const blob = await response.blob()

      const reader = new FileReader()
      reader.onloadend = () => {
        setImageBase64(reader.result as string)
      }
      reader.readAsDataURL(blob)
    } catch (error) {
      console.error('Failed to load map image:', error)
    }
  }
  useEffect(() => {
    if (oppId) {
      const fetchOpportunity = async () => {
        try {
          const res = await getOpportunityById({ opportunityId: oppId!, deleted: false })
          if (isSuccess(res)) {
            const { opportunity } = res?.data?.data
            setOppData(opportunity)
          }
        } catch (error) {
          console.log({ error })
        }
      }
      fetchOpportunity()
    }
  }, [oppId])

  useEffect(() => {
    const handleWeather = async () => {
      try {
        const zip: string = oppId ? oppData?.zip || '' : extractZipFromAddress(companySettingForAll?.address || '')
        const res = await weatherApi(zip, new Date().toISOString())

        if (isSuccess(res)) {
          setWeather(res)
        }
      } catch (error) {
        console.log({ error })
      }
    }
    if (!formId && oppData) {
      handleWeather()
    }
  }, [oppData, formId])

  useEffect(() => {
    if (formId) {
      const fetchOppFormById = async () => {
        try {
          const res = await opportunityFormById(formId)
          if (isSuccess(res)) {
            const { form } = res?.data?.data
            setFormDataById(form)
            setImageBase64(form?.locationImage)
            setWeather(form?.weather)
          }
        } catch (error) {
          console.log({ error })
        } finally {
          setLoading(false)
        }
      }
      fetchOppFormById()
    }
  }, [formId])

  useEffect(() => {
    if (builderFormId && !formId) {
      const fetchFormById = async () => {
        try {
          const res = await getCompanyFormById(builderFormId)
          if (isSuccess(res)) {
            const { form } = res?.data?.data
            setFormDataById(form)
          }
        } catch (error) {
          console.log({ error })
        } finally {
          setLoading(false)
        }
      }
      fetchFormById()
    }
  }, [builderFormId])

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: formDataById.fields.reduce((acc: any, field: any, index: number) => {
      // Default to [] for checkbox-group and file fields
      if (field.type === 'checkbox-group' || field.type === 'file') {
        acc[field.name] = Array.isArray(field.value) ? field.value : []
      } else {
        // preserve 0 and false; only default to '' when value is null/undefined
        acc[field.name] = field.value ?? ''
      }
      acc[`${field.name}_order`] = index + 1
      return acc
    }, {}),

    validationSchema: Yup.object(
      formDataById.fields.reduce((acc: any, field: any) => {
        if (field.required) {
          if (field.type === 'checkbox-group' || field.type === 'file') {
            acc[field.name] = Yup.array().min(1, 'At least one item is required')
          } else {
            acc[field.name] = Yup.string().required('Required')
          }
        }
        return acc
      }, {})
    ),

    onSubmit: (values) => {
      handleFormSubmit(values)
    },
  })

  useEffect(() => {
    setIsDirty(formik?.dirty)
  }, [formik?.dirty])

  const handleFileChange = async (fileValue: any[], formName: string) => {
    try {
      const newFiles = fileValue.filter((file) => file.file)
      if (newFiles.length === 0) {
        console.log('No new files to upload.')
        return
      }
      const validFiles = validateFormFiles(newFiles, {
        maxImageSizeMB: company?.companySettingForAll?.maxImageSizeMB,
        maxVideoSizeMB: company?.companySettingForAll?.maxVideoSizeMB,
        maxAudioSizeMB: company?.companySettingForAll?.maxAudioSizeMB,
      })

      if (validFiles?.length) {
        const res = await getPresignedUrlMedia(
          oppId ? FilePathTypeEnum.Project : FilePathTypeEnum.Member,
          validFiles.map((itm: any) => ({
            fileName: itm.name,
            mimetype: itm.type.includes('video') ? 'video/mp4' : itm.type.includes('audio') ? 'audio/mp3' : itm.type,
          })),
          currentMember?._id,
          oppId!
        )
        if (!isSuccess(res)) {
          return
        }

        const urlData: Record<string, any> = res?.data?.data?.signedUrls?.reduce((acc: any, itm: any) => {
          acc[itm.fileName] = itm
          return acc
        }, {})

        const uploadPromises = validFiles.map(async (itm: any) => {
          const myHeaders = new Headers()
          myHeaders.append(
            'Content-Type',
            itm.type.includes('video') ? 'video/mp4' : itm.type.includes('audio') ? 'audio/mp3' : itm.type
          )

          const file = itm?.mimeType?.includes('image') ? (await compressImage(itm.file))?.file : itm.file

          const requestOptions = {
            method: 'PUT',
            headers: myHeaders,
            body: file,
          }

          const response = await fetch(urlData[itm.name]?.url, requestOptions)
          if (!response.ok) {
            notify('Failed to upload', 'error')
            throw new Error(`Failed to upload ${itm.name}: ${response.statusText}`)
          }
          return response
        })

        await Promise.all(uploadPromises)

        const payload: FilePayload[] = []
        console.log({ validFiles, fileValue, urlData })
        const onlyImages = validFiles.filter((itm: any) => itm.type.includes('image'))

        const metaData = await extractImageData(onlyImages)

        validFiles?.forEach((itm: any) => {
          if (itm?.mimetype?.includes('image')) {
            payload.push({
              _id: generateUUID()!,
              createdAt: metaData?.[itm?.name]?.createdAt,
              createdBy: currentMember?._id,
              mimetype: itm?.type,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              location: metaData?.[itm?.name]?.location || undefined,
              tags: itm?.tag ? [itm.tag, formName] : [formName],
              formFieldByName: itm?.formFieldByName,
            })
          } else {
            payload.push({
              _id: generateUUID()!,
              createdAt: new Date().toISOString(),
              createdBy: currentMember?._id,
              mimetype: itm.type.includes('video') ? 'video/mp4' : itm.type.includes('audio') ? 'audio/mp3' : itm.type,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              tags: itm?.tag ? [itm.tag, formName] : [formName],
              formFieldByName: itm?.formFieldByName,
            })
          }
        })

        const uploadRes = await createMediaOpportunity(oppId!, payload)

        if (isSuccess(uploadRes)) {
          notify(uploadRes?.data?.data?.message, 'success')
          return uploadRes?.data?.data?.images
        }
      }
    } catch (error) {
      console.log('error======>', error)
    }
  }

  const handleFormSubmit = async (values: any) => {
    try {
      setLoadingSubmit(true)

      setProgress(20)
      let formattedResponses = formDataById.fields.map((field, index) => {
        const fieldValue =
          values[field.name] !== undefined
            ? values[field.name]
            : field.value !== undefined
            ? field.value
            : field.type === 'file' || field.type === 'checkbox-group'
            ? []
            : ''

        return {
          ...field,
          value: fieldValue,
          order: index + 1,
        }
      })

      const form_Id = formId ? formId : generateUUID()!
      const image_Id = generateUUID()!
      // const fileResponse = formattedResponses?.find(
      //   ({ type, accept }: { type: string; accept: string }) => type === 'file' && !accept
      // )
      // const fileValue = Array.isArray(fileResponse?.value)
      //   ? fileResponse.value.filter(({ file }: { file: File }) => file)
      //   : []

      const fileValue =
        formattedResponses
          ?.filter(({ type }) => type === 'file') // Only process file-type fields
          ?.flatMap((fileField) => {
            const values = Array.isArray(fileField.value) ? fileField.value : []
            return values
              .map((item) => ({
                ...item,
                formFieldByName: fileField.name, // Add parent's name to each file item
                tag: fileField.tag, // Add parent's name to each file item
              }))
              .filter((item) => item?.file) // Keep only items with a file property
          }) || []
      // const fileValue = formattedResponses
      //   ?.filter(({ type }: { type: string }) => type === 'file')
      //   ?.flatMap((fileField: any) =>
      //     Array.isArray(fileField?.value)
      //       ? fileField.value.filter((item: any) => item?.file) // only include ones with actual files
      //       : []
      //   )

      console.log({ formattedResponses, fileValue })
      // return
      const images = fileValue?.length ? await handleFileChange(fileValue, formDataById.name) : []

      if (!images) {
        return
      }

      /**
       * Updates `formattedResponses` by ensuring that:
       * - All objects with `type === 'file'` are processed.
       * - Any objects in `value` that contain a `file` property (local files) are removed.
       * - New images from `images` are appended if available.
       * - If `images` is empty, only the filtered `value` (without local files) is retained.
       *
       * This prevents storing local `File` objects while keeping remote URLs intact.
       */

      formattedResponses = formattedResponses?.map((item: any) => {
        if (item?.type === 'file') {
          // Keep existing files that are already uploaded (non-temp)
          const existingFiles = (Array.isArray(item?.value) ? item.value : []).filter((file: any) => !file.file)

          // Find matching images based on formFieldByName and item.name
          const matchedFiles =
            images?.filter((img) => {
              const matchesName = img.formFieldByName === item.name

              const matchesMimeType = (() => {
                if (!item.accept) return true
                if (item.accept === 'video/*') return img.mimetype.startsWith('video/')
                if (item.accept === 'audio/*') return img.mimetype.startsWith('audio/')
                if (item.accept === 'image/*') return img.mimetype.startsWith('image/')
                return false
              })()

              return matchesName && matchesMimeType
            }) || []

          return {
            ...item,
            value: [...existingFiles, ...matchedFiles],
          }
        }

        return item
      })
      console.log({ values, formattedResponses, images, fileValue })

      // formattedResponses = formattedResponses?.map((item: any) =>
      //   item?.type === 'file'
      //     ? {
      //         ...item,
      //         value:
      //           images && images.length
      //             ? [...(Array.isArray(item?.value) ? item.value : []).filter((file: any) => !file.file), ...images]
      //             : (Array.isArray(item?.value) ? item.value : []).filter((file: any) => !file.file),
      //       }
      //     : item
      // )
      setProgress(40)
      if (formId) {
        const res = await deleteMediaOpportunity(imageId!)
        if (!isSuccess(res)) {
          return
        }
      }

      // await handleFileChange()

      const url = await generateAndUploadPDF(formattedResponses)
      const payload: FilePayload[] = []
      if (url) {
        payload.push({
          _id: image_Id!,
          createdAt: new Date().toISOString(),
          createdBy: currentMember?._id!,
          mimetype: 'application/pdf',
          name: formDataById.name,
          url: url?.split('?')[0],
          thumbnail: getThumbnailUrl(url?.split('?')[0]),
          tags: [stepName ? stepName : 'Forms'],
          builderFormId: builderFormId,
          formId: form_Id,
          stepId: stepName ? state?.stepId : undefined,
        })
        setProgress(80)

        const uploadRes = await createMediaOpportunity(oppId!, payload)
        if (isSuccess(uploadRes)) {
          setProgress(100)
          setIsDirty(false)

          console.log('Form Submitted', url, values, formattedResponses)

          const data = {
            oppId: oppId,
            mediaId: image_Id,
            builderFormId: builderFormId,
            name: formDataById.name,
            fields: formattedResponses,
            mediaUrl: url?.split('?')[0],
            _id: form_Id,
            locationImage: imageBase64,
            weather: weather,
            PO: oppData?.PO,
            num: oppData?.num,
          }
          console.log({ formattedResponses, values })
          // if (!formId) {
          const res = await createOpportunityForm(data)
          if (isSuccess(res)) {
            if (stepName) {
              await updateChecklist({
                key: state?.stepId,
                value: {
                  url: url?.split('?')[0],
                  formId: form_Id,
                  mediaId: image_Id,
                },

                boolean: false,
                opportunityId: oppId!,
                currDate: new Date().toISOString(),
                stage: state?.stage,
                updatedBy: currentMember._id!,
              })

              navigate(`/sales/opportunity/${oppId}/`)
              return
            }

            oppId ? navigate(`/sales/opportunity/${oppId}/media`) : navigate(`/forms`)
          }
        }
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setProgress(0)
      setLoadingSubmit(false)
    }
  }

  // const downloadPDF = async () => {
  //   let formattedResponses = formDataById.fields.map((field, index) => ({
  //     ...field,
  //     // fieldName: field.name,
  //     value: formik.initialValues[field.name] !== undefined ? formik.initialValues[field.name] : field.value || '', // Use input value or default value
  //     order: index + 1, // Maintain field order
  //   }))
  //   const fileResponse = formattedResponses?.find(({ type }: { type: string }) => type === 'file')
  //   const fileValue = Array.isArray(fileResponse?.value)
  //     ? fileResponse.value.filter(({ file }: { file: File }) => file)
  //     : []
  //   const images = []

  //   formattedResponses = formattedResponses?.map((item: any) =>
  //     item?.type === 'file'
  //       ? {
  //           ...item,
  //           value:
  //             Array.isArray(images) && images.length
  //               ? [...(Array.isArray(item?.value) ? item.value : []).filter((file: any) => !file.file), ...images]
  //               : (Array.isArray(item?.value) ? item.value : []).filter((file: any) => !file.file),
  //         }
  //       : item
  //   )
  //   const htmlContent = ReactDOMServer.renderToString(
  //     <FormPDFLayout
  //       weather={weather?.data}
  //       imageBase64={imageBase64}
  //       formattedResponses={formattedResponses}
  //       name={formDataById?.name}
  //       oppData={oppData}
  //       createdBy={currentMember?.name}
  //     />
  //   )

  //   // addManualPageBreaks()
  //   console.log('first', htmlContent, formDataById, formik.initialValues)

  //   const element = document.createElement('div')
  //   element.innerHTML = htmlContent
  //   html2pdf()
  //     .set({
  //       margin: 8.5, // Explicit margins [top, right, bottom, left]
  //       filename: 'document.pdf',
  //       image: { type: 'jpeg', quality: 1.0 },
  //       html2canvas: {
  //         scale: 2, // Reduced from 2 to prevent scaling overflow
  //         useCORS: true,
  //         imageTimeout: 0,
  //       },
  //       jsPDF: {
  //         unit: 'mm',
  //         format: 'letter',
  //         orientation: 'portrait',
  //       },
  //       pagebreak: { mode: ['avoid', 'css'] },
  //       // margin: 10,
  //       // filename: 'report.pdf',
  //       // image: { type: 'jpeg', quality: 0.98 },
  //       // html2canvas: { scale: 2, useCORS: true },
  //       // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  //       // pagebreak: { mode: ['avoid-all', 'css'] },
  //     })
  //     .from(element)
  //     .save()
  // }

  const generateAndUploadPDF = async (formattedResponses: any) => {
    try {
      // ================ OLD CODE ================

      // 1️⃣ Convert HTML to PDF Blob
      // const htmlContent = ReactDOMServer.renderToString(
      //   <FormPDFLayout
      //     weather={weather?.data}
      //     imageBase64={imageBase64}
      //     formattedResponses={formattedResponses}
      //     name={formDataById?.name}
      //     oppData={oppData}
      //     createdBy={currentMember?.name}
      //   />
      // )

      //     const element = document.createElement('div')
      //     element.innerHTML = `
      //     <style>
      //     * { word-wrap: break-word; }
      //     table, div { page-break-inside: avoid; }
      //     p { page-break-inside: avoid; word-break: break-word; }

      //     /* Ensure sections don’t split */
      //     .avoid-break {
      //       page-break-before: always;
      //       page-break-inside: avoid;
      //     }
      //   </style>
      //   ${htmlContent}
      // `

      //     element.innerHTML = htmlContent

      // const pdfBlob = await html2pdf()
      //   .set({
      //     margin: 8.5, // Explicit margins [top, right, bottom, left]
      //     filename: 'document.pdf',
      //     image: { type: 'jpeg', quality: 1.0 },
      //     html2canvas: {
      //       scale: 2, // Reduced from 2 to prevent scaling overflow
      //       useCORS: true,
      //     },
      //     jsPDF: {
      //       unit: 'mm',
      //       format: 'letter',
      //       orientation: 'portrait',
      //     },
      //     pagebreak: { mode: ['avoid', 'css'] },
      //     // margin: 10,
      //     // filename: 'report.pdf',
      //     // image: { type: 'jpeg', quality: 0.98 },
      //     // html2canvas: { scale: 2, useCORS: true },
      //     // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
      //     // pagebreak: { mode: ['avoid-all', 'css'] },
      //   })
      //   .from(htmlContent)
      //   .outputPdf('blob')

      // ================ OLD CODE ================

      // 2️⃣ Get pre-signed URL from your backend
      const fileName = `${formDataById.name}-${Date.now()}.pdf`

      const pdfBlob = await pdf(
        <NewFormPDFLayout
          weather={weather?.data}
          imageBase64={imageBase64}
          formattedResponses={formattedResponses}
          name={formDataById?.name}
          oppData={oppData}
          createdBy={currentMember?.name}
        />
      ).toBlob()
      const presignedRes = await getPresignedUrlMedia(
        oppId ? FilePathTypeEnum.Project : FilePathTypeEnum.Member,
        [{ fileName, mimetype: 'application/pdf' }],
        currentMember?._id,
        oppId!
      )

      if (!isSuccess(presignedRes)) {
        console.error('Error getting presigned URL:', presignedRes)
        return
      }

      const { url } = presignedRes.data.data?.signedUrls[0] // API returns URL & final file URL

      // 3️⃣ Upload the PDF to S3 using the pre-signed URL
      const uploadRes = await fetch(url, {
        method: 'PUT',
        body: pdfBlob,
        headers: { 'Content-Type': 'application/pdf' },
      })
      setProgress(40)
      if (!uploadRes.ok) {
        console.error('Error uploading PDF:', uploadRes)
        return
      }

      console.log('File uploaded successfully:', url)

      // 4️⃣ Pass file URL to handleFormSubmit
      return url
    } catch (error) {
      console.error('Error generating/uploading PDF:', error)
      setLoadingSubmit(false)
    }
  }

  // const formattedResponses = formDataById.fields.map((field, index) => ({
  //   ...field,
  //   // fieldName: field.name,
  //   value: formik.initialValues[field.name] !== undefined ? formik.initialValues[field.name] : field.value || '', // Use input value or default value
  //   order: index + 1, // Maintain field order
  // }))
  const { data: dataMedia, loading: loadingMedia } = useFetch({
    fetchFn: () => (oppId ? getOpportunityMedia(oppId) : getAllMedia({ limit: 100 })),
  })
  return (
    <>
      {/* {oppData?.zip && ( */}
      {/* <FormPDFLayout
        weather={weather?.data}
        imageBase64={imageBase64}
        formattedResponses={formattedResponses}
        name={formDataById?.name}
        oppData={oppData}
        createdBy={currentMember?.name}
      /> */}
      {/* )}*/}

      <div className="form-container">
        {loading ? (
          <>
            <SharedStyled.FlexCol gap="25px">
              <SLoader height={45} width={100} isPercent />
              <SLoader height={15} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={15} width={100} isPercent />
              <SLoader height={85} width={100} isPercent />
            </SharedStyled.FlexCol>
          </>
        ) : (
          <form onSubmit={formik.handleSubmit} className="form-sheet">
            {' '}
            <div className="form-content">
              <div>
                <SharedStyled.Text
                  padding="10px 0 10px 0"
                  fontSize="18px"
                  textTransform="capitalize"
                  textAlign="center"
                  fontWeight="700"
                >
                  {formDataById?.name || ''}
                </SharedStyled.Text>
              </div>
              <br />
              {formDataById.fields.map((field) => (
                <div style={{ marginBottom: '15px' }} key={field.name}>
                  <div className="form-group">
                    {!notIncludeInLabel?.includes(field.type) && <label className="form-label">{field.label}</label>}
                    <div className={`field-wrapper ${field?.type === 'header' ? 'header-field' : ''}`}>
                      {(() => {
                        switch (field.type) {
                          case 'autocomplete':
                            return (
                              <>
                                <>
                                  <input
                                    list={`datalist-${field.name}`}
                                    className="form-input"
                                    name={field.name}
                                    onChange={formik.handleChange}
                                    value={formik.values[field.name] || ''}
                                  />
                                  <datalist id={`datalist-${field.name}`}>
                                    {field.values?.map((option: any, idx: number) => (
                                      <option key={idx} value={option.label} />
                                    ))}
                                  </datalist>
                                </>
                              </>
                            )

                          case 'header':
                            const Header = field.subtype || 'h3'
                            return (
                              <SharedStyled.FlexRow justifyContent="center">
                                <Header>{field.label}</Header>
                              </SharedStyled.FlexRow>
                            )

                          case 'text':
                            return (
                              <input
                                type={field.subtype}
                                className="form-input"
                                name={field.name}
                                onChange={formik.handleChange}
                                value={formik.values[field.name] ?? ''}
                              />
                            )
                          case 'number':
                            return (
                              <input
                                type="number"
                                className="form-input"
                                name={field.name}
                                onChange={formik.handleChange}
                                value={formik.values[field.name] ?? ''}
                              />
                            )
                          case 'date': {
                            const isTimeOnly = field.subtype === 'time'
                            let defaultValue = formik.values[field.name] || ''

                            if (field.today && !isTimeOnly && !formik.values[field.name]) {
                              const now = new Date()

                              if (field.subtype === 'datetime-local') {
                                // Use extractLocalTime to get local HH:mm
                                const time = extractLocalTime(now.toISOString()) || '00:00'
                                defaultValue = `${now.toISOString().split('T')[0]}T${time}`
                              } else {
                                defaultValue = now.toISOString().split('T')[0]
                              }
                              formik.setFieldValue(field.name, defaultValue)
                            }
                            return (
                              <input
                                type={field.subtype || 'text'}
                                className="form-input"
                                name={field.name}
                                onChange={formik.handleChange}
                                value={defaultValue}
                              />
                            )
                          }

                          case 'file':
                            return (
                              <div className="file-wrap">
                                {field.accept?.includes('image') || field.accept?.includes('video') ? (
                                  <CameraFileUpload field={field} formik={formik} />
                                ) : (
                                  <></>
                                )}
                                <FileUpload field={field} formik={formik} />
                                {
                                  <OpportunityMediaModal
                                    field={field}
                                    formik={formik}
                                    oppId={oppId!}
                                    dataMedia={dataMedia}
                                    loadingMedia={loadingMedia}
                                  />
                                }
                              </div>
                            )
                          case 'checkbox-group':
                            return field.values.map((option) => (
                              <label key={option.label} className="checkbox-label">
                                <input
                                  type="checkbox"
                                  name={field.name}
                                  value={option.label}
                                  onChange={(event) => {
                                    const selected = formik.values[field.name] || []
                                    if (event.target.checked) {
                                      formik.setFieldValue(field.name, [...selected, option.label]) // ✅ Update array
                                    } else {
                                      formik.setFieldValue(
                                        field.name,
                                        selected.filter((v) => v !== option.label)
                                      )
                                    }
                                  }}
                                  checked={formik.values[field.name]?.includes(option.label)}
                                />
                                {option.label}
                              </label>
                            ))
                          case 'radio-group':
                            return field.values.map((option) => (
                              <label key={option.label} className="radio-label">
                                <input
                                  type="radio"
                                  name={field.name}
                                  value={option.label}
                                  onChange={formik.handleChange}
                                  checked={formik.values[field.name] === option.label} // ✅ Fix value binding
                                />
                                {option.label}
                              </label>
                            ))
                          case 'select':
                            return (
                              <select
                                className="form-input"
                                name={field.name}
                                onChange={formik.handleChange}
                                value={formik.values[field.name] || ''}
                              >
                                <option value="" disabled hidden>
                                  -- Select an option --
                                </option>
                                {field.values.map((option) => (
                                  <option key={option.label} value={option.label}>
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            )
                          case 'textarea':
                            return (
                              <textarea
                                className="form-input"
                                name={field.name}
                                onChange={formik.handleChange}
                                value={formik.values[field.name] || ''} // ✅ Fix value binding
                              />
                            )
                          case 'button':
                            return (
                              <button type="button" className="btn-default btn">
                                {field.label}
                              </button>
                            )
                          default:
                            return null
                        }
                      })()}
                    </div>
                  </div>
                  {formik.errors[field.name] && <div className="error">{formik.errors[field.name]}</div>}
                </div>
              ))}
              <div>
                <Button isLoading={loadingSubmit} type="submit">
                  Submit
                </Button>
              </div>
              {/* <Button type="button" onClick={downloadPDF}>
                Download PDF
              </Button> */}
            </div>
            <SharedStyled.ProgressLoader progress={progress} />
          </form>
        )}
      </div>
    </>
  )
}

export default FormFill
