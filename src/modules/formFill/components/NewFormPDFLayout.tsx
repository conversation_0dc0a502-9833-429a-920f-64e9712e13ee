import { Page, Text, View, Image, Document, StyleSheet } from '@react-pdf/renderer'
import Logo from '../../../assets/images/nhr.png'
import { generateWeatherString } from './constant'
import { dayjsFormat, formatPhoneNumber } from '../../../shared/helpers/util'

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 11,
    lineHeight: 1.4,
    padding: 20,
  },
  lightText: {
    fontSize: 11,
    lineHeight: 1.2,
  },
  flex: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    borderBottom: '1px solid #E4E7EA',
    alignItems: 'flex-end',
    paddingBottom: 2,
  },
  header: {
    maxWidth: '60%',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  logo: {
    width: 250,
    height: 60,
    objectFit: 'contain',
  },
  dualColumn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  column: {
    width: '48%',
  },
  dualColumnTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 2,
    lineHeight: 1.2,
  },
  clientGroup: {
    marginBottom: 10,
  },
  clientText: {
    fontSize: 11,
    lineHeight: 1.2,
  },
  sectionTitle: {
    textAlign: 'center',
    padding: 6,
    marginVertical: 8,
    fontWeight: 'bold',
    fontSize: 14,
    backgroundColor: '#e7e7e7',
    textTransform: 'uppercase',
  },

  h1: {
    fontSize: 18,
    padding: 10,
  },
  h2: {
    fontSize: 16,
    padding: 8,
  },
  h3: {
    fontSize: 14,
    padding: 6,
  },
  h4: {
    fontSize: 12,
  },
  h5: {
    fontSize: 10,
  },
  h6: {
    fontSize: 8,
  },

  fieldGroup: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
    // gap: 6,
    // border: '1px solid green',
  },
  fieldGroupCol: {
    marginVertical: 10,
    gap: 1,
    flexDirection: 'column',
    // border: '1px solid green',
    width: '100%',
  },

  fieldLabel: {
    fontWeight: 'bold',
    lineHeight: 1.4,
    fontSize: 11,
  },

  fieldLabelContainer: {
    width: 280,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },

  line: {
    flex: 1,
    height: 14,
    marginHorizontal: 6,
    marginBottom: 6,
    borderBottom: '1px solid #E4E7EA',
  },
  grid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    width: '100%',
  },
  fieldValue: {
    flex: 1,
    fontSize: 11,
    // height: 10,
    border: '1px solid white',
  },
  uploadedImage: {
    width: 150,
    height: 100,
    // objectFit: 'contain',
    // marginRight: 5,
    // marginBottom: 5,
  },
  fileLink: {
    fontSize: 11,
    color: 'blue',
    textDecoration: 'underline',
  },
})

const NewFormPDFLayout = ({
  weather,
  imageBase64,
  formattedResponses,
  name,
  oppData,
  createdBy,
}: {
  weather: any
  imageBase64: any
  formattedResponses: any[]
  name: string
  createdBy: string
  oppData: any
}) => {
  return (
    <Document>
      <Page size="LETTER" style={styles.page}>
        {/* Header */}
        <View style={styles.flex}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>{name}</Text>

            <Text style={[styles.lightText, { marginTop: 4 }]}>
              Completed: {dayjsFormat(new Date(), 'MMM D, YYYY h:mm A')}
            </Text>
            <Text style={styles.lightText}>Prepared by: {createdBy}</Text>
          </View>
          <Image src={Logo} style={styles.logo} />
        </View>

        {/* Client + Project Address */}
        {oppData && (
          <View style={styles.dualColumn}>
            <View style={styles.column}>
              <Text style={styles.dualColumnTitle}>Client:</Text>
              <View style={styles.clientGroup}>
                <Text style={styles.clientText}>{oppData?.contact?.fullName}</Text>
                <Text style={styles.clientText}>{formatPhoneNumber(oppData?.contact?.phone, '')}</Text>
              </View>
            </View>
            <View style={styles.column}>
              <Text style={styles.dualColumnTitle}>Project Address:</Text>
              <View style={styles.clientGroup}>
                <Text style={styles.clientText}>{oppData?.street}</Text>
                <Text style={styles.clientText}>
                  {oppData?.city}, {oppData?.state} {oppData?.zip}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Dynamic Fields */}
        {formattedResponses
          ?.sort((a: any, b: any) => a?.order - b?.order)
          ?.map((field: any, index: number) => {
            switch (field.type) {
              case 'header':
                return (
                  <Text key={index} style={[styles.sectionTitle, styles?.[field?.subtype as keyof typeof styles]]}>
                    {field.label}
                  </Text>
                )

              case 'paragraph':
                return (
                  <Text key={index} style={styles.fieldValue}>
                    {field.label}
                  </Text>
                )

              case 'date': {
                let displayDate = 'N/A'
                if (field.value) {
                  const rawValue = field.value

                  switch (field.subtype) {
                    case 'datetime-local': {
                      const date = new Date(rawValue)
                      displayDate = date.toLocaleString(undefined, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true,
                      })
                      break
                    }

                    case 'time': {
                      const [hours, minutes] = rawValue.split(':')
                      const date = new Date()
                      date.setHours(+hours)
                      date.setMinutes(+minutes)
                      displayDate = date.toLocaleTimeString(undefined, {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true,
                      })
                      break
                    }

                    case 'date':
                    default: {
                      const date = new Date(rawValue)
                      displayDate = date.toLocaleDateString(undefined, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })
                      break
                    }
                  }
                }
                return (
                  <View key={index} style={styles.fieldGroup}>
                    <View style={styles.fieldLabelContainer}>
                      <Text style={styles.fieldLabel}>{field.label}</Text>
                      <View style={styles.line} />
                    </View>
                    <Text style={styles.fieldValue}>{displayDate}</Text>
                  </View>
                )
              }

              case 'checkbox-group':
                return (
                  <View key={index} style={styles.fieldGroup}>
                    <View style={styles.fieldLabelContainer}>
                      <Text style={styles.fieldLabel}>{field.label}</Text>
                      <View style={styles.line} />
                    </View>

                    <View>
                      {field.value?.map((v: string, idx: number) => (
                        <Text key={idx}>{v}</Text>
                      ))}
                    </View>
                  </View>
                )

              case 'weather':
                return (
                  <View style={styles.fieldGroupCol} key={index}>
                    <Text style={styles.fieldLabel}>Weather</Text>
                    <Text style={styles.fieldValue}>{generateWeatherString(weather, field?.value)}</Text>
                  </View>
                )

              case 'location':
                return (
                  <View key={index} style={styles.fieldGroupCol}>
                    <Text style={styles.fieldLabel}>Location</Text>
                    {imageBase64 ? (
                      <Image src={imageBase64} style={styles.uploadedImage} />
                    ) : (
                      <Text style={styles.fieldValue}>{field.value}</Text>
                    )}
                  </View>
                )

              case 'file': {
                const images = field.value?.filter((f: any) => f.mimetype?.startsWith('image'))
                const others = field.value?.filter(
                  (f: any) => f.mimetype?.startsWith('audio') || f.mimetype?.startsWith('video')
                )
                return (
                  <View key={index} style={styles.fieldGroupCol}>
                    <Text style={styles.fieldLabel}>{field.label}</Text>
                    <View style={styles.grid}>
                      {images?.map((file: any, i: number) => (
                        <Image key={i} src={file.url} style={styles.uploadedImage} />
                      ))}
                      {others?.map((file: any, i: number) => (
                        <Text key={i} style={styles.fileLink}>
                          {file.url}
                        </Text>
                      ))}
                    </View>
                  </View>
                )
              }

              default:
                let fieldValue
                // Accept 0 values for numeric fields and render them instead of 'N/A'
                if (field?.type === 'number' || field?.subtype === 'number') {
                  fieldValue = field?.value === 0 || field?.value === '0' ? '0' : field?.value ?? 'N/A'
                } else {
                  fieldValue = field?.value ? field?.value : 'N/A'
                }
                return (
                  <View key={index} style={styles.fieldGroup}>
                    <View style={styles.fieldLabelContainer}>
                      <Text style={styles.fieldLabel}>{field.label}</Text>
                      <View style={styles.line} />
                    </View>
                    <Text style={styles.fieldValue}>
                      {fieldValue === 0 || fieldValue === '0' ? '0' : fieldValue || '--'}
                    </Text>
                  </View>
                )
            }
          })}
      </Page>
    </Document>
  )
}

export default NewFormPDFLayout
