import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import * as Yup from 'yup'

import { getProfileInfo, updateProfile } from '../../logic/apis/profile'
import { CustomModal } from '../../shared/customModal/CustomModal'
import {
  allowedImageTypes,
  FilePathTypeEnum,
  MAX_IMAGE_HEIGHT,
  MAX_IMAGE_SIZE,
  MAX_IMAGE_WIDTH,
  StorageKey,
  TIME_ZONES,
} from '../../shared/helpers/constants'
import { onlyText, userName } from '../../shared/helpers/regex'
import { getDataFromLocalStorage, getInitials, isSuccess, notify, uuidToColor } from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import { ChangePassword } from './components/changePassword/ChangePassword'
import { SelectCompany } from './components/SelectCompany/SelectCompany'
import * as Styled from './style'
import Button from '../../shared/components/button/Button'
import { AvatarSvg } from '../../shared/helpers/images'
import { SLoader } from '../../shared/components/loader/Loader'
import UploadSvg from '../../assets/newIcons/upload.svg'
import Toggle from '../../shared/toggle/Toggle'
import useImageUpload from '../../shared/hooks/useImageUpload'
import { useAppDispatch } from '../../logic/redux/reduxHook'
import { setTriggerRetch } from '../../logic/redux/actions/ui'
import { Info, NameCont } from '../../shared/components/profileInfo/style'
import { deleteImageFromS3 } from '../../logic/apis/media'
import { ProfileResponse } from './Profile.test'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName?: string
  lastName?: string
  preferredName?: string
  // username: string
  email?: string
  roles?: string
  mobileAccess?: boolean
}

/**
 *
 * @returns A Profile component with all the validations to its input fields
 */
const Profile = () => {
  const { uploadImage, error, imageUrl } = useImageUpload()
  const [imgLoading, setImgLoading] = useState(false)
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    preferredName: '',
    // username: '',
    email: '',
    roles: '',
    mobileAccess: false,
  })

  const [isEdit, setIsEdit] = useState(false)

  /**
   * This showChangePasswordModal is a boolean state which will be used to change the state of the modal
   */
  const [showChangePasswordModal, setShowChangePasswordModal] = useState<boolean>(false)

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [fetchLoading, setFetchLoading] = useState(false)
  const [timeZ, setTimeZ] = useState<string>(TIME_ZONES[0])

  const dispatch = useAppDispatch()

  const navigate = useNavigate()
  const uploadRef = useRef<any>(null)

  const globalSelector = useSelector((state: any) => state)
  const { profileInfo } = globalSelector.auth
  const { companies, currentCompany, position, triggerRefetch } = globalSelector.company
  const [avatar, setAvatar] = useState<any>()
  const userId = getDataFromLocalStorage(StorageKey.id)

  useEffect(() => {
    if (profileInfo?.imageUrl) {
      setAvatar(profileInfo?.imageUrl)
    }
  }, [profileInfo?.imageUrl])

  /**
   * ProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ProfileSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      // .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    preferredName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // username: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(userName, 'Enter Valid Username'),
    email: Yup.string().email('Invalid email').required('Required'),
    roles: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    console.log({ submittedValues })
    setLoading(true)
    try {
      let data: any = { ...submittedValues }
      delete data?.roles
      const response = await updateProfile(data, userId)
      if (isSuccess(response)) {
        notify('Updated Profile Successfully', 'success')
        setLoading(false)
        setIsEdit(false)
        getDetails()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('Profile handleSubmit', error)
      setLoading(false)
    }
  }

  const getDetails = async () => {
    try {
      setFetchLoading(true)
      const response = await getProfileInfo()
      if (isSuccess(response)) {
        let user = response?.data?.data?.user as ProfileResponse
        let userObject = {
          firstName: user.firstName,
          lastName: user.lastName,
          preferredName: user.preferredName,
          mobileAccess: user?.mobileAccess ?? false,
          // username: user.username,
          email: user.email,
          roles: position,
        }
        setInitialValues({ ...initialValues, ...userObject })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setFetchLoading(false)
    }
  }

  useEffect(() => {
    getDetails()
  }, [])

  const handleAvatarChange = (e: any) => {
    const file = e.target.files[0]

    if (file?.size > MAX_IMAGE_SIZE) {
      notify('File size exceeds the 5MB limit', 'error')
      return
    }

    if (!allowedImageTypes.includes(file?.type)) {
      notify('Only JPEG and PNG file types are allowed', 'error')
      return
    }

    // const img = new Image()
    // img.src = URL.createObjectURL(file)

    // img.onload = () => {
    //   const { width, height } = img
    //   // Image Resolution Validation
    //   if (width > MAX_IMAGE_WIDTH || height > MAX_IMAGE_HEIGHT) {
    //     notify(`Image resolution exceeds the allowed ${MAX_IMAGE_WIDTH}x${MAX_IMAGE_HEIGHT} pixels`, 'error')
    //     return
    //   } else {
    //     if (file) {
    //       setAvatar(URL.createObjectURL(file))
    //       setImgLoading(true)
    //       uploadImage(file, FilePathTypeEnum.Profile)
    //     }
    //   }
    // }

    if (file) {
      setAvatar(URL.createObjectURL(file))
      setImgLoading(true)
      uploadImage(file, FilePathTypeEnum.Profile)
    }
  }

  useEffect(() => {
    if (imageUrl) {
      ;(async () => {
        try {
          const response = await updateProfile(
            {
              imageUrl,
            },
            userId
          )
          if (isSuccess(response)) {
            setImgLoading(false)
            notify('Updated Profile Image', 'success')

            profileInfo?.imageUrl && deleteImageFromS3(profileInfo?.imageUrl)

            dispatch(setTriggerRetch(!triggerRefetch))
            setIsEdit(false)
          }
        } catch (error) {}
      })()
    }
  }, [imageUrl])

  return (
    <>
      <Styled.ProfileCont isEdit={isEdit}>
        <SharedStyled.FlexCol gap="36px">
          <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
            <SharedStyled.SectionTitle>{isEdit ? 'Edit' : 'My'} Profile</SharedStyled.SectionTitle>
            <Button
              className="fit"
              onClick={() => {
                setIsEdit((prev) => !prev)
              }}
            >
              {isEdit ? 'Cancel Edit' : 'Edit Profile'}
            </Button>
          </SharedStyled.FlexRow>
          <SharedStyled.FlexRow gap="36px" alignItems="flex-start" className="profileCont">
            <div className={`avatar-wrapper ${imgLoading ? 'loading' : ''}`}>
              {avatar ? (
                <img
                  src={avatar ?? AvatarSvg}
                  alt="profile picture"
                  className="profile loading"
                  // onClick={() => {
                  //   uploadRef.current?.click()
                  // }}
                />
              ) : (
                <NameCont
                  bg={profileInfo?.id ? uuidToColor(profileInfo?.id!) : '#efefef'}
                  className={isEdit ? 'profile-edit' : 'profile-name'}
                >
                  {profileInfo?.firstName ? (
                    <Info>{getInitials(`${profileInfo?.firstName} ${profileInfo?.lastName}`)}</Info>
                  ) : null}
                </NameCont>
              )}
            </div>

            {isEdit ? (
              <>
                <Styled.UploadCont gap="24px">
                  <SharedStyled.FlexCol>
                    <h5>Upload a profile image</h5>
                    <p>Recommended size: 512x512px. JPG, PNG.</p>
                  </SharedStyled.FlexCol>

                  <SharedStyled.FlexCol gap="12px">
                    <SharedStyled.FlexRow width="100%" justifyContent="space-between">
                      <h6>Profile image</h6>
                      <p>Optional</p>
                    </SharedStyled.FlexRow>

                    <Styled.UploadFileCont
                      onClick={() => {
                        uploadRef.current?.click()
                      }}
                    >
                      <input type="file" ref={uploadRef} onChange={handleAvatarChange} accept="image/*" />
                      <SharedStyled.FlexRow gap="16px" flexWrap="wrap">
                        <Styled.ImgCont>
                          <img src={UploadSvg} alt="upload icon" />
                        </Styled.ImgCont>

                        <SharedStyled.FlexCol width="max-content">
                          <h6>Select media to upload</h6>
                          <p>File types: JPG, PNG.</p>
                        </SharedStyled.FlexCol>
                      </SharedStyled.FlexRow>
                    </Styled.UploadFileCont>
                  </SharedStyled.FlexCol>
                </Styled.UploadCont>
              </>
            ) : (
              <Styled.InfoCont>
                {fetchLoading ? (
                  <SLoader width={100} />
                ) : (
                  <h6>
                    {initialValues?.firstName} {initialValues?.lastName}
                  </h6>
                )}
                <SharedStyled.FlexCol gap="2px">
                  <SharedStyled.FlexCol alignItems="center">
                    {fetchLoading ? <SLoader width={100} height={20} /> : <p>{position}</p>}
                    {fetchLoading ? <SLoader width={100} height={10} /> : <p>{initialValues?.email}</p>}
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexCol>
              </Styled.InfoCont>
            )}
          </SharedStyled.FlexRow>
        </SharedStyled.FlexCol>

        <SharedStyled.HorizontalDivider />

        {!isEdit && (
          <SharedStyled.FlexCol gap="24px">
            {fetchLoading ? (
              <SLoader width={100} height={20} />
            ) : (
              <h6>
                <Toggle
                  title="Mobile Access: "
                  // customStyles={{ margin: '16px' }}
                  isToggled={initialValues?.mobileAccess ?? false}
                  onToggle={() => {
                    handleSubmit({ mobileAccess: !initialValues?.mobileAccess })
                    setInitialValues((prev) => ({
                      ...prev,
                      mobileAccess: !initialValues?.mobileAccess,
                    }))
                  }}
                />
              </h6>
            )}
            <h6>Personal Information</h6>
            <SharedStyled.FlexRow>
              <SharedStyled.FlexCol gap="8px">
                <h6>First Name</h6>
                {fetchLoading ? <SLoader width={100} /> : <p>{initialValues?.firstName}</p>}
              </SharedStyled.FlexCol>
              <SharedStyled.FlexCol gap="8px">
                <h6>Last Name</h6>
                {fetchLoading ? <SLoader width={100} /> : <p>{initialValues?.lastName}</p>}
              </SharedStyled.FlexCol>
            </SharedStyled.FlexRow>

            {/* <SharedStyled.FlexCol gap="8px">
              <h6>Username</h6>
              {fetchLoading ? <SLoader width={100} /> : <p>{initialValues?.username}</p>}
            </SharedStyled.FlexCol> */}
            <SharedStyled.FlexCol gap="8px">
              <h6>Company</h6>
              {fetchLoading ? <SLoader width={100} /> : <p>{currentCompany?.companyName}</p>}
            </SharedStyled.FlexCol>
          </SharedStyled.FlexCol>
        )}

        {isEdit && (
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={ProfileSchema}
            enableReinitialize={true}
            validateOnChange={true}
            validateOnBlur={false}
          >
            {({ errors, touched }) => {
              return (
                <Styled.ProfileContainer>
                  {/* <SharedStyled.ContentContainer> */}
                  <Form className="form">
                    <SharedStyled.Content
                      maxWidth="1280px"
                      width="100%"
                      disableBoxShadow={true}
                      noPadding={true}
                      gap="16px"
                    >
                      <SharedStyled.TwoInputDiv>
                        <InputWithValidation
                          labelName="First Name"
                          stateName="firstName"
                          error={touched.firstName && errors.firstName ? true : false}
                          twoInput={true}
                        />
                        <InputWithValidation
                          labelName="Last Name"
                          stateName="lastName"
                          error={touched.lastName && errors.lastName ? true : false}
                          twoInput={true}
                        />
                      </SharedStyled.TwoInputDiv>
                      <InputWithValidation
                        labelName="Preferred Name"
                        stateName="preferredName"
                        error={touched.preferredName && errors.preferredName ? true : false}
                      />
                      {/* <InputWithValidation
                        labelName="Username"
                        stateName="username"
                        error={touched.username && errors.username ? true : false}
                      /> */}
                      {/* <InputWithValidation
                        disabled={true}
                        labelName="Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                      <InputWithValidation
                        disabled={true}
                        labelName="Roles"
                        stateName="roles"
                        error={touched.roles && errors.roles ? true : false}
                      /> */}
                      <SelectCompany companies={companies} />
                      <SharedStyled.FlexRow gap="16px" margin="20px 0 0 0" flexWrap="wrap">
                        <Button className="fit" type="submit" isLoading={loading}>
                          Update Profile
                        </Button>
                        <Button className="fit outline" type="button" onClick={() => setShowChangePasswordModal(true)}>
                          Change Password
                        </Button>
                      </SharedStyled.FlexRow>
                    </SharedStyled.Content>
                  </Form>
                  {/* </SharedStyled.ContentContainer> */}
                </Styled.ProfileContainer>
              )
            }}
          </Formik>
        )}
        <CustomModal show={showChangePasswordModal}>
          <ChangePassword setShowChangePasswordModal={setShowChangePasswordModal} />
        </CustomModal>
      </Styled.ProfileCont>
    </>
  )
}

export default Profile
