import { act, render } from '@testing-library/react'
import * as Yup from 'yup'
import configureMockStore from 'redux-mock-store'
import { MemoryRouter } from 'react-router-dom'
import { Provider } from 'react-redux'

import { store } from '../../logic/redux/store'
import { getProfileInfo } from '../../logic/apis/profile'
import Profile from './Profile'

const getProfileSchema = Yup.object({
  id: Yup.string().uuid(),
  firstName: Yup.string(),
  lastName: Yup.string(),
  email: Yup.string().email(),
  preferredName: Yup.string(),
  member: Yup.array().of(Yup.string().uuid()),
  phone: Yup.string().nullable(),
  mobileAccess: Yup.boolean(),
  imageUrl: Yup.string().url(),
})

export type ProfileResponse = Yup.InferType<typeof getProfileSchema>
const mockStore = configureMockStore()

beforeAll(() => {
  require('dotenv').config()
  const token = process.env.TESTING_API_KEY
  localStorage.setItem('token', JSON.stringify(token))
})

describe('Profile get API', () => {
  it('ensures API response matches schema', async () => {
    const realStore = store.getState()
    const tempStore = mockStore(realStore)

    render(
      <Provider store={tempStore}>
        <MemoryRouter>
          <Profile />
        </MemoryRouter>
      </Provider>
    )

    await act(async () => {
      const response = await getProfileInfo()

      await expect(getProfileSchema.validate(response?.data?.data?.user)).resolves.toBeTruthy()
    })
  })
})
